// Native Bridge Services
export { default as NativeBridgeManager } from './NativeBridgeManager';
export { default as iOSMultipeerConnectivity } from './iOSMultipeerConnectivity';
export { default as AndroidVPNService } from './AndroidVPNService';

// Type exports
export type { 
  NativeBridgeConfig, 
  PlatformCapabilities, 
  UnifiedNetworkStats 
} from './NativeBridgeManager';

export type { 
  MultipeerPeer, 
  MultipeerSessionInfo, 
  MultipeerDataEvent, 
  MultipeerConnectionEvent 
} from './iOSMultipeerConnectivity';

export type { 
  VPNStatus, 
  NetworkInterface, 
  SystemNetworkInfo, 
  GamingPacketInfo 
} from './AndroidVPNService';
