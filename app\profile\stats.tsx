import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';

const { width } = Dimensions.get('window');

export default function StatsScreen() {
  const userStats = useSelector((state: RootState) => state.user.stats);
  const userProfile = useSelector((state: RootState) => state.user.profile);

  const formatTime = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const StatCard = ({ title, value, subtitle, icon, color = '#00D4FF' }: any) => (
    <View style={styles.statCard}>
      <BlurView intensity={15} style={styles.statBlur}>
        <View style={[styles.statIcon, { backgroundColor: color }]}>
          <Ionicons name={icon} size={24} color="#000" />
        </View>
        <View style={styles.statContent}>
          <Text style={styles.statValue}>{value}</Text>
          <Text style={styles.statTitle}>{title}</Text>
          {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
        </View>
      </BlurView>
    </View>
  );

  const SectionCard = ({ title, children }: any) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <BlurView intensity={15} style={styles.sectionCard}>
        {children}
      </BlurView>
    </View>
  );

  const StatRow = ({ label, value, icon }: any) => (
    <View style={styles.statRow}>
      <View style={styles.statRowLeft}>
        <Ionicons name={icon} size={20} color="#00D4FF" />
        <Text style={styles.statRowLabel}>{label}</Text>
      </View>
      <Text style={styles.statRowValue}>{value}</Text>
    </View>
  );

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Overview Stats */}
          <View style={styles.overviewGrid}>
            <StatCard
              title="Level"
              value={userProfile?.level || 1}
              subtitle={`${userProfile?.totalPoints || 0} points`}
              icon="trophy"
              color="#FFD700"
            />
            <StatCard
              title="Win Rate"
              value={`${Math.round(userStats?.winRate || 0)}%`}
              subtitle={`${userStats?.gamesWon || 0} wins`}
              icon="checkmark-circle"
              color="#00FF88"
            />
            <StatCard
              title="Games Played"
              value={userStats?.gamesPlayed || 0}
              subtitle={`${userStats?.gamesLost || 0} losses`}
              icon="game-controller"
              color="#9D4EDD"
            />
            <StatCard
              title="Total Playtime"
              value={formatTime(userStats?.totalPlaytime || 0)}
              subtitle="All sessions"
              icon="time"
              color="#00D4FF"
            />
          </View>

          {/* Gaming Statistics */}
          <SectionCard title="Gaming Statistics">
            <StatRow
              label="Games Played"
              value={userStats?.gamesPlayed || 0}
              icon="game-controller"
            />
            <StatRow
              label="Games Won"
              value={userStats?.gamesWon || 0}
              icon="trophy"
            />
            <StatRow
              label="Games Lost"
              value={userStats?.gamesLost || 0}
              icon="close-circle"
            />
            <StatRow
              label="Win Rate"
              value={`${Math.round(userStats?.winRate || 0)}%`}
              icon="trending-up"
            />
            <StatRow
              label="Longest Session"
              value={formatTime(userStats?.longestSession || 0)}
              icon="stopwatch"
            />
            <StatRow
              label="Average Session"
              value={formatTime(userStats?.averageSessionLength || 0)}
              icon="analytics"
            />
            {userStats?.favoriteGame && (
              <StatRow
                label="Favorite Game"
                value={userStats.favoriteGame}
                icon="heart"
              />
            )}
          </SectionCard>

          {/* Connection Statistics */}
          <SectionCard title="Connection Statistics">
            <StatRow
              label="Total Connections"
              value={userStats?.totalConnections || 0}
              icon="link"
            />
            <StatRow
              label="Game Sessions"
              value={userStats?.totalGameSessions || 0}
              icon="people"
            />
            <StatRow
              label="Sessions Hosted"
              value={userStats?.sessionsHosted || 0}
              icon="person-add"
            />
            <StatRow
              label="Sessions Joined"
              value={userStats?.sessionsJoined || 0}
              icon="enter"
            />
            <StatRow
              label="Devices Connected"
              value={userStats?.devicesConnected?.length || 0}
              icon="phone-portrait"
            />
            {userStats?.firstConnectionDate && (
              <StatRow
                label="First Connection"
                value={formatDate(userStats.firstConnectionDate)}
                icon="calendar"
              />
            )}
            {userStats?.lastConnectionDate && (
              <StatRow
                label="Last Connection"
                value={formatDate(userStats.lastConnectionDate)}
                icon="time"
              />
            )}
          </SectionCard>

          {/* Social Statistics */}
          <SectionCard title="Social Statistics">
            <StatRow
              label="Achievements Unlocked"
              value={userStats?.achievementsUnlocked || 0}
              icon="medal"
            />
            <StatRow
              label="Total Points"
              value={userProfile?.totalPoints || 0}
              icon="star"
            />
            <StatRow
              label="Current Level"
              value={userProfile?.level || 1}
              icon="trending-up"
            />
            <StatRow
              label="Friends"
              value={userProfile?.friendCount || 0}
              icon="people"
            />
            <StatRow
              label="Groups"
              value={userProfile?.groupCount || 0}
              icon="people-circle"
            />
            {userProfile?.title && (
              <StatRow
                label="Current Title"
                value={userProfile.title}
                icon="ribbon"
              />
            )}
          </SectionCard>

          {/* Progress Indicators */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Progress to Next Level</Text>
            <BlurView intensity={15} style={styles.progressCard}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressLevel}>Level {userProfile?.level || 1}</Text>
                <Text style={styles.progressPoints}>
                  {userProfile?.totalPoints || 0} / {((userProfile?.level || 1) * 1000)} points
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { 
                      width: `${Math.min(
                        ((userProfile?.totalPoints || 0) % 1000) / 10, 
                        100
                      )}%` 
                    }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {1000 - ((userProfile?.totalPoints || 0) % 1000)} points to next level
              </Text>
            </BlurView>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 100,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    width: (width - 60) / 2,
    marginBottom: 12,
  },
  statBlur: {
    borderRadius: 16,
    padding: 16,
    overflow: 'hidden',
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statContent: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    color: '#CCCCCC',
    textAlign: 'center',
  },
  statSubtitle: {
    fontSize: 12,
    color: '#888888',
    marginTop: 2,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  sectionCard: {
    borderRadius: 16,
    padding: 20,
    overflow: 'hidden',
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statRowLabel: {
    fontSize: 16,
    color: '#CCCCCC',
    marginLeft: 12,
  },
  statRowValue: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  progressCard: {
    borderRadius: 16,
    padding: 20,
    overflow: 'hidden',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressLevel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  progressPoints: {
    fontSize: 14,
    color: '#00D4FF',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#00D4FF',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#CCCCCC',
    textAlign: 'center',
  },
});
