import { BluetoothModule } from 'react-native-bluetooth-classic';
import { Platform, PermissionsAndroid } from 'react-native';

export interface BluetoothDevice {
  id: string;
  name: string;
  address: string;
  paired: boolean;
  connected: boolean;
  rssi?: number;
}

export interface BluetoothServiceInterface {
  isEnabled(): Promise<boolean>;
  requestEnable(): Promise<boolean>;
  requestPermissions(): Promise<boolean>;
  startDiscovery(): Promise<void>;
  stopDiscovery(): Promise<void>;
  getBondedDevices(): Promise<BluetoothDevice[]>;
  getDiscoveredDevices(): Promise<BluetoothDevice[]>;
  connectToDevice(deviceId: string): Promise<boolean>;
  disconnectFromDevice(deviceId: string): Promise<boolean>;
  sendData(deviceId: string, data: string): Promise<boolean>;
  onDeviceDiscovered(callback: (device: BluetoothDevice) => void): void;
  onDeviceConnected(callback: (device: BluetoothDevice) => void): void;
  onDeviceDisconnected(callback: (device: BluetoothDevice) => void): void;
  onDataReceived(callback: (deviceId: string, data: string) => void): void;
}

class BluetoothService implements BluetoothServiceInterface {
  private discoveredDevices: Map<string, BluetoothDevice> = new Map();
  private connectedDevices: Map<string, BluetoothDevice> = new Map();
  private isDiscovering = false;
  
  // Event callbacks
  private deviceDiscoveredCallback?: (device: BluetoothDevice) => void;
  private deviceConnectedCallback?: (device: BluetoothDevice) => void;
  private deviceDisconnectedCallback?: (device: BluetoothDevice) => void;
  private dataReceivedCallback?: (deviceId: string, data: string) => void;

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Set up Bluetooth event listeners
    BluetoothModule.onBluetoothEnabled(() => {
      console.log('Bluetooth enabled');
    });

    BluetoothModule.onBluetoothDisabled(() => {
      console.log('Bluetooth disabled');
      this.connectedDevices.clear();
    });

    BluetoothModule.onDeviceDiscovered((device: any) => {
      const bluetoothDevice: BluetoothDevice = {
        id: device.id,
        name: device.name || 'Unknown Device',
        address: device.address,
        paired: device.bonded || false,
        connected: false,
        rssi: device.rssi,
      };
      
      this.discoveredDevices.set(device.id, bluetoothDevice);
      this.deviceDiscoveredCallback?.(bluetoothDevice);
    });

    BluetoothModule.onDeviceConnected((device: any) => {
      const bluetoothDevice: BluetoothDevice = {
        id: device.id,
        name: device.name || 'Unknown Device',
        address: device.address,
        paired: device.bonded || false,
        connected: true,
      };
      
      this.connectedDevices.set(device.id, bluetoothDevice);
      this.deviceConnectedCallback?.(bluetoothDevice);
    });

    BluetoothModule.onDeviceDisconnected((device: any) => {
      const bluetoothDevice = this.connectedDevices.get(device.id);
      if (bluetoothDevice) {
        bluetoothDevice.connected = false;
        this.connectedDevices.delete(device.id);
        this.deviceDisconnectedCallback?.(bluetoothDevice);
      }
    });

    BluetoothModule.onDataReceived((data: any) => {
      this.dataReceivedCallback?.(data.device.id, data.data);
    });
  }

  async isEnabled(): Promise<boolean> {
    try {
      return await BluetoothModule.isEnabled();
    } catch (error) {
      console.error('Error checking Bluetooth status:', error);
      return false;
    }
  }

  async requestEnable(): Promise<boolean> {
    try {
      return await BluetoothModule.requestEnable();
    } catch (error) {
      console.error('Error enabling Bluetooth:', error);
      return false;
    }
  }

  async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.BLUETOOTH,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ];

        // For Android 12+ (API 31+)
        if (Platform.Version >= 31) {
          permissions.push(
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADVERTISE
          );
        }

        const granted = await PermissionsAndroid.requestMultiple(permissions);
        
        return Object.values(granted).every(
          permission => permission === PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (error) {
        console.error('Error requesting Bluetooth permissions:', error);
        return false;
      }
    }
    return true; // iOS handles permissions automatically
  }

  async startDiscovery(): Promise<void> {
    try {
      if (this.isDiscovering) {
        await this.stopDiscovery();
      }
      
      this.discoveredDevices.clear();
      await BluetoothModule.startDiscovery();
      this.isDiscovering = true;
    } catch (error) {
      console.error('Error starting Bluetooth discovery:', error);
      throw error;
    }
  }

  async stopDiscovery(): Promise<void> {
    try {
      if (this.isDiscovering) {
        await BluetoothModule.cancelDiscovery();
        this.isDiscovering = false;
      }
    } catch (error) {
      console.error('Error stopping Bluetooth discovery:', error);
      throw error;
    }
  }

  async getBondedDevices(): Promise<BluetoothDevice[]> {
    try {
      const bondedDevices = await BluetoothModule.getBondedDevices();
      return bondedDevices.map((device: any) => ({
        id: device.id,
        name: device.name || 'Unknown Device',
        address: device.address,
        paired: true,
        connected: this.connectedDevices.has(device.id),
      }));
    } catch (error) {
      console.error('Error getting bonded devices:', error);
      return [];
    }
  }

  async getDiscoveredDevices(): Promise<BluetoothDevice[]> {
    return Array.from(this.discoveredDevices.values());
  }

  async connectToDevice(deviceId: string): Promise<boolean> {
    try {
      const success = await BluetoothModule.connectToDevice(deviceId);
      return success;
    } catch (error) {
      console.error('Error connecting to device:', error);
      return false;
    }
  }

  async disconnectFromDevice(deviceId: string): Promise<boolean> {
    try {
      const success = await BluetoothModule.disconnectFromDevice(deviceId);
      if (success) {
        this.connectedDevices.delete(deviceId);
      }
      return success;
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      return false;
    }
  }

  async sendData(deviceId: string, data: string): Promise<boolean> {
    try {
      const success = await BluetoothModule.writeToDevice(deviceId, data);
      return success;
    } catch (error) {
      console.error('Error sending data to device:', error);
      return false;
    }
  }

  onDeviceDiscovered(callback: (device: BluetoothDevice) => void): void {
    this.deviceDiscoveredCallback = callback;
  }

  onDeviceConnected(callback: (device: BluetoothDevice) => void): void {
    this.deviceConnectedCallback = callback;
  }

  onDeviceDisconnected(callback: (device: BluetoothDevice) => void): void {
    this.deviceDisconnectedCallback = callback;
  }

  onDataReceived(callback: (deviceId: string, data: string) => void): void {
    this.dataReceivedCallback = callback;
  }
}

export default new BluetoothService();
