import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { Achievement } from '../../services/achievements/AchievementManager';

interface AchievementBadgeProps {
  achievement: Achievement;
  size?: 'small' | 'medium' | 'large';
  onPress?: () => void;
  showProgress?: boolean;
}

export default function AchievementBadge({
  achievement,
  size = 'medium',
  onPress,
  showProgress = false
}: AchievementBadgeProps) {
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#CCCCCC';
      case 'uncommon': return '#00FF88';
      case 'rare': return '#00D4FF';
      case 'epic': return '#9D4EDD';
      case 'legendary': return '#FFD700';
      default: return '#CCCCCC';
    }
  };

  const getRarityGradient = (rarity: string) => {
    switch (rarity) {
      case 'common': return ['#CCCCCC', '#999999'];
      case 'uncommon': return ['#00FF88', '#00CC6A'];
      case 'rare': return ['#00D4FF', '#0099CC'];
      case 'epic': return ['#9D4EDD', '#7B2CBF'];
      case 'legendary': return ['#FFD700', '#FFA500'];
      default: return ['#CCCCCC', '#999999'];
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          container: styles.smallContainer,
          icon: 16,
          name: styles.smallName,
          points: styles.smallPoints
        };
      case 'large':
        return {
          container: styles.largeContainer,
          icon: 32,
          name: styles.largeName,
          points: styles.largePoints
        };
      default:
        return {
          container: styles.mediumContainer,
          icon: 24,
          name: styles.mediumName,
          points: styles.mediumPoints
        };
    }
  };

  const sizeStyles = getSizeStyles();
  const isUnlocked = achievement.isUnlocked;
  const isHidden = achievement.isHidden && !isUnlocked;

  const BadgeContent = () => (
    <View style={[styles.container, sizeStyles.container]}>
      <LinearGradient
        colors={isUnlocked ? getRarityGradient(achievement.rarity) : ['#333333', '#222222']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <BlurView intensity={10} style={styles.content}>
          {/* Icon */}
          <View style={[
            styles.iconContainer,
            { backgroundColor: isUnlocked ? getRarityColor(achievement.rarity) : 'rgba(255,255,255,0.1)' }
          ]}>
            <Ionicons 
              name={isHidden ? 'help' : achievement.icon as any} 
              size={sizeStyles.icon} 
              color={isUnlocked ? '#000' : '#666'} 
            />
          </View>

          {/* Name */}
          <Text style={[
            sizeStyles.name,
            !isUnlocked && styles.lockedText
          ]}>
            {isHidden ? '???' : achievement.name}
          </Text>

          {/* Points */}
          <Text style={[
            sizeStyles.points,
            !isUnlocked && styles.lockedPoints
          ]}>
            +{achievement.reward.points}
          </Text>

          {/* Progress Bar */}
          {showProgress && !isUnlocked && achievement.maxProgress > 1 && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { 
                      width: `${(achievement.progress / achievement.maxProgress) * 100}%`,
                      backgroundColor: getRarityColor(achievement.rarity)
                    }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {achievement.progress}/{achievement.maxProgress}
              </Text>
            </View>
          )}

          {/* Unlocked Indicator */}
          {isUnlocked && (
            <View style={styles.unlockedIndicator}>
              <Ionicons name="checkmark-circle" size={12} color="#00FF88" />
            </View>
          )}
        </BlurView>
      </LinearGradient>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        <BadgeContent />
      </TouchableOpacity>
    );
  }

  return <BadgeContent />;
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  smallContainer: {
    width: 80,
    height: 80,
  },
  mediumContainer: {
    width: 100,
    height: 100,
  },
  largeContainer: {
    width: 120,
    height: 120,
  },
  gradient: {
    flex: 1,
    padding: 1,
  },
  content: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 11,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  iconContainer: {
    borderRadius: 20,
    padding: 8,
    marginBottom: 4,
  },
  smallName: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 2,
  },
  mediumName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 2,
  },
  largeName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 4,
  },
  smallPoints: {
    fontSize: 8,
    color: '#FFD700',
    fontWeight: 'bold',
  },
  mediumPoints: {
    fontSize: 10,
    color: '#FFD700',
    fontWeight: 'bold',
  },
  largePoints: {
    fontSize: 12,
    color: '#FFD700',
    fontWeight: 'bold',
  },
  lockedText: {
    color: '#CCCCCC',
  },
  lockedPoints: {
    color: '#888888',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    right: 4,
  },
  progressBar: {
    height: 3,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 1.5,
  },
  progressText: {
    fontSize: 8,
    color: '#CCCCCC',
    textAlign: 'center',
    marginTop: 2,
  },
  unlockedIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
});
