import { useState, useEffect, useCallback } from 'react';
import GameDetector, { GameInfo } from '../services/game/GameDetector';

interface UseGameDetectionReturn {
  games: GameInfo[];
  multiplayerGames: GameInfo[];
  localMultiplayerGames: GameInfo[];
  recentGames: GameInfo[];
  mostPlayedGames: GameInfo[];
  categories: string[];
  loading: boolean;
  error: string | null;
  refreshGames: () => Promise<void>;
  searchGames: (query: string) => Promise<GameInfo[]>;
  getGamesByCategory: (category: string) => Promise<GameInfo[]>;
  getCompatibleGames: (requirements: {
    minPlayers?: number;
    maxPlayers?: number;
    networkAvailable?: boolean;
    bluetoothAvailable?: boolean;
  }) => Promise<GameInfo[]>;
  updatePlaytime: (packageName: string, minutes: number) => Promise<void>;
}

export default function useGameDetection(): UseGameDetectionReturn {
  const [games, setGames] = useState<GameInfo[]>([]);
  const [multiplayerGames, setMultiplayerGames] = useState<GameInfo[]>([]);
  const [localMultiplayerGames, setLocalMultiplayerGames] = useState<GameInfo[]>([]);
  const [recentGames, setRecentGames] = useState<GameInfo[]>([]);
  const [mostPlayedGames, setMostPlayedGames] = useState<GameInfo[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadGames = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all games and related data
      const [
        allGames,
        multiplayer,
        localMultiplayer,
        recent,
        mostPlayed,
        gameCategories,
      ] = await Promise.all([
        GameDetector.getInstalledGames(),
        GameDetector.getMultiplayerGames(),
        GameDetector.getLocalMultiplayerGames(),
        GameDetector.getRecentlyPlayedGames(),
        GameDetector.getMostPlayedGames(),
        GameDetector.getGameCategories(),
      ]);

      setGames(allGames);
      setMultiplayerGames(multiplayer);
      setLocalMultiplayerGames(localMultiplayer);
      setRecentGames(recent);
      setMostPlayedGames(mostPlayed);
      setCategories(gameCategories);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load games');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshGames = useCallback(async () => {
    try {
      setError(null);
      await GameDetector.scanForGames();
      await loadGames();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh games');
    }
  }, [loadGames]);

  const searchGames = useCallback(async (query: string): Promise<GameInfo[]> => {
    try {
      return await GameDetector.searchGames(query);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
      return [];
    }
  }, []);

  const getGamesByCategory = useCallback(async (category: string): Promise<GameInfo[]> => {
    try {
      return await GameDetector.getGamesByCategory(category);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get games by category');
      return [];
    }
  }, []);

  const getCompatibleGames = useCallback(async (requirements: {
    minPlayers?: number;
    maxPlayers?: number;
    networkAvailable?: boolean;
    bluetoothAvailable?: boolean;
  }): Promise<GameInfo[]> => {
    try {
      return await GameDetector.getCompatibleGames(requirements);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get compatible games');
      return [];
    }
  }, []);

  const updatePlaytime = useCallback(async (packageName: string, minutes: number): Promise<void> => {
    try {
      await GameDetector.updateGamePlaytime(packageName, minutes);
      // Refresh the games data to reflect the updated playtime
      await loadGames();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update playtime');
    }
  }, [loadGames]);

  useEffect(() => {
    loadGames();
  }, [loadGames]);

  return {
    games,
    multiplayerGames,
    localMultiplayerGames,
    recentGames,
    mostPlayedGames,
    categories,
    loading,
    error,
    refreshGames,
    searchGames,
    getGamesByCategory,
    getCompatibleGames,
    updatePlaytime,
  };
}
