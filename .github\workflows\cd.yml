name: 🚀 Continuous Deployment

on:
  push:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - development
          - staging
          - production
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  # Job 1: Pre-deployment checks
  pre-deployment:
    name: 🔍 Pre-deployment Checks
    runs-on: ubuntu-latest
    timeout-minutes: 5
    outputs:
      environment: ${{ steps.determine-env.outputs.environment }}
      should-deploy: ${{ steps.determine-env.outputs.should-deploy }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🎯 Determine deployment environment
        id: determine-env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "should-deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/develop" ]; then
            echo "environment=development" >> $GITHUB_OUTPUT
            echo "should-deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "should-deploy=true" >> $GITHUB_OUTPUT
          else
            echo "environment=none" >> $GITHUB_OUTPUT
            echo "should-deploy=false" >> $GITHUB_OUTPUT
          fi

      - name: 📋 Display deployment info
        run: |
          echo "🎯 Target Environment: ${{ steps.determine-env.outputs.environment }}"
          echo "🚀 Should Deploy: ${{ steps.determine-env.outputs.should-deploy }}"

  # Job 2: Run CI pipeline (unless skipped)
  run-ci:
    name: 🔄 Run CI Pipeline
    uses: ./.github/workflows/ci.yml
    if: needs.pre-deployment.outputs.should-deploy == 'true' && github.event.inputs.skip_tests != 'true'
    needs: pre-deployment

  # Job 3: Development deployment
  deploy-development:
    name: 🧪 Deploy to Development
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [pre-deployment, run-ci]
    if: |
      always() && 
      needs.pre-deployment.outputs.environment == 'development' && 
      needs.pre-deployment.outputs.should-deploy == 'true' &&
      (needs.run-ci.result == 'success' || github.event.inputs.skip_tests == 'true')
    environment:
      name: development
      url: https://dev.logaco.app
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: 🔑 Expo authentication
        run: npx expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 🏗️ Build development version
        run: |
          npx expo export --platform all
          npx expo build:web

      - name: 🚀 Deploy to development
        run: |
          echo "🚀 Deploying to development environment..."
          # Add your development deployment commands here
          # Example: Deploy to Expo development server
          npx expo publish --release-channel=development

      - name: 📱 Update development app
        run: |
          echo "📱 Updating development app..."
          # Add commands to update development builds
          # Example: Trigger EAS update
          npx eas update --branch=development --message="Development deployment from ${{ github.sha }}"

      - name: ✅ Deployment success notification
        run: |
          echo "✅ Development deployment completed successfully!"
          echo "🔗 App URL: https://dev.logaco.app"

  # Job 4: Staging deployment
  deploy-staging:
    name: 🎭 Deploy to Staging
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [pre-deployment, run-ci]
    if: |
      always() && 
      needs.pre-deployment.outputs.environment == 'staging' && 
      needs.pre-deployment.outputs.should-deploy == 'true' &&
      (needs.run-ci.result == 'success' || github.event.inputs.skip_tests == 'true')
    environment:
      name: staging
      url: https://staging.logaco.app
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: 🔑 Expo authentication
        run: npx expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 🏗️ Build staging version
        run: |
          npx expo export --platform all
          npx expo build:web

      - name: 🚀 Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          npx expo publish --release-channel=staging

      - name: 📱 Update staging app
        run: |
          echo "📱 Updating staging app..."
          npx eas update --branch=staging --message="Staging deployment from ${{ github.sha }}"

      - name: 🧪 Run smoke tests
        run: |
          echo "🧪 Running smoke tests on staging..."
          # Add smoke test commands here
          npm run test:smoke || true

      - name: ✅ Staging deployment success
        run: |
          echo "✅ Staging deployment completed successfully!"
          echo "🔗 App URL: https://staging.logaco.app"

  # Job 5: Production deployment (requires manual approval)
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [pre-deployment, run-ci]
    if: |
      always() && 
      needs.pre-deployment.outputs.environment == 'production' && 
      needs.pre-deployment.outputs.should-deploy == 'true' &&
      (needs.run-ci.result == 'success' || github.event.inputs.skip_tests == 'true')
    environment:
      name: production
      url: https://logaco.app
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI and EAS CLI
        run: |
          npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}
          npm install -g eas-cli

      - name: 🔑 Expo authentication
        run: npx expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 🏗️ Build production version
        run: |
          npx expo export --platform all
          npx expo build:web

      - name: 📱 Build app store versions
        run: |
          echo "📱 Building app store versions..."
          # Build for iOS App Store
          npx eas build --platform ios --profile production --non-interactive
          # Build for Google Play Store
          npx eas build --platform android --profile production --non-interactive

      - name: 🚀 Deploy to production
        run: |
          echo "🚀 Deploying to production environment..."
          npx expo publish --release-channel=production

      - name: 📱 Update production app
        run: |
          echo "📱 Updating production app..."
          npx eas update --branch=production --message="Production deployment from ${{ github.sha }}"

      - name: 🏪 Submit to app stores
        run: |
          echo "🏪 Submitting to app stores..."
          # Submit to iOS App Store (requires additional setup)
          # npx eas submit --platform ios --latest
          # Submit to Google Play Store (requires additional setup)
          # npx eas submit --platform android --latest

      - name: ✅ Production deployment success
        run: |
          echo "✅ Production deployment completed successfully!"
          echo "🔗 App URL: https://logaco.app"
          echo "📱 App store submissions initiated"

  # Job 6: Post-deployment monitoring
  post-deployment:
    name: 📊 Post-deployment Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [deploy-development, deploy-staging, deploy-production]
    if: always() && (needs.deploy-development.result == 'success' || needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    
    steps:
      - name: 📊 Monitor deployment health
        run: |
          echo "📊 Monitoring deployment health..."
          # Add health check commands here
          # Example: Check app responsiveness, API endpoints, etc.

      - name: 📈 Update deployment metrics
        run: |
          echo "📈 Updating deployment metrics..."
          # Add metrics collection commands here

      - name: 🔔 Send success notifications
        run: |
          echo "🔔 Sending deployment success notifications..."
          # Add notification commands here (Slack, email, etc.)

  # Job 7: Rollback capability
  rollback:
    name: 🔄 Rollback Deployment
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: failure() && github.event_name == 'workflow_dispatch'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔄 Perform rollback
        run: |
          echo "🔄 Performing rollback..."
          # Add rollback commands here
          # Example: Revert to previous Expo release channel
          # npx expo publish --release-channel=production-rollback

      - name: 🚨 Rollback notification
        run: |
          echo "🚨 Rollback completed. Previous version restored."
