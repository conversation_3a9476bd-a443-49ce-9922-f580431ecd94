# 🔄 Pull Request

## 📋 Description

**What does this PR do?**
<!-- Provide a clear and concise description of the changes -->

**Related Issue(s):**
<!-- Link to related issues using "Fixes #123" or "Closes #123" -->

## 🎯 Type of Change

- [ ] 🐛 Bug fix (non-breaking change that fixes an issue)
- [ ] ✨ New feature (non-breaking change that adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 CI/CD pipeline changes
- [ ] 🎨 Code style/formatting changes
- [ ] ♻️ Code refactoring (no functional changes)
- [ ] ⚡ Performance improvements
- [ ] 🔒 Security improvements
- [ ] 🧪 Test additions or improvements

## 🧪 Testing

**How has this been tested?**
- [ ] Unit tests
- [ ] Integration tests
- [ ] Manual testing
- [ ] Automated testing in CI/CD

**Test Coverage:**
- [ ] All new code is covered by tests
- [ ] Existing tests still pass
- [ ] Coverage threshold is maintained (>80%)

**Testing Steps:**
1. <!-- Step 1 -->
2. <!-- Step 2 -->
3. <!-- Step 3 -->

## 📱 Platform Testing

**Tested on:**
- [ ] iOS Simulator
- [ ] iOS Device
- [ ] Android Emulator
- [ ] Android Device
- [ ] Web Browser
- [ ] Expo Go

**Screen Sizes Tested:**
- [ ] Phone (small)
- [ ] Phone (large)
- [ ] Tablet
- [ ] Desktop (web)

## 🔍 Code Quality

**Code Review Checklist:**
- [ ] Code follows project style guidelines
- [ ] Self-review of code completed
- [ ] Code is properly commented
- [ ] No console.log statements left in production code
- [ ] No TODO comments without issue references
- [ ] Error handling is implemented where needed

**Performance Considerations:**
- [ ] No performance regressions introduced
- [ ] Optimized for mobile performance
- [ ] Bundle size impact considered
- [ ] Memory usage optimized

## 🔒 Security

**Security Checklist:**
- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization considered
- [ ] No new security vulnerabilities introduced
- [ ] Dependencies are secure and up-to-date

## 📚 Documentation

**Documentation Updates:**
- [ ] README updated (if needed)
- [ ] API documentation updated
- [ ] Code comments added/updated
- [ ] Changelog updated
- [ ] Migration guide provided (for breaking changes)

## 🚀 Deployment

**Deployment Considerations:**
- [ ] Database migrations included (if needed)
- [ ] Environment variables documented
- [ ] Feature flags configured (if applicable)
- [ ] Rollback plan considered
- [ ] Monitoring/alerting updated

**Release Notes:**
<!-- What should be included in the release notes? -->

## 📊 Impact Analysis

**Areas Affected:**
- [ ] User Interface
- [ ] Business Logic
- [ ] Data Layer
- [ ] API Integration
- [ ] Authentication
- [ ] Navigation
- [ ] Performance
- [ ] Security

**Breaking Changes:**
<!-- List any breaking changes and migration steps -->

**Backward Compatibility:**
- [ ] Fully backward compatible
- [ ] Requires migration steps
- [ ] Breaking change (major version bump needed)

## 🔗 Dependencies

**New Dependencies Added:**
<!-- List any new packages or dependencies -->

**Dependencies Updated:**
<!-- List any updated packages -->

**Dependency Security:**
- [ ] All dependencies are from trusted sources
- [ ] No known security vulnerabilities
- [ ] License compatibility verified

## 📸 Screenshots/Videos

**Before:**
<!-- Screenshots or videos showing the current state -->

**After:**
<!-- Screenshots or videos showing the changes -->

## ✅ Pre-merge Checklist

**Code Quality:**
- [ ] All CI/CD checks pass
- [ ] Code review completed
- [ ] No merge conflicts
- [ ] Branch is up to date with target branch

**Testing:**
- [ ] All tests pass locally
- [ ] All tests pass in CI
- [ ] Manual testing completed
- [ ] Cross-platform testing done (if applicable)

**Documentation:**
- [ ] Code is self-documenting
- [ ] Complex logic is commented
- [ ] Public APIs are documented
- [ ] README updated if needed

**Security:**
- [ ] Security review completed
- [ ] No secrets in code
- [ ] Input validation implemented
- [ ] Error handling doesn't expose sensitive info

## 🎯 Post-merge Tasks

**Immediate Tasks:**
- [ ] Monitor deployment
- [ ] Verify functionality in staging
- [ ] Update project board/issues
- [ ] Notify stakeholders

**Follow-up Tasks:**
- [ ] Performance monitoring
- [ ] User feedback collection
- [ ] Analytics review
- [ ] Documentation updates

## 💬 Additional Notes

**Special Instructions:**
<!-- Any special instructions for reviewers or deployers -->

**Known Issues:**
<!-- Any known issues or limitations -->

**Future Improvements:**
<!-- Ideas for future enhancements -->

---

**Reviewer Guidelines:**
- Focus on code quality, security, and maintainability
- Test the changes locally if possible
- Verify all checklist items are completed
- Provide constructive feedback
- Approve only when confident in the changes

**Merge Guidelines:**
- Use "Squash and merge" for feature branches
- Use "Merge commit" for release branches
- Ensure commit message follows conventional commits format
- Delete feature branch after merge
