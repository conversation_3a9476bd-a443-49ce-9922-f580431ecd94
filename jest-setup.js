// Mock AsyncStorage
jest.mock("@react-native-async-storage/async-storage", () =>
  require("@react-native-async-storage/async-storage/jest/async-storage-mock")
);

// Mock Expo modules
jest.mock("expo-constants", () => ({
  default: {
    expoConfig: {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      slug: "logaco",
    },
  },
}));

jest.mock("expo-router", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
  Link: ({ children }) => children,
}));

jest.mock("expo-blur", () => ({
  BlurView: "BlurView",
}));

jest.mock("expo-linear-gradient", () => ({
  LinearGradient: "LinearGradient",
}));

// Mock networking modules - moved to individual test files to avoid module resolution issues

// Mock QR code components
jest.mock("react-native-qrcode-svg", () => "QRCode");
jest.mock("expo-barcode-scanner", () => ({
  BarCodeScanner: {
    requestPermissionsAsync: jest.fn(() =>
      Promise.resolve({ status: "granted" })
    ),
    Constants: {
      BarCodeType: {
        qr: "qr",
      },
    },
  },
}));

// Silence the warning: Animated: `useNativeDriver` is not supported
// Note: This mock is not needed for our current setup

// Mock react-native-safe-area-context
jest.mock("react-native-safe-area-context", () => ({
  SafeAreaProvider: ({ children }) => children,
  SafeAreaView: ({ children }) => children,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

// Global test utilities
global.mockNetworkService = {
  isConnected: false,
  connectedDevices: [],
  availableDevices: [],
  connectionMethod: "bluetooth",
  connect: jest.fn(),
  disconnect: jest.fn(),
  startScanning: jest.fn(),
  stopScanning: jest.fn(),
  sendData: jest.fn(),
};

global.mockGameService = {
  installedGames: [],
  activeSessions: [],
  detectGames: jest.fn(),
  createSession: jest.fn(),
  joinSession: jest.fn(),
  leaveSession: jest.fn(),
};
