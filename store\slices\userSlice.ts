import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UserProfile {
  id: string;
  name: string;
  avatar?: string;
  deviceName: string;
  deviceType: 'ios' | 'android' | 'windows' | 'mac';
  isVisible: boolean;
  status: 'online' | 'away' | 'busy' | 'offline';
  createdAt: number;
  lastActiveAt: number;
  // Social features
  achievements: string[];
  totalPoints: number;
  level: number;
  title?: string;
  friendCount: number;
  groupCount: number;
}

export interface UserPreferences {
  // Connection preferences
  autoConnect: boolean;
  connectionTimeout: number;
  maxConnections: number;
  preferredConnectionType: 'bluetooth' | 'wifi' | 'auto';

  // Privacy settings
  shareDeviceInfo: boolean;
  shareGameActivity: boolean;
  allowInvitations: boolean;

  // Notification preferences
  connectionNotifications: boolean;
  gameInviteNotifications: boolean;
  sessionUpdateNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;

  // Display preferences
  theme: 'dark' | 'light' | 'auto';
  language: string;
  showOnlineStatus: boolean;

  // Game preferences
  favoriteGames: string[];
  recentConnectionMethods: string[];
}

export interface UserStats {
  totalConnections: number;
  totalGameSessions: number;
  totalPlayTime: number; // in milliseconds
  favoriteGame?: string;
  longestSession: number; // in milliseconds
  averageSessionLength: number; // in milliseconds
  devicesConnected: string[];
  firstConnectionDate?: number;
  lastConnectionDate?: number;
  // Gaming stats
  gamesPlayed: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  // Social stats
  achievementsUnlocked: number;
  leaderboardRank: number;
  sessionsHosted: number;
  sessionsJoined: number;
}

export interface UserState {
  // Profile
  profile: UserProfile | null;
  isProfileSetup: boolean;

  // Preferences
  preferences: UserPreferences;

  // Stats
  stats: UserStats;

  // Onboarding
  hasCompletedOnboarding: boolean;
  onboardingStep: number;

  // App state
  isFirstLaunch: boolean;
  appVersion: string;
  lastAppVersion?: string;

  // Loading states
  isUpdatingProfile: boolean;
  isSavingPreferences: boolean;

  // Errors
  error: string | null;
}

const initialState: UserState = {
  // Profile
  profile: null,
  isProfileSetup: false,

  // Preferences
  preferences: {
    // Connection preferences
    autoConnect: false,
    connectionTimeout: 30000,
    maxConnections: 4,
    preferredConnectionType: 'auto',

    // Privacy settings
    shareDeviceInfo: true,
    shareGameActivity: true,
    allowInvitations: true,

    // Notification preferences
    connectionNotifications: true,
    gameInviteNotifications: true,
    sessionUpdateNotifications: true,
    soundEnabled: true,
    vibrationEnabled: true,

    // Display preferences
    theme: 'dark',
    language: 'en',
    showOnlineStatus: true,

    // Game preferences
    favoriteGames: [],
    recentConnectionMethods: [],
  },

  // Stats
  stats: {
    totalConnections: 0,
    totalGameSessions: 0,
    totalPlayTime: 0,
    longestSession: 0,
    averageSessionLength: 0,
    devicesConnected: [],
    // Gaming stats
    gamesPlayed: 0,
    gamesWon: 0,
    gamesLost: 0,
    winRate: 0,
    // Social stats
    achievementsUnlocked: 0,
    leaderboardRank: 0,
    sessionsHosted: 0,
    sessionsJoined: 0,
  },

  // Onboarding
  hasCompletedOnboarding: false,
  onboardingStep: 0,

  // App state
  isFirstLaunch: true,
  appVersion: '1.0.0',

  // Loading states
  isUpdatingProfile: false,
  isSavingPreferences: false,

  // Errors
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Profile management
    setProfile: (state, action: PayloadAction<UserProfile>) => {
      state.profile = action.payload;
      state.isProfileSetup = true;
    },

    updateProfile: (state, action: PayloadAction<Partial<UserProfile>>) => {
      if (state.profile) {
        state.profile = { ...state.profile, ...action.payload };
      }
    },

    setUserStatus: (state, action: PayloadAction<UserProfile['status']>) => {
      if (state.profile) {
        state.profile.status = action.payload;
        state.profile.lastActiveAt = Date.now();
      }
    },

    setDeviceVisibility: (state, action: PayloadAction<boolean>) => {
      if (state.profile) {
        state.profile.isVisible = action.payload;
      }
    },

    // Preferences management
    updatePreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },

    addFavoriteGame: (state, action: PayloadAction<string>) => {
      if (!state.preferences.favoriteGames.includes(action.payload)) {
        state.preferences.favoriteGames.push(action.payload);
      }
    },

    removeFavoriteGame: (state, action: PayloadAction<string>) => {
      state.preferences.favoriteGames = state.preferences.favoriteGames.filter(
        gameId => gameId !== action.payload,
      );
    },

    addRecentConnectionMethod: (state, action: PayloadAction<string>) => {
      const methods = state.preferences.recentConnectionMethods;
      const filtered = methods.filter(method => method !== action.payload);
      state.preferences.recentConnectionMethods = [action.payload, ...filtered].slice(0, 5);
    },

    // Stats management
    incrementConnections: state => {
      state.stats.totalConnections += 1;
      state.stats.lastConnectionDate = Date.now();

      if (!state.stats.firstConnectionDate) {
        state.stats.firstConnectionDate = Date.now();
      }
    },

    incrementGameSessions: state => {
      state.stats.totalGameSessions += 1;
    },

    addPlayTime: (state, action: PayloadAction<number>) => {
      state.stats.totalPlayTime += action.payload;

      // Update longest session if this one is longer
      if (action.payload > state.stats.longestSession) {
        state.stats.longestSession = action.payload;
      }

      // Recalculate average session length
      if (state.stats.totalGameSessions > 0) {
        state.stats.averageSessionLength =
          state.stats.totalPlayTime / state.stats.totalGameSessions;
      }
    },

    addConnectedDevice: (state, action: PayloadAction<string>) => {
      if (!state.stats.devicesConnected.includes(action.payload)) {
        state.stats.devicesConnected.push(action.payload);
      }
    },

    setFavoriteGame: (state, action: PayloadAction<string>) => {
      state.stats.favoriteGame = action.payload;
    },

    // Social stats management
    updateSocialStats: (state, action: PayloadAction<Partial<UserStats>>) => {
      state.stats = { ...state.stats, ...action.payload };
    },

    addAchievement: (state, action: PayloadAction<string>) => {
      if (state.profile && !state.profile.achievements.includes(action.payload)) {
        state.profile.achievements.push(action.payload);
        state.stats.achievementsUnlocked += 1;
      }
    },

    updatePoints: (state, action: PayloadAction<number>) => {
      if (state.profile) {
        state.profile.totalPoints += action.payload;
        // Calculate level based on points (every 1000 points = 1 level)
        state.profile.level = Math.floor(state.profile.totalPoints / 1000) + 1;
      }
    },

    setUserTitle: (state, action: PayloadAction<string>) => {
      if (state.profile) {
        state.profile.title = action.payload;
      }
    },

    updateSocialCounts: (
      state,
      action: PayloadAction<{ friendCount?: number; groupCount?: number }>,
    ) => {
      if (state.profile) {
        if (action.payload.friendCount !== undefined) {
          state.profile.friendCount = action.payload.friendCount;
        }
        if (action.payload.groupCount !== undefined) {
          state.profile.groupCount = action.payload.groupCount;
        }
      }
    },

    recordGameResult: (state, action: PayloadAction<'win' | 'loss' | 'draw'>) => {
      state.stats.gamesPlayed += 1;

      if (action.payload === 'win') {
        state.stats.gamesWon += 1;
      } else if (action.payload === 'loss') {
        state.stats.gamesLost += 1;
      }

      // Recalculate win rate
      if (state.stats.gamesPlayed > 0) {
        state.stats.winRate = Math.round((state.stats.gamesWon / state.stats.gamesPlayed) * 100);
      }
    },

    incrementSessionsHosted: state => {
      state.stats.sessionsHosted += 1;
    },

    incrementSessionsJoined: state => {
      state.stats.sessionsJoined += 1;
    },

    // Onboarding
    completeOnboarding: state => {
      state.hasCompletedOnboarding = true;
      state.onboardingStep = 0;
    },

    setOnboardingStep: (state, action: PayloadAction<number>) => {
      state.onboardingStep = action.payload;
    },

    skipOnboarding: state => {
      state.hasCompletedOnboarding = true;
      state.onboardingStep = 0;
    },

    // App state
    setFirstLaunch: (state, action: PayloadAction<boolean>) => {
      state.isFirstLaunch = action.payload;
    },

    updateAppVersion: (state, action: PayloadAction<string>) => {
      state.lastAppVersion = state.appVersion;
      state.appVersion = action.payload;
    },

    // Loading states
    setUpdatingProfile: (state, action: PayloadAction<boolean>) => {
      state.isUpdatingProfile = action.payload;
    },

    setSavingPreferences: (state, action: PayloadAction<boolean>) => {
      state.isSavingPreferences = action.payload;
    },

    // Error handling
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isUpdatingProfile = false;
      state.isSavingPreferences = false;
    },

    clearError: state => {
      state.error = null;
    },

    // Reset user data
    resetUserData: state => {
      return {
        ...initialState,
        isFirstLaunch: false,
        appVersion: state.appVersion,
      };
    },
  },
});

export const {
  setProfile,
  updateProfile,
  setUserStatus,
  setDeviceVisibility,
  updatePreferences,
  addFavoriteGame,
  removeFavoriteGame,
  addRecentConnectionMethod,
  incrementConnections,
  incrementGameSessions,
  addPlayTime,
  addConnectedDevice,
  setFavoriteGame,
  updateSocialStats,
  addAchievement,
  updatePoints,
  setUserTitle,
  updateSocialCounts,
  recordGameResult,
  incrementSessionsHosted,
  incrementSessionsJoined,
  completeOnboarding,
  setOnboardingStep,
  skipOnboarding,
  setFirstLaunch,
  updateAppVersion,
  setUpdatingProfile,
  setSavingPreferences,
  setError,
  clearError,
  resetUserData,
} = userSlice.actions;

export default userSlice.reducer;
