import Foundation
import MultipeerConnectivity
import React

@objc(MultipeerConnectivityBridge)
class MultipeerConnectivityBridge: RC<PERSON>ventEmitter, MCSessionDelegate, MCBrowserViewControllerDelegate, MCAdvertiserAssistantDelegate {
    
    private var peerID: MCPeerID!
    private var session: MCSession!
    private var browser: MCBrowserViewController?
    private var advertiser: MCAdvertiserAssistant?
    private var serviceType = "logaco-gaming"
    private var isAdvertising = false
    private var isBrowsing = false
    
    override init() {
        super.init()
        setupMultipeerConnectivity()
    }
    
    override func supportedEvents() -> [String]! {
        return [
            "MultipeerPeerFound",
            "MultipeerPeerLost",
            "MultipeerPeerConnected",
            "MultipeerPeerDisconnected",
            "MultipeerDataReceived",
            "MultipeerConnectionStateChanged",
            "MultipeerError"
        ]
    }
    
    private func setupMultipeerConnectivity() {
        let deviceName = UIDevice.current.name
        peerID = MCPeerID(displayName: deviceName)
        session = MCSession(peer: peerID, securityIdentity: nil, encryptionPreference: .required)
        session.delegate = self
    }
    
    // MARK: - React Native Bridge Methods
    
    @objc
    func startAdvertising(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            if !self.isAdvertising {
                self.advertiser = MCAdvertiserAssistant(serviceType: self.serviceType, discoveryInfo: nil, session: self.session)
                self.advertiser?.delegate = self
                self.advertiser?.start()
                self.isAdvertising = true
                resolve(["status": "advertising_started"])
            } else {
                resolve(["status": "already_advertising"])
            }
        }
    }
    
    @objc
    func stopAdvertising(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            if self.isAdvertising {
                self.advertiser?.stop()
                self.advertiser = nil
                self.isAdvertising = false
                resolve(["status": "advertising_stopped"])
            } else {
                resolve(["status": "not_advertising"])
            }
        }
    }
    
    @objc
    func startBrowsing(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            if !self.isBrowsing {
                self.browser = MCBrowserViewController(serviceType: self.serviceType, session: self.session)
                self.browser?.delegate = self
                self.isBrowsing = true
                
                if let rootViewController = UIApplication.shared.keyWindow?.rootViewController {
                    rootViewController.present(self.browser!, animated: true) {
                        resolve(["status": "browsing_started"])
                    }
                } else {
                    reject("PRESENTATION_ERROR", "Could not present browser", nil)
                }
            } else {
                resolve(["status": "already_browsing"])
            }
        }
    }
    
    @objc
    func stopBrowsing(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            if self.isBrowsing {
                self.browser?.dismiss(animated: true) {
                    self.browser = nil
                    self.isBrowsing = false
                    resolve(["status": "browsing_stopped"])
                }
            } else {
                resolve(["status": "not_browsing"])
            }
        }
    }
    
    @objc
    func sendData(_ data: String, toPeer peerDisplayName: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        guard let dataToSend = data.data(using: .utf8) else {
            reject("DATA_ERROR", "Could not convert string to data", nil)
            return
        }
        
        let targetPeers = session.connectedPeers.filter { $0.displayName == peerDisplayName }
        
        if targetPeers.isEmpty {
            reject("PEER_NOT_FOUND", "Peer not connected", nil)
            return
        }
        
        do {
            try session.send(dataToSend, toPeers: targetPeers, with: .reliable)
            resolve(["status": "data_sent", "bytes": dataToSend.count])
        } catch {
            reject("SEND_ERROR", error.localizedDescription, error)
        }
    }
    
    @objc
    func broadcastData(_ data: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        guard let dataToSend = data.data(using: .utf8) else {
            reject("DATA_ERROR", "Could not convert string to data", nil)
            return
        }
        
        let connectedPeers = session.connectedPeers
        
        if connectedPeers.isEmpty {
            resolve(["status": "no_peers_connected", "bytes": 0])
            return
        }
        
        do {
            try session.send(dataToSend, toPeers: connectedPeers, with: .reliable)
            resolve(["status": "data_broadcasted", "bytes": dataToSend.count, "peers": connectedPeers.count])
        } catch {
            reject("BROADCAST_ERROR", error.localizedDescription, error)
        }
    }
    
    @objc
    func getConnectedPeers(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        let peers = session.connectedPeers.map { peer in
            return [
                "displayName": peer.displayName,
                "peerID": peer.description
            ]
        }
        resolve(["peers": peers, "count": peers.count])
    }
    
    @objc
    func disconnectFromPeer(_ peerDisplayName: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        session.disconnect()
        resolve(["status": "disconnected"])
    }
    
    @objc
    func getSessionInfo(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        let info = [
            "localPeerID": peerID.displayName,
            "serviceType": serviceType,
            "isAdvertising": isAdvertising,
            "isBrowsing": isBrowsing,
            "connectedPeersCount": session.connectedPeers.count,
            "encryptionPreference": "required"
        ] as [String : Any]
        
        resolve(info)
    }
    
    // MARK: - MCSessionDelegate
    
    func session(_ session: MCSession, peer peerID: MCPeerID, didChange state: MCSessionState) {
        DispatchQueue.main.async {
            let stateString: String
            let eventName: String
            
            switch state {
            case .connected:
                stateString = "connected"
                eventName = "MultipeerPeerConnected"
            case .connecting:
                stateString = "connecting"
                eventName = "MultipeerConnectionStateChanged"
            case .notConnected:
                stateString = "disconnected"
                eventName = "MultipeerPeerDisconnected"
            @unknown default:
                stateString = "unknown"
                eventName = "MultipeerConnectionStateChanged"
            }
            
            let eventData = [
                "peerID": peerID.displayName,
                "state": stateString,
                "timestamp": Date().timeIntervalSince1970
            ] as [String : Any]
            
            self.sendEvent(withName: eventName, body: eventData)
        }
    }
    
    func session(_ session: MCSession, didReceive data: Data, fromPeer peerID: MCPeerID) {
        DispatchQueue.main.async {
            if let receivedString = String(data: data, encoding: .utf8) {
                let eventData = [
                    "data": receivedString,
                    "fromPeer": peerID.displayName,
                    "bytes": data.count,
                    "timestamp": Date().timeIntervalSince1970
                ] as [String : Any]
                
                self.sendEvent(withName: "MultipeerDataReceived", body: eventData)
            }
        }
    }
    
    func session(_ session: MCSession, didReceive stream: InputStream, withName streamName: String, fromPeer peerID: MCPeerID) {
        // Handle stream if needed
    }
    
    func session(_ session: MCSession, didStartReceivingResourceWithName resourceName: String, fromPeer peerID: MCPeerID, with progress: Progress) {
        // Handle resource transfer if needed
    }
    
    func session(_ session: MCSession, didFinishReceivingResourceWithName resourceName: String, fromPeer peerID: MCPeerID, at localURL: URL?, withError error: Error?) {
        // Handle resource transfer completion if needed
    }
    
    // MARK: - MCBrowserViewControllerDelegate
    
    func browserViewControllerDidFinish(_ browserViewController: MCBrowserViewController) {
        DispatchQueue.main.async {
            browserViewController.dismiss(animated: true) {
                self.browser = nil
                self.isBrowsing = false
            }
        }
    }
    
    func browserViewControllerWasCancelled(_ browserViewController: MCBrowserViewController) {
        DispatchQueue.main.async {
            browserViewController.dismiss(animated: true) {
                self.browser = nil
                self.isBrowsing = false
            }
        }
    }
    
    // MARK: - MCAdvertiserAssistantDelegate
    
    func advertiserAssistantWillPresentInvitation(_ advertiserAssistant: MCAdvertiserAssistant) {
        // Handle invitation presentation
    }
    
    func advertiserAssistantDidDismissInvitation(_ advertiserAssistant: MCAdvertiserAssistant) {
        // Handle invitation dismissal
    }
    
    // MARK: - Utility Methods
    
    @objc
    static func requiresMainQueueSetup() -> Bool {
        return true
    }
}

// MARK: - Objective-C Bridge Header

@objc(MultipeerConnectivityBridge)
class MultipeerConnectivityBridgeObjC: NSObject {
    @objc static func moduleName() -> String {
        return "MultipeerConnectivityBridge"
    }
}
