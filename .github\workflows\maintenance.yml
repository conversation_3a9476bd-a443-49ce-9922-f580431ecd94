name: 🔧 Maintenance & Security

on:
  schedule:
    # Run daily at 6 AM UTC
    - cron: '0 6 * * *'
    # Run weekly dependency updates on Mondays at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:
    inputs:
      task:
        description: 'Maintenance task to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - security-scan
          - dependency-update
          - performance-audit
          - cleanup

env:
  NODE_VERSION: '18'

jobs:
  # Job 1: Security scanning
  security-scan:
    name: 🔒 Security Scanning
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: github.event.inputs.task == 'security-scan' || github.event.inputs.task == 'all' || github.event_name == 'schedule'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Run npm audit
        run: |
          echo "🔍 Running npm audit..."
          npm audit --audit-level=low --json > npm-audit-results.json || true
          
          # Check for high/critical vulnerabilities
          HIGH_VULNS=$(cat npm-audit-results.json | jq '.metadata.vulnerabilities.high // 0')
          CRITICAL_VULNS=$(cat npm-audit-results.json | jq '.metadata.vulnerabilities.critical // 0')
          
          echo "🔍 High vulnerabilities: $HIGH_VULNS"
          echo "🔍 Critical vulnerabilities: $CRITICAL_VULNS"
          
          if [ "$HIGH_VULNS" -gt 0 ] || [ "$CRITICAL_VULNS" -gt 0 ]; then
            echo "⚠️ High or critical vulnerabilities found!"
            echo "high-vulns=$HIGH_VULNS" >> $GITHUB_OUTPUT
            echo "critical-vulns=$CRITICAL_VULNS" >> $GITHUB_OUTPUT
          fi

      - name: 🔒 Advanced security scan with Snyk
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium --json > snyk-results.json
        continue-on-error: true

      - name: 🕵️ Scan for secrets and sensitive data
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified --json > trufflehog-results.json
        continue-on-error: true

      - name: 📊 Generate security report
        run: |
          echo "# 🔒 Security Scan Report" > security-report.md
          echo "" >> security-report.md
          echo "**Scan Date:** $(date)" >> security-report.md
          echo "" >> security-report.md
          
          echo "## 📦 NPM Audit Results" >> security-report.md
          if [ -f npm-audit-results.json ]; then
            echo "- **Total vulnerabilities:** $(cat npm-audit-results.json | jq '.metadata.vulnerabilities.total // 0')" >> security-report.md
            echo "- **Critical:** $(cat npm-audit-results.json | jq '.metadata.vulnerabilities.critical // 0')" >> security-report.md
            echo "- **High:** $(cat npm-audit-results.json | jq '.metadata.vulnerabilities.high // 0')" >> security-report.md
            echo "- **Moderate:** $(cat npm-audit-results.json | jq '.metadata.vulnerabilities.moderate // 0')" >> security-report.md
            echo "- **Low:** $(cat npm-audit-results.json | jq '.metadata.vulnerabilities.low // 0')" >> security-report.md
          fi
          
          echo "" >> security-report.md
          echo "## 🔍 Snyk Scan Results" >> security-report.md
          if [ -f snyk-results.json ]; then
            echo "- **Snyk scan completed**" >> security-report.md
          else
            echo "- **Snyk scan not available**" >> security-report.md
          fi
          
          echo "" >> security-report.md
          echo "## 🕵️ Secret Scan Results" >> security-report.md
          if [ -f trufflehog-results.json ]; then
            echo "- **Secret scan completed**" >> security-report.md
          else
            echo "- **Secret scan not available**" >> security-report.md
          fi

      - name: 📤 Upload security artifacts
        uses: actions/upload-artifact@v4
        with:
          name: security-scan-results
          path: |
            security-report.md
            npm-audit-results.json
            snyk-results.json
            trufflehog-results.json
          retention-days: 30

  # Job 2: Dependency updates
  dependency-update:
    name: 📦 Dependency Updates
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: |
      (github.event.inputs.task == 'dependency-update' || github.event.inputs.task == 'all' || 
       (github.event_name == 'schedule' && github.event.schedule == '0 9 * * 1'))
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔍 Check for outdated packages
        run: |
          echo "🔍 Checking for outdated packages..."
          npm outdated --json > outdated-packages.json || true
          
          if [ -s outdated-packages.json ]; then
            echo "📦 Found outdated packages:"
            cat outdated-packages.json | jq -r 'keys[]'
            echo "has-updates=true" >> $GITHUB_OUTPUT
          else
            echo "✅ All packages are up to date"
            echo "has-updates=false" >> $GITHUB_OUTPUT
          fi

      - name: 🔄 Update dependencies
        if: steps.check-outdated.outputs.has-updates == 'true'
        run: |
          echo "🔄 Updating dependencies..."
          
          # Update patch and minor versions only (safer)
          npx npm-check-updates -u --target minor
          
          # Install updated dependencies
          npm install
          
          # Run tests to ensure updates don't break anything
          npm test

      - name: 📝 Generate update report
        if: steps.check-outdated.outputs.has-updates == 'true'
        run: |
          echo "# 📦 Dependency Update Report" > dependency-update-report.md
          echo "" >> dependency-update-report.md
          echo "**Update Date:** $(date)" >> dependency-update-report.md
          echo "" >> dependency-update-report.md
          
          echo "## 📦 Updated Packages" >> dependency-update-report.md
          if [ -f outdated-packages.json ]; then
            cat outdated-packages.json | jq -r 'to_entries[] | "- **\(.key)**: \(.value.current) → \(.value.wanted)"' >> dependency-update-report.md
          fi
          
          echo "" >> dependency-update-report.md
          echo "## ✅ Test Results" >> dependency-update-report.md
          echo "All tests passed after dependency updates." >> dependency-update-report.md

      - name: 🔀 Create pull request
        if: steps.check-outdated.outputs.has-updates == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: '📦 Update dependencies'
          title: '📦 Automated dependency updates'
          body-path: dependency-update-report.md
          branch: automated/dependency-updates
          delete-branch: true
          labels: |
            dependencies
            automated

  # Job 3: Performance audit
  performance-audit:
    name: ⚡ Performance Audit
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: github.event.inputs.task == 'performance-audit' || github.event.inputs.task == 'all' || github.event_name == 'schedule'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli

      - name: 🏗️ Build for performance analysis
        run: |
          npx expo export --platform web
          npx expo build:web

      - name: 📊 Analyze bundle size
        run: |
          echo "📊 Analyzing bundle size..."
          
          # Install bundle analyzer
          npm install -g webpack-bundle-analyzer
          
          # Analyze bundle (if webpack stats are available)
          if [ -f dist/static/js/*.js ]; then
            echo "📦 JavaScript bundle sizes:" > performance-report.md
            ls -lh dist/static/js/*.js >> performance-report.md
            
            echo "" >> performance-report.md
            echo "📦 CSS bundle sizes:" >> performance-report.md
            ls -lh dist/static/css/*.css >> performance-report.md || echo "No CSS files found" >> performance-report.md
          fi

      - name: 🔍 Check for performance issues
        run: |
          echo "🔍 Checking for performance issues..."
          
          # Check for large files
          echo "" >> performance-report.md
          echo "## 🚨 Large Files (>1MB)" >> performance-report.md
          find dist -size +1M -type f -exec ls -lh {} \; >> performance-report.md || echo "No large files found" >> performance-report.md
          
          # Check for duplicate dependencies
          echo "" >> performance-report.md
          echo "## 🔄 Duplicate Dependencies" >> performance-report.md
          npx npm ls --depth=0 | grep -E "UNMET|duplicate" >> performance-report.md || echo "No duplicate dependencies found" >> performance-report.md

      - name: 📤 Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: performance-audit-results
          path: performance-report.md
          retention-days: 30

  # Job 4: Cleanup tasks
  cleanup:
    name: 🧹 Cleanup Tasks
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: github.event.inputs.task == 'cleanup' || github.event.inputs.task == 'all' || github.event_name == 'schedule'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🧹 Clean up old artifacts
        uses: actions/github-script@v7
        with:
          script: |
            const { data: artifacts } = await github.rest.actions.listArtifactsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 100
            });
            
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            
            for (const artifact of artifacts.artifacts) {
              const createdAt = new Date(artifact.created_at);
              if (createdAt < thirtyDaysAgo) {
                console.log(`Deleting old artifact: ${artifact.name}`);
                await github.rest.actions.deleteArtifact({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  artifact_id: artifact.id
                });
              }
            }

      - name: 📊 Generate maintenance summary
        run: |
          echo "# 🔧 Maintenance Summary" > maintenance-summary.md
          echo "" >> maintenance-summary.md
          echo "**Date:** $(date)" >> maintenance-summary.md
          echo "" >> maintenance-summary.md
          echo "## ✅ Completed Tasks" >> maintenance-summary.md
          echo "- Security scanning" >> maintenance-summary.md
          echo "- Dependency updates" >> maintenance-summary.md
          echo "- Performance audit" >> maintenance-summary.md
          echo "- Cleanup tasks" >> maintenance-summary.md
          echo "" >> maintenance-summary.md
          echo "## 📊 Next Scheduled Run" >> maintenance-summary.md
          echo "- **Daily security scan:** Tomorrow at 6 AM UTC" >> maintenance-summary.md
          echo "- **Weekly dependency update:** Next Monday at 9 AM UTC" >> maintenance-summary.md

      - name: 📤 Upload maintenance summary
        uses: actions/upload-artifact@v4
        with:
          name: maintenance-summary
          path: maintenance-summary.md
          retention-days: 7
