import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Animated,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";

export type StatusType =
  | "online"
  | "offline"
  | "connecting"
  | "connected"
  | "disconnected"
  | "error"
  | "warning"
  | "success"
  | "info";

interface StatusIndicatorProps {
  status: StatusType;
  label?: string;
  size?: "small" | "medium" | "large";
  variant?: "dot" | "badge" | "card";
  animated?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  showIcon?: boolean;
  customColor?: string;
}

export default function StatusIndicator({
  status,
  label,
  size = "medium",
  variant = "dot",
  animated = true,
  style,
  textStyle,
  showIcon = false,
  customColor,
}: StatusIndicatorProps) {
  const pulseValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;

  const getStatusConfig = () => {
    const configs = {
      online: {
        color: "#4CAF50",
        icon: "checkmark-circle" as keyof typeof Ionicons.glyphMap,
        text: "Online",
      },
      offline: {
        color: "#9E9E9E",
        icon: "close-circle" as keyof typeof Ionicons.glyphMap,
        text: "Offline",
      },
      connecting: {
        color: "#FF9800",
        icon: "sync" as keyof typeof Ionicons.glyphMap,
        text: "Connecting",
      },
      connected: {
        color: "#00D4FF",
        icon: "wifi" as keyof typeof Ionicons.glyphMap,
        text: "Connected",
      },
      disconnected: {
        color: "#F44336",
        icon: "wifi-off" as keyof typeof Ionicons.glyphMap,
        text: "Disconnected",
      },
      error: {
        color: "#F44336",
        icon: "alert-circle" as keyof typeof Ionicons.glyphMap,
        text: "Error",
      },
      warning: {
        color: "#FF9800",
        icon: "warning" as keyof typeof Ionicons.glyphMap,
        text: "Warning",
      },
      success: {
        color: "#4CAF50",
        icon: "checkmark-circle" as keyof typeof Ionicons.glyphMap,
        text: "Success",
      },
      info: {
        color: "#2196F3",
        icon: "information-circle" as keyof typeof Ionicons.glyphMap,
        text: "Info",
      },
    };

    return configs[status];
  };

  const getSizeConfig = () => {
    const configs = {
      small: {
        dotSize: 8,
        iconSize: 16,
        fontSize: 12,
        padding: 6,
      },
      medium: {
        dotSize: 12,
        iconSize: 20,
        fontSize: 14,
        padding: 8,
      },
      large: {
        dotSize: 16,
        iconSize: 24,
        fontSize: 16,
        padding: 12,
      },
    };

    return configs[size];
  };

  useEffect(() => {
    if (animated && (status === "connecting" || status === "online")) {
      // Pulse animation for connecting/online status
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );

      pulseAnimation.start();

      return () => pulseAnimation.stop();
    }

    if (animated && (status === "success" || status === "connected")) {
      // Scale animation for success states
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [status, animated, pulseValue, scaleValue]);

  const statusConfig = getStatusConfig();
  const sizeConfig = getSizeConfig();

  // Fallback for undefined status
  if (!statusConfig) {
    console.warn(
      `StatusIndicator: Unknown status "${status}". Using default "info" status.`
    );
    const fallbackConfig = {
      color: "#2196F3",
      icon: "information-circle" as keyof typeof Ionicons.glyphMap,
      text: "Unknown",
    };
    const color = customColor || fallbackConfig.color;
    const displayLabel = label || fallbackConfig.text;

    return (
      <View style={[styles.dotContainer, style]}>
        <View
          style={[
            styles.dot,
            {
              width: sizeConfig.dotSize,
              height: sizeConfig.dotSize,
              backgroundColor: color,
            },
          ]}
        />
        {displayLabel && (
          <Text
            style={[
              styles.dotLabel,
              { fontSize: sizeConfig.fontSize },
              textStyle,
            ]}
          >
            {displayLabel}
          </Text>
        )}
      </View>
    );
  }

  const color = customColor || statusConfig.color;
  const displayLabel = label || statusConfig.text;

  const renderDot = () => (
    <Animated.View
      style={[
        styles.dot,
        {
          width: sizeConfig.dotSize,
          height: sizeConfig.dotSize,
          backgroundColor: color,
          transform: [{ scale: scaleValue }],
        },
        animated &&
          (status === "connecting" || status === "online") && {
            opacity: pulseValue.interpolate({
              inputRange: [0, 1],
              outputRange: [0.5, 1],
            }),
          },
      ]}
    />
  );

  const renderBadge = () => (
    <View style={[styles.badge, { padding: sizeConfig.padding }, style]}>
      <Animated.View
        style={[styles.badgeContent, { transform: [{ scale: scaleValue }] }]}
      >
        {showIcon && (
          <Ionicons
            name={statusConfig.icon}
            size={sizeConfig.iconSize}
            color={color}
            style={styles.badgeIcon}
          />
        )}
        {renderDot()}
        {displayLabel && (
          <Text
            style={[
              styles.badgeText,
              { fontSize: sizeConfig.fontSize, color },
              textStyle,
            ]}
          >
            {displayLabel}
          </Text>
        )}
      </Animated.View>
    </View>
  );

  const renderCard = () => (
    <BlurView intensity={15} style={[styles.card, style]}>
      <Animated.View
        style={[
          styles.cardContent,
          { padding: sizeConfig.padding },
          { transform: [{ scale: scaleValue }] },
        ]}
      >
        <View style={styles.cardHeader}>
          {showIcon && (
            <Ionicons
              name={statusConfig.icon}
              size={sizeConfig.iconSize}
              color={color}
              style={styles.cardIcon}
            />
          )}
          {renderDot()}
          {displayLabel && (
            <Text
              style={[
                styles.cardText,
                { fontSize: sizeConfig.fontSize },
                textStyle,
              ]}
            >
              {displayLabel}
            </Text>
          )}
        </View>
      </Animated.View>
    </BlurView>
  );

  if (variant === "badge") {
    return renderBadge();
  }

  if (variant === "card") {
    return renderCard();
  }

  // Default dot variant
  return (
    <View style={[styles.dotContainer, style]}>
      {renderDot()}
      {displayLabel && (
        <Text
          style={[
            styles.dotLabel,
            { fontSize: sizeConfig.fontSize },
            textStyle,
          ]}
        >
          {displayLabel}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  dotContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  dot: {
    borderRadius: 50,
  },
  dotLabel: {
    color: "#FFFFFF",
    fontWeight: "500",
  },
  badge: {
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  badgeContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  badgeIcon: {
    marginRight: 2,
  },
  badgeText: {
    fontWeight: "500",
  },
  card: {
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "rgba(0, 212, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  cardContent: {
    minWidth: 120,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  cardIcon: {
    marginRight: 4,
  },
  cardText: {
    color: "#FFFFFF",
    fontWeight: "500",
    flex: 1,
  },
});
