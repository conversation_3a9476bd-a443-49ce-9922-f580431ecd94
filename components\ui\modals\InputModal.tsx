import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

interface InputModalProps {
  visible: boolean;
  title: string;
  message?: string;
  placeholder?: string;
  defaultValue?: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  onConfirm: (value: string) => void;
  onCancel: () => void;
  validation?: (value: string) => string | null; // Returns error message or null
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  maxLength?: number;
  multiline?: boolean;
  autoFocus?: boolean;
}

const { width, height } = Dimensions.get('window');

export default function InputModal({
  visible,
  title,
  message,
  placeholder = 'Enter text...',
  defaultValue = '',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmColor = '#00D4FF',
  icon = 'create',
  iconColor = '#00D4FF',
  onConfirm,
  onCancel,
  validation,
  secureTextEntry = false,
  keyboardType = 'default',
  maxLength,
  multiline = false,
  autoFocus = true,
}: InputModalProps) {
  const [value, setValue] = useState(defaultValue);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (visible) {
      setValue(defaultValue);
      setError(null);
      if (autoFocus && inputRef.current) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      }
    }
  }, [visible, defaultValue, autoFocus]);

  const handleConfirm = () => {
    if (validation) {
      const validationError = validation(value);
      if (validationError) {
        setError(validationError);
        return;
      }
    }
    
    onConfirm(value);
  };

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    if (error) {
      setError(null);
    }
  };

  const isConfirmDisabled = value.trim().length === 0 || !!error;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={onCancel}
    >
      <KeyboardAvoidingView
        style={styles.overlay}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <BlurView intensity={20} style={styles.blurOverlay}>
          <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={1}
            onPress={onCancel}
          />
          
          <View style={styles.modalContainer}>
            <BlurView intensity={40} style={styles.modalBlur}>
              <View style={styles.modalContent}>
                {/* Header */}
                <View style={styles.header}>
                  {icon && (
                    <View style={[styles.iconContainer, { backgroundColor: `${iconColor}20` }]}>
                      <Ionicons name={icon} size={28} color={iconColor} />
                    </View>
                  )}
                  
                  <View style={styles.headerText}>
                    <Text style={styles.title}>{title}</Text>
                    {message && (
                      <Text style={styles.message}>{message}</Text>
                    )}
                  </View>
                </View>

                {/* Input */}
                <View style={styles.inputContainer}>
                  <TextInput
                    ref={inputRef}
                    style={[
                      styles.input,
                      multiline && styles.inputMultiline,
                      error && styles.inputError,
                    ]}
                    value={value}
                    onChangeText={handleValueChange}
                    placeholder={placeholder}
                    placeholderTextColor="rgba(255, 255, 255, 0.5)"
                    secureTextEntry={secureTextEntry}
                    keyboardType={keyboardType}
                    maxLength={maxLength}
                    multiline={multiline}
                    numberOfLines={multiline ? 4 : 1}
                    textAlignVertical={multiline ? 'top' : 'center'}
                    selectionColor="#00D4FF"
                  />
                  
                  {error && (
                    <View style={styles.errorContainer}>
                      <Ionicons name="warning" size={16} color="#FF4757" />
                      <Text style={styles.errorText}>{error}</Text>
                    </View>
                  )}
                  
                  {maxLength && (
                    <Text style={styles.characterCount}>
                      {value.length}/{maxLength}
                    </Text>
                  )}
                </View>

                {/* Actions */}
                <View style={styles.actionsContainer}>
                  <TouchableOpacity
                    style={[styles.button, styles.cancelButton]}
                    onPress={onCancel}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.cancelButtonText}>{cancelText}</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.button,
                      styles.confirmButton,
                      { backgroundColor: confirmColor },
                      isConfirmDisabled && styles.confirmButtonDisabled,
                    ]}
                    onPress={handleConfirm}
                    disabled={isConfirmDisabled}
                    activeOpacity={0.8}
                  >
                    <Text style={[
                      styles.confirmButtonText,
                      isConfirmDisabled && styles.confirmButtonTextDisabled,
                    ]}>
                      {confirmText}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </BlurView>
          </View>
        </BlurView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: width * 0.9,
    maxWidth: 400,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
  },
  modalBlur: {
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalContent: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: 20,
  },
  inputContainer: {
    marginBottom: 24,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: '#FFFFFF',
    minHeight: 48,
  },
  inputMultiline: {
    minHeight: 100,
    maxHeight: 150,
  },
  inputError: {
    borderColor: '#FF4757',
    backgroundColor: 'rgba(255, 71, 87, 0.1)',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 6,
  },
  errorText: {
    fontSize: 12,
    color: '#FF4757',
    flex: 1,
  },
  characterCount: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'right',
    marginTop: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  confirmButton: {
    // backgroundColor set dynamically
  },
  confirmButtonDisabled: {
    opacity: 0.5,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  confirmButtonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
});
