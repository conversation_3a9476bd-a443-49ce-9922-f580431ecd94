import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

export interface ActionModalAction {
  text: string;
  onPress: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
  color?: string;
  destructive?: boolean;
  disabled?: boolean;
}

interface ActionModalProps {
  visible: boolean;
  title: string;
  message?: string;
  actions: ActionModalAction[];
  onCancel: () => void;
  cancelText?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
}

const { width, height } = Dimensions.get('window');

export default function ActionModal({
  visible,
  title,
  message,
  actions,
  onCancel,
  cancelText = 'Cancel',
  icon,
  iconColor = '#00D4FF',
}: ActionModalProps) {
  const renderAction = (action: ActionModalAction, index: number) => {
    const actionColor = action.destructive 
      ? '#FF4757' 
      : action.color || '#00D4FF';
    
    const textColor = action.disabled 
      ? 'rgba(255, 255, 255, 0.3)'
      : action.destructive 
        ? '#FF4757'
        : action.color || '#00D4FF';

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.actionButton,
          action.disabled && styles.actionButtonDisabled,
        ]}
        onPress={action.onPress}
        disabled={action.disabled}
        activeOpacity={0.7}
      >
        <View style={styles.actionContent}>
          {action.icon && (
            <View style={[
              styles.actionIconContainer,
              { backgroundColor: `${actionColor}20` }
            ]}>
              <Ionicons 
                name={action.icon} 
                size={20} 
                color={actionColor} 
              />
            </View>
          )}
          
          <Text style={[styles.actionText, { color: textColor }]}>
            {action.text}
          </Text>
          
          <Ionicons 
            name="chevron-forward" 
            size={16} 
            color="rgba(255, 255, 255, 0.3)" 
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      statusBarTranslucent
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <BlurView intensity={20} style={styles.blurOverlay}>
          <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={1}
            onPress={onCancel}
          />
          
          <View style={styles.modalContainer}>
            <BlurView intensity={40} style={styles.modalBlur}>
              <View style={styles.modalContent}>
                {/* Header */}
                <View style={styles.header}>
                  {icon && (
                    <View style={[styles.iconContainer, { backgroundColor: `${iconColor}20` }]}>
                      <Ionicons name={icon} size={28} color={iconColor} />
                    </View>
                  )}
                  
                  <View style={styles.headerText}>
                    <Text style={styles.title}>{title}</Text>
                    {message && (
                      <Text style={styles.message}>{message}</Text>
                    )}
                  </View>
                </View>

                {/* Actions */}
                <ScrollView 
                  style={styles.actionsContainer}
                  showsVerticalScrollIndicator={false}
                >
                  {actions.map(renderAction)}
                </ScrollView>

                {/* Cancel Button */}
                <View style={styles.footer}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={onCancel}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.cancelButtonText}>{cancelText}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </BlurView>
          </View>
        </BlurView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backdrop: {
    flex: 1,
  },
  modalContainer: {
    maxHeight: height * 0.8,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -5 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
  },
  modalBlur: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderWidth: 1,
    borderBottomWidth: 0,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalContent: {
    padding: 20,
    paddingBottom: 40, // Extra padding for safe area
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: 20,
  },
  actionsContainer: {
    maxHeight: height * 0.4,
    marginBottom: 20,
  },
  actionButton: {
    marginBottom: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  actionIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
  },
  cancelButton: {
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    minWidth: 120,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
  },
});
