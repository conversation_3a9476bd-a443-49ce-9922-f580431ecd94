import { useState, useEffect, useCallback } from 'react';
import SessionManager, { 
  GameSession, 
  Player, 
  SessionSettings, 
  SessionInvite 
} from '../services/game/SessionManager';
import { GameInfo } from '../services/game/GameDetector';

interface UseSessionManagerReturn {
  currentSession: GameSession | null;
  activeSessions: GameSession[];
  sessionHistory: GameSession[];
  loading: boolean;
  error: string | null;
  createSession: (game: GameInfo, host: Player, settings?: Partial<SessionSettings>) => Promise<GameSession>;
  joinSession: (sessionId: string, player: Player) => Promise<boolean>;
  leaveSession: (sessionId: string, playerId: string) => Promise<boolean>;
  startSession: (sessionId: string) => Promise<boolean>;
  endSession: (sessionId: string) => Promise<boolean>;
  setPlayerReady: (sessionId: string, playerId: string, ready: boolean) => Promise<boolean>;
  updateSessionSettings: (sessionId: string, settings: Partial<SessionSettings>) => Promise<boolean>;
  generateInvite: (sessionId: string, expirationMinutes?: number) => Promise<SessionInvite>;
  joinFromInvite: (inviteData: string, player: Player) => Promise<boolean>;
  kickPlayer: (sessionId: string, playerId: string, hostId: string) => Promise<boolean>;
  pauseSession: (sessionId: string) => Promise<boolean>;
  resumeSession: (sessionId: string) => Promise<boolean>;
}

export default function useSessionManager(): UseSessionManagerReturn {
  const [currentSession, setCurrentSession] = useState<GameSession | null>(null);
  const [activeSessions, setActiveSessions] = useState<GameSession[]>([]);
  const [sessionHistory, setSessionHistory] = useState<GameSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateSessionData = useCallback(() => {
    setCurrentSession(SessionManager.getCurrentSession());
    setActiveSessions(SessionManager.getActiveSessions());
    setSessionHistory(SessionManager.getSessionHistory());
  }, []);

  const createSession = useCallback(async (
    game: GameInfo, 
    host: Player, 
    settings?: Partial<SessionSettings>
  ): Promise<GameSession> => {
    try {
      setLoading(true);
      setError(null);
      
      const session = await SessionManager.createSession(game, host, settings);
      updateSessionData();
      
      return session;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create session';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [updateSessionData]);

  const joinSession = useCallback(async (sessionId: string, player: Player): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await SessionManager.joinSession(sessionId, player);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to join session';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [updateSessionData]);

  const leaveSession = useCallback(async (sessionId: string, playerId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await SessionManager.leaveSession(sessionId, playerId);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to leave session';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [updateSessionData]);

  const startSession = useCallback(async (sessionId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await SessionManager.startSession(sessionId);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start session';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [updateSessionData]);

  const endSession = useCallback(async (sessionId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await SessionManager.endSession(sessionId);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to end session';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [updateSessionData]);

  const setPlayerReady = useCallback(async (
    sessionId: string, 
    playerId: string, 
    ready: boolean
  ): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await SessionManager.setPlayerReady(sessionId, playerId, ready);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update ready status';
      setError(errorMessage);
      return false;
    }
  }, [updateSessionData]);

  const updateSessionSettings = useCallback(async (
    sessionId: string, 
    settings: Partial<SessionSettings>
  ): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await SessionManager.updateSessionSettings(sessionId, settings);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update session settings';
      setError(errorMessage);
      return false;
    }
  }, [updateSessionData]);

  const generateInvite = useCallback(async (
    sessionId: string, 
    expirationMinutes?: number
  ): Promise<SessionInvite> => {
    try {
      setError(null);
      
      const invite = await SessionManager.generateInvite(sessionId, expirationMinutes);
      return invite;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate invite';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const joinFromInvite = useCallback(async (inviteData: string, player: Player): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await SessionManager.joinSessionFromInvite(inviteData, player);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to join from invite';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [updateSessionData]);

  const kickPlayer = useCallback(async (
    sessionId: string, 
    playerId: string, 
    hostId: string
  ): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await SessionManager.kickPlayer(sessionId, playerId, hostId);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to kick player';
      setError(errorMessage);
      return false;
    }
  }, [updateSessionData]);

  const pauseSession = useCallback(async (sessionId: string): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await SessionManager.pauseSession(sessionId);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pause session';
      setError(errorMessage);
      return false;
    }
  }, [updateSessionData]);

  const resumeSession = useCallback(async (sessionId: string): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await SessionManager.resumeSession(sessionId);
      updateSessionData();
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to resume session';
      setError(errorMessage);
      return false;
    }
  }, [updateSessionData]);

  // Set up event listeners
  useEffect(() => {
    const handleSessionCreated = (session: GameSession) => {
      updateSessionData();
    };

    const handlePlayerJoined = ({ session, player }: { session: GameSession; player: Player }) => {
      updateSessionData();
    };

    const handlePlayerLeft = ({ session, player }: { session: GameSession; player: Player }) => {
      updateSessionData();
    };

    const handleSessionStarted = (session: GameSession) => {
      updateSessionData();
    };

    const handleSessionEnded = (session: GameSession) => {
      updateSessionData();
    };

    const handlePlayerReadyChanged = ({ session, player, ready }: { 
      session: GameSession; 
      player: Player; 
      ready: boolean;
    }) => {
      updateSessionData();
    };

    const handleSessionSettingsUpdated = ({ session, settings }: { 
      session: GameSession; 
      settings: Partial<SessionSettings>;
    }) => {
      updateSessionData();
    };

    // Add event listeners
    SessionManager.on('sessionCreated', handleSessionCreated);
    SessionManager.on('playerJoined', handlePlayerJoined);
    SessionManager.on('playerLeft', handlePlayerLeft);
    SessionManager.on('sessionStarted', handleSessionStarted);
    SessionManager.on('sessionEnded', handleSessionEnded);
    SessionManager.on('playerReadyChanged', handlePlayerReadyChanged);
    SessionManager.on('sessionSettingsUpdated', handleSessionSettingsUpdated);

    // Initial data load
    updateSessionData();

    // Cleanup
    return () => {
      SessionManager.off('sessionCreated', handleSessionCreated);
      SessionManager.off('playerJoined', handlePlayerJoined);
      SessionManager.off('playerLeft', handlePlayerLeft);
      SessionManager.off('sessionStarted', handleSessionStarted);
      SessionManager.off('sessionEnded', handleSessionEnded);
      SessionManager.off('playerReadyChanged', handlePlayerReadyChanged);
      SessionManager.off('sessionSettingsUpdated', handleSessionSettingsUpdated);
    };
  }, [updateSessionData]);

  return {
    currentSession,
    activeSessions,
    sessionHistory,
    loading,
    error,
    createSession,
    joinSession,
    leaveSession,
    startSession,
    endSession,
    setPlayerReady,
    updateSessionSettings,
    generateInvite,
    joinFromInvite,
    kickPlayer,
    pauseSession,
    resumeSession,
  };
}
