import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SafeIconProps {
  name: keyof typeof Ionicons.glyphMap;
  size?: number;
  color?: string;
  style?: ViewStyle;
  fallbackText?: string;
  testID?: string;
}

/**
 * SafeIcon component that provides fallback rendering when vector icons fail to load
 * This is particularly useful for Android builds where icon bundling might fail
 */
export default function SafeIcon({
  name,
  size = 24,
  color = '#FFFFFF',
  style,
  fallbackText,
  testID,
}: SafeIconProps) {
  const [iconError, setIconError] = React.useState(false);

  // Reset error state when icon name changes
  React.useEffect(() => {
    setIconError(false);
  }, [name]);

  // Fallback text based on icon name if not provided
  const getFallbackText = () => {
    if (fallbackText) return fallbackText;
    
    // Common icon mappings to text
    const iconTextMap: Record<string, string> = {
      'home': '🏠',
      'home-outline': '🏠',
      'game-controller': '🎮',
      'game-controller-outline': '🎮',
      'wifi': '📶',
      'wifi-outline': '📶',
      'settings': '⚙️',
      'settings-outline': '⚙️',
      'close': '✕',
      'checkmark': '✓',
      'arrow-forward': '→',
      'arrow-back': '←',
      'qr-code': '⬜',
      'camera': '📷',
      'flash': '⚡',
      'flash-off': '⚡',
      'refresh': '↻',
      'share-outline': '📤',
      'information-circle': 'ℹ️',
      'play-circle': '▶️',
      'medical': '🏥',
      'phone-portrait': '📱',
      'laptop': '💻',
      'device-desktop': '🖥️',
      'cellular-outline': '📶',
      'wifi-off': '📵',
    };

    return iconTextMap[name] || '?';
  };

  const handleIconError = () => {
    console.warn(`SafeIcon: Failed to render icon "${name}", using fallback`);
    setIconError(true);
  };

  if (iconError) {
    return (
      <View
        style={[
          styles.fallbackContainer,
          {
            width: size,
            height: size,
          },
          style,
        ]}
        testID={testID ? `${testID}-fallback` : undefined}
      >
        <Text
          style={[
            styles.fallbackText,
            {
              fontSize: size * 0.8,
              color,
            },
          ]}
        >
          {getFallbackText()}
        </Text>
      </View>
    );
  }

  try {
    return (
      <Ionicons
        name={name}
        size={size}
        color={color}
        style={style}
        testID={testID}
        onError={handleIconError}
      />
    );
  } catch (error) {
    console.warn(`SafeIcon: Error rendering icon "${name}":`, error);
    return (
      <View
        style={[
          styles.fallbackContainer,
          {
            width: size,
            height: size,
          },
          style,
        ]}
        testID={testID ? `${testID}-fallback` : undefined}
      >
        <Text
          style={[
            styles.fallbackText,
            {
              fontSize: size * 0.8,
              color,
            },
          ]}
        >
          {getFallbackText()}
        </Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  fallbackContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
  },
  fallbackText: {
    textAlign: 'center',
    fontWeight: 'bold',
  },
});
