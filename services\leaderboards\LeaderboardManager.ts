import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from '../../utils/EventEmitter';

export interface PlayerScore {
  playerId: string;
  playerName: string;
  avatar?: string;
  score: number;
  rank: number;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  totalPlaytime: number;
  achievements: string[];
  lastActive: number;
  deviceType: string;
}

export interface LeaderboardEntry {
  playerId: string;
  playerName: string;
  avatar?: string;
  score: number;
  rank: number;
  change: number; // Position change from last update
  trend: 'up' | 'down' | 'same';
  badges: string[];
  stats: PlayerStats;
}

export interface PlayerStats {
  gamesPlayed: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  totalPlaytime: number;
  averageSessionLength: number;
  longestSession: number;
  favoriteGame?: string;
  achievementCount: number;
  connectionsMade: number;
  sessionsHosted: number;
  lastActive: number;
}

export interface Leaderboard {
  id: string;
  name: string;
  description: string;
  type: LeaderboardType;
  category: LeaderboardCategory;
  timeframe: LeaderboardTimeframe;
  entries: LeaderboardEntry[];
  lastUpdated: number;
  totalPlayers: number;
  isActive: boolean;
}

export type LeaderboardType = 'global' | 'local' | 'friends' | 'game_specific';

export type LeaderboardCategory =
  | 'overall'
  | 'wins'
  | 'playtime'
  | 'achievements'
  | 'connections'
  | 'hosting';

export type LeaderboardTimeframe = 'all_time' | 'monthly' | 'weekly' | 'daily';

class LeaderboardManager extends EventEmitter {
  private static instance: LeaderboardManager;
  private isInitialized: boolean = false;
  private leaderboards: Map<string, Leaderboard> = new Map();
  private playerScores: Map<string, PlayerScore> = new Map();
  private currentPlayerId: string = '';

  private constructor() {
    super();
  }

  static getInstance(): LeaderboardManager {
    if (!LeaderboardManager.instance) {
      LeaderboardManager.instance = new LeaderboardManager();
    }
    return LeaderboardManager.instance;
  }

  async initialize(playerId: string): Promise<void> {
    try {
      console.log('Initializing Leaderboard Manager...');

      this.currentPlayerId = playerId;

      // Load leaderboard data
      await this.loadLeaderboards();
      await this.loadPlayerScores();

      // Initialize default leaderboards
      this.createDefaultLeaderboards();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('Leaderboard Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Leaderboard Manager:', error);
      throw error;
    }
  }

  private async loadLeaderboards(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('leaderboards');
      if (stored) {
        const data = JSON.parse(stored);
        data.forEach((leaderboard: Leaderboard) => {
          this.leaderboards.set(leaderboard.id, leaderboard);
        });
      }
    } catch (error) {
      console.error('Failed to load leaderboards:', error);
    }
  }

  private async loadPlayerScores(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('player_scores');
      if (stored) {
        const data = JSON.parse(stored);
        data.forEach((score: PlayerScore) => {
          this.playerScores.set(score.playerId, score);
        });
      }
    } catch (error) {
      console.error('Failed to load player scores:', error);
    }
  }

  private async saveLeaderboards(): Promise<void> {
    try {
      const data = Array.from(this.leaderboards.values());
      await AsyncStorage.setItem('leaderboards', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save leaderboards:', error);
    }
  }

  private async savePlayerScores(): Promise<void> {
    try {
      const data = Array.from(this.playerScores.values());
      await AsyncStorage.setItem('player_scores', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save player scores:', error);
    }
  }

  private createDefaultLeaderboards(): void {
    const defaultLeaderboards: Omit<Leaderboard, 'entries' | 'lastUpdated' | 'totalPlayers'>[] = [
      {
        id: 'global_overall',
        name: 'Global Champions',
        description: 'Top players worldwide based on overall performance',
        type: 'global',
        category: 'overall',
        timeframe: 'all_time',
        isActive: true,
      },
      {
        id: 'local_wins',
        name: 'Local Winners',
        description: 'Most wins in your area',
        type: 'local',
        category: 'wins',
        timeframe: 'monthly',
        isActive: true,
      },
      {
        id: 'global_playtime',
        name: 'Marathon Gamers',
        description: 'Players with the most total playtime',
        type: 'global',
        category: 'playtime',
        timeframe: 'all_time',
        isActive: true,
      },
      {
        id: 'weekly_achievements',
        name: 'Achievement Hunters',
        description: 'Most achievements unlocked this week',
        type: 'global',
        category: 'achievements',
        timeframe: 'weekly',
        isActive: true,
      },
      {
        id: 'connection_masters',
        name: 'Connection Masters',
        description: 'Players who connect with the most devices',
        type: 'global',
        category: 'connections',
        timeframe: 'monthly',
        isActive: true,
      },
      {
        id: 'session_hosts',
        name: 'Session Hosts',
        description: 'Players who host the most gaming sessions',
        type: 'global',
        category: 'hosting',
        timeframe: 'monthly',
        isActive: true,
      },
    ];

    defaultLeaderboards.forEach(leaderboard => {
      if (!this.leaderboards.has(leaderboard.id)) {
        const fullLeaderboard: Leaderboard = {
          ...leaderboard,
          entries: [],
          lastUpdated: Date.now(),
          totalPlayers: 0,
        };
        this.leaderboards.set(leaderboard.id, fullLeaderboard);
      }
    });
  }

  // Update player score and recalculate rankings
  async updatePlayerScore(
    playerId: string,
    playerName: string,
    stats: Partial<PlayerStats>,
    avatar?: string,
  ): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const existingScore = this.playerScores.get(playerId);

      const updatedScore: PlayerScore = {
        playerId,
        playerName,
        avatar,
        score: this.calculateOverallScore(stats),
        rank: 0, // Will be calculated during ranking
        gamesPlayed: stats.gamesPlayed || existingScore?.gamesPlayed || 0,
        gamesWon: stats.gamesWon || existingScore?.gamesWon || 0,
        winRate: this.calculateWinRate(stats.gamesWon || 0, stats.gamesPlayed || 0),
        totalPlaytime: stats.totalPlaytime || existingScore?.totalPlaytime || 0,
        achievements: existingScore?.achievements || [],
        lastActive: Date.now(),
        deviceType: existingScore?.deviceType || 'unknown',
      };

      this.playerScores.set(playerId, updatedScore);

      // Update all relevant leaderboards
      await this.updateAllLeaderboards();

      await this.savePlayerScores();

      this.emit('playerScoreUpdated', updatedScore);
    } catch (error) {
      console.error('Failed to update player score:', error);
    }
  }

  private calculateOverallScore(stats: Partial<PlayerStats>): number {
    const weights = {
      gamesWon: 100,
      winRate: 50,
      playtime: 0.01, // 1 point per 100 minutes
      achievements: 25,
      connections: 10,
      sessionsHosted: 20,
    };

    let score = 0;
    score += (stats.gamesWon || 0) * weights.gamesWon;
    score += this.calculateWinRate(stats.gamesWon || 0, stats.gamesPlayed || 0) * weights.winRate;
    score += (stats.totalPlaytime || 0) * weights.playtime;
    score += (stats.achievementCount || 0) * weights.achievements;
    score += (stats.connectionsMade || 0) * weights.connections;
    score += (stats.sessionsHosted || 0) * weights.sessionsHosted;

    return Math.round(score);
  }

  private calculateWinRate(wins: number, totalGames: number): number {
    if (totalGames === 0) return 0;
    return Math.round((wins / totalGames) * 100);
  }

  private async updateAllLeaderboards(): Promise<void> {
    for (const leaderboard of this.leaderboards.values()) {
      await this.updateLeaderboard(leaderboard.id);
    }
  }

  private async updateLeaderboard(leaderboardId: string): Promise<void> {
    const leaderboard = this.leaderboards.get(leaderboardId);
    if (!leaderboard) return;

    // Get relevant players based on leaderboard criteria
    const relevantPlayers = this.getRelevantPlayers(leaderboard);

    // Sort players based on leaderboard category
    const sortedPlayers = this.sortPlayersByCategory(relevantPlayers, leaderboard.category);

    // Create leaderboard entries with rankings
    const entries: LeaderboardEntry[] = sortedPlayers.map((player, index) => {
      const previousEntry = leaderboard.entries.find(e => e.playerId === player.playerId);
      const previousRank = previousEntry?.rank || index + 1;
      const currentRank = index + 1;
      const change = previousRank - currentRank;

      return {
        playerId: player.playerId,
        playerName: player.playerName,
        avatar: player.avatar,
        score: this.getScoreForCategory(player, leaderboard.category),
        rank: currentRank,
        change,
        trend: change > 0 ? 'up' : change < 0 ? 'down' : 'same',
        badges: this.getPlayerBadges(player),
        stats: this.getPlayerStatsFromScore(player),
      };
    });

    // Update leaderboard
    leaderboard.entries = entries;
    leaderboard.lastUpdated = Date.now();
    leaderboard.totalPlayers = entries.length;

    this.leaderboards.set(leaderboardId, leaderboard);
    await this.saveLeaderboards();

    this.emit('leaderboardUpdated', leaderboard);
  }

  private getRelevantPlayers(leaderboard: Leaderboard): PlayerScore[] {
    let players = Array.from(this.playerScores.values());

    // Filter by timeframe
    if (leaderboard.timeframe !== 'all_time') {
      const timeLimit = this.getTimeLimit(leaderboard.timeframe);
      players = players.filter(player => player.lastActive >= timeLimit);
    }

    // Filter by type (for now, all players are considered for all types)
    // In a real implementation, you might filter by location for 'local' type

    return players;
  }

  private getTimeLimit(timeframe: LeaderboardTimeframe): number {
    const now = Date.now();
    switch (timeframe) {
      case 'daily':
        return now - 24 * 60 * 60 * 1000;
      case 'weekly':
        return now - 7 * 24 * 60 * 60 * 1000;
      case 'monthly':
        return now - 30 * 24 * 60 * 60 * 1000;
      default:
        return 0;
    }
  }

  private sortPlayersByCategory(
    players: PlayerScore[],
    category: LeaderboardCategory,
  ): PlayerScore[] {
    return players.sort((a, b) => {
      switch (category) {
        case 'overall':
          return b.score - a.score;
        case 'wins':
          return b.gamesWon - a.gamesWon;
        case 'playtime':
          return b.totalPlaytime - a.totalPlaytime;
        case 'achievements':
          return b.achievements.length - a.achievements.length;
        case 'connections':
          // This would need to be tracked separately
          return b.score - a.score;
        case 'hosting':
          // This would need to be tracked separately
          return b.score - a.score;
        default:
          return b.score - a.score;
      }
    });
  }

  private getScoreForCategory(player: PlayerScore, category: LeaderboardCategory): number {
    switch (category) {
      case 'overall':
        return player.score;
      case 'wins':
        return player.gamesWon;
      case 'playtime':
        return Math.round(player.totalPlaytime / 60000); // Convert to minutes
      case 'achievements':
        return player.achievements.length;
      case 'connections':
        return player.score; // Placeholder
      case 'hosting':
        return player.score; // Placeholder
      default:
        return player.score;
    }
  }

  private getPlayerBadges(player: PlayerScore): string[] {
    const badges: string[] = [];

    if (player.gamesWon >= 10) badges.push('golden_winner');
    if (player.gamesWon >= 5 && player.gamesWon < 10) badges.push('silver_achiever');
    if (player.winRate >= 80) badges.push('champion');
    if (player.totalPlaytime >= 5 * 60 * 60 * 1000) badges.push('marathon_gamer');
    if (player.achievements.length >= 10) badges.push('achievement_hunter');

    return badges;
  }

  private getPlayerStatsFromScore(player: PlayerScore): PlayerStats {
    return {
      gamesPlayed: player.gamesPlayed,
      gamesWon: player.gamesWon,
      gamesLost: player.gamesPlayed - player.gamesWon,
      winRate: player.winRate,
      totalPlaytime: player.totalPlaytime,
      averageSessionLength: player.gamesPlayed > 0 ? player.totalPlaytime / player.gamesPlayed : 0,
      longestSession: 0, // Would need to be tracked separately
      achievementCount: player.achievements.length,
      connectionsMade: 0, // Would need to be tracked separately
      sessionsHosted: 0, // Would need to be tracked separately
      lastActive: player.lastActive,
    };
  }

  // Public API methods
  getLeaderboard(leaderboardId: string): Leaderboard | null {
    return this.leaderboards.get(leaderboardId) || null;
  }

  getAllLeaderboards(): Leaderboard[] {
    return Array.from(this.leaderboards.values());
  }

  getActiveLeaderboards(): Leaderboard[] {
    return this.getAllLeaderboards().filter(lb => lb.isActive);
  }

  getPlayerRank(playerId: string, leaderboardId: string): number {
    const leaderboard = this.leaderboards.get(leaderboardId);
    if (!leaderboard) return 0;

    const entry = leaderboard.entries.find(e => e.playerId === playerId);
    return entry?.rank || 0;
  }

  getPlayerScore(playerId: string): PlayerScore | null {
    return this.playerScores.get(playerId) || null;
  }

  getCurrentPlayerRankings(): { leaderboardId: string; rank: number; total: number }[] {
    return this.getActiveLeaderboards().map(leaderboard => ({
      leaderboardId: leaderboard.id,
      rank: this.getPlayerRank(this.currentPlayerId, leaderboard.id),
      total: leaderboard.totalPlayers,
    }));
  }

  getTopPlayers(leaderboardId: string, limit: number = 10): LeaderboardEntry[] {
    const leaderboard = this.leaderboards.get(leaderboardId);
    if (!leaderboard) return [];

    return leaderboard.entries.slice(0, limit);
  }

  // Convenience methods for tracking specific achievements
  async trackGameWin(
    playerId: string,
    playerName: string,
    gameId: string,
    sessionDuration: number,
  ): Promise<void> {
    const existingScore = this.playerScores.get(playerId);
    const stats: Partial<PlayerStats> = {
      gamesPlayed: (existingScore?.gamesPlayed || 0) + 1,
      gamesWon: (existingScore?.gamesWon || 0) + 1,
      totalPlaytime: (existingScore?.totalPlaytime || 0) + sessionDuration,
    };

    await this.updatePlayerScore(playerId, playerName, stats);
  }

  async trackGameLoss(
    playerId: string,
    playerName: string,
    gameId: string,
    sessionDuration: number,
  ): Promise<void> {
    const existingScore = this.playerScores.get(playerId);
    const stats: Partial<PlayerStats> = {
      gamesPlayed: (existingScore?.gamesPlayed || 0) + 1,
      gamesWon: existingScore?.gamesWon || 0,
      totalPlaytime: (existingScore?.totalPlaytime || 0) + sessionDuration,
    };

    await this.updatePlayerScore(playerId, playerName, stats);
  }

  async trackAchievementUnlocked(playerId: string, achievementId: string): Promise<void> {
    const existingScore = this.playerScores.get(playerId);
    if (existingScore && !existingScore.achievements.includes(achievementId)) {
      existingScore.achievements.push(achievementId);
      this.playerScores.set(playerId, existingScore);
      await this.updateAllLeaderboards();
      await this.savePlayerScores();
    }
  }
}

export default LeaderboardManager.getInstance();
