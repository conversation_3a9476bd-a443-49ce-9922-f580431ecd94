import { Platform, AppState, AppStateStatus } from 'react-native';
import { EventEmitter } from 'events';
import ConnectionManager from '../networking/ConnectionManager';
import WebRTCManager from '../networking/WebRTCManager';

export interface BackgroundTask {
  id: string;
  type: 'connection_monitor' | 'heartbeat' | 'reconnection' | 'data_sync';
  priority: 'high' | 'medium' | 'low';
  interval: number;
  lastRun: number;
  isRunning: boolean;
  maxExecutionTime: number;
}

export interface ConnectionHealth {
  deviceId: string;
  connectionId: string;
  status: 'healthy' | 'degraded' | 'critical' | 'disconnected';
  latency: number;
  packetLoss: number;
  lastSeen: number;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}

export interface BackgroundServiceConfig {
  enableBackgroundExecution: boolean;
  heartbeatInterval: number;
  reconnectionTimeout: number;
  maxReconnectionAttempts: number;
  batteryOptimization: boolean;
  connectionHealthThreshold: {
    latency: number;
    packetLoss: number;
    timeout: number;
  };
}

class ConnectionService extends EventEmitter {
  private static instance: ConnectionService;
  private isActive: boolean = false;
  private appState: AppStateStatus = 'active';
  private backgroundTasks: Map<string, BackgroundTask> = new Map();
  private connectionHealth: Map<string, ConnectionHealth> = new Map();
  private connectionManager: ConnectionManager;
  private webrtcManager: WebRTCManager;
  private config: BackgroundServiceConfig;
  private backgroundTaskId: number | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectionQueue: string[] = [];

  private constructor() {
    super();
    this.connectionManager = ConnectionManager.getInstance();
    this.webrtcManager = WebRTCManager.getInstance();
    this.config = {
      enableBackgroundExecution: true,
      heartbeatInterval: 5000,
      reconnectionTimeout: 30000,
      maxReconnectionAttempts: 5,
      batteryOptimization: true,
      connectionHealthThreshold: {
        latency: 1000,
        packetLoss: 0.1,
        timeout: 30000
      }
    };
    this.setupAppStateHandling();
  }

  static getInstance(): ConnectionService {
    if (!ConnectionService.instance) {
      ConnectionService.instance = new ConnectionService();
    }
    return ConnectionService.instance;
  }

  // Initialization
  async initialize(config?: Partial<BackgroundServiceConfig>): Promise<void> {
    try {
      console.log('Initializing Connection Service...');

      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Setup platform-specific background execution
      await this.setupBackgroundExecution();

      // Initialize background tasks
      this.initializeBackgroundTasks();

      // Start monitoring
      this.startConnectionMonitoring();

      this.isActive = true;
      this.emit('initialized');
      console.log('Connection Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Connection Service:', error);
      throw error;
    }
  }

  private async setupBackgroundExecution(): Promise<void> {
    if (Platform.OS === 'android') {
      await this.setupAndroidBackgroundExecution();
    } else if (Platform.OS === 'ios') {
      await this.setupiOSBackgroundExecution();
    }
  }

  private async setupAndroidBackgroundExecution(): Promise<void> {
    // Android background service setup
    console.log('Setting up Android background execution...');
    // This would integrate with native Android background service
  }

  private async setupiOSBackgroundExecution(): Promise<void> {
    // iOS background app refresh setup
    console.log('Setting up iOS background execution...');
    // This would integrate with iOS background app refresh
  }

  private setupAppStateHandling(): void {
    AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  private handleAppStateChange(nextAppState: AppStateStatus): void {
    console.log(`App state changed from ${this.appState} to ${nextAppState}`);
    
    if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
      // App going to background
      this.onAppBackground();
    } else if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
      // App coming to foreground
      this.onAppForeground();
    }
    
    this.appState = nextAppState;
  }

  private async onAppBackground(): Promise<void> {
    console.log('App entering background, starting background tasks...');
    
    if (this.config.enableBackgroundExecution) {
      await this.startBackgroundTasks();
    }
    
    this.emit('appBackground');
  }

  private async onAppForeground(): Promise<void> {
    console.log('App entering foreground, resuming normal operation...');
    
    await this.stopBackgroundTasks();
    await this.performConnectionHealthCheck();
    
    this.emit('appForeground');
  }

  // Background Task Management
  private initializeBackgroundTasks(): void {
    const tasks: BackgroundTask[] = [
      {
        id: 'connection_monitor',
        type: 'connection_monitor',
        priority: 'high',
        interval: this.config.heartbeatInterval,
        lastRun: 0,
        isRunning: false,
        maxExecutionTime: 5000
      },
      {
        id: 'heartbeat',
        type: 'heartbeat',
        priority: 'medium',
        interval: this.config.heartbeatInterval * 2,
        lastRun: 0,
        isRunning: false,
        maxExecutionTime: 3000
      },
      {
        id: 'reconnection',
        type: 'reconnection',
        priority: 'high',
        interval: this.config.reconnectionTimeout,
        lastRun: 0,
        isRunning: false,
        maxExecutionTime: 10000
      }
    ];

    tasks.forEach(task => this.backgroundTasks.set(task.id, task));
  }

  private async startBackgroundTasks(): Promise<void> {
    if (Platform.OS === 'ios') {
      // iOS background task registration
      this.backgroundTaskId = await this.registerBackgroundTask();
    }

    // Start task execution
    for (const task of this.backgroundTasks.values()) {
      if (!task.isRunning) {
        this.scheduleTask(task);
      }
    }
  }

  private async stopBackgroundTasks(): Promise<void> {
    // Stop all background tasks
    for (const task of this.backgroundTasks.values()) {
      task.isRunning = false;
    }

    if (this.backgroundTaskId !== null && Platform.OS === 'ios') {
      await this.endBackgroundTask(this.backgroundTaskId);
      this.backgroundTaskId = null;
    }
  }

  private async registerBackgroundTask(): Promise<number> {
    // iOS background task registration
    console.log('Registering iOS background task...');
    return 1; // Mock task ID
  }

  private async endBackgroundTask(taskId: number): Promise<void> {
    // iOS background task cleanup
    console.log(`Ending iOS background task ${taskId}...`);
  }

  private scheduleTask(task: BackgroundTask): void {
    const executeTask = async () => {
      if (!task.isRunning || !this.isActive) return;

      const startTime = Date.now();
      
      try {
        await this.executeBackgroundTask(task);
        task.lastRun = Date.now();
      } catch (error) {
        console.error(`Background task ${task.id} failed:`, error);
      }

      const executionTime = Date.now() - startTime;
      if (executionTime > task.maxExecutionTime) {
        console.warn(`Background task ${task.id} exceeded max execution time: ${executionTime}ms`);
      }

      // Schedule next execution
      if (task.isRunning) {
        setTimeout(executeTask, task.interval);
      }
    };

    task.isRunning = true;
    setTimeout(executeTask, task.interval);
  }

  private async executeBackgroundTask(task: BackgroundTask): Promise<void> {
    switch (task.type) {
      case 'connection_monitor':
        await this.monitorConnections();
        break;
      case 'heartbeat':
        await this.sendHeartbeats();
        break;
      case 'reconnection':
        await this.processReconnectionQueue();
        break;
      case 'data_sync':
        await this.syncPendingData();
        break;
    }
  }

  // Connection Monitoring
  private startConnectionMonitoring(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.appState === 'active') {
        this.performConnectionHealthCheck();
      }
    }, this.config.heartbeatInterval);
  }

  private async performConnectionHealthCheck(): Promise<void> {
    try {
      const connectedDevices = this.connectionManager.getConnectedDevices();
      
      for (const device of connectedDevices) {
        await this.checkConnectionHealth(device.id);
      }
    } catch (error) {
      console.error('Connection health check failed:', error);
    }
  }

  private async checkConnectionHealth(deviceId: string): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Send ping to measure latency
      const pingResult = await this.pingDevice(deviceId);
      const latency = Date.now() - startTime;

      let health = this.connectionHealth.get(deviceId);
      if (!health) {
        health = {
          deviceId,
          connectionId: '',
          status: 'healthy',
          latency: 0,
          packetLoss: 0,
          lastSeen: Date.now(),
          reconnectAttempts: 0,
          maxReconnectAttempts: this.config.maxReconnectionAttempts
        };
        this.connectionHealth.set(deviceId, health);
      }

      // Update health metrics
      health.latency = latency;
      health.lastSeen = Date.now();

      // Determine health status
      if (pingResult) {
        health.status = latency > this.config.connectionHealthThreshold.latency ? 'degraded' : 'healthy';
        health.reconnectAttempts = 0;
      } else {
        health.status = 'critical';
        this.scheduleReconnection(deviceId);
      }

      this.emit('connectionHealthUpdated', health);
    } catch (error) {
      console.error(`Failed to check health for device ${deviceId}:`, error);
    }
  }

  private async pingDevice(deviceId: string): Promise<boolean> {
    try {
      // Send ping through WebRTC or other connection method
      return await this.webrtcManager.ping(deviceId);
    } catch (error) {
      return false;
    }
  }

  private async monitorConnections(): Promise<void> {
    const now = Date.now();
    
    for (const [deviceId, health] of this.connectionHealth) {
      if (now - health.lastSeen > this.config.connectionHealthThreshold.timeout) {
        console.warn(`Device ${deviceId} timed out`);
        health.status = 'disconnected';
        this.scheduleReconnection(deviceId);
      }
    }
  }

  private async sendHeartbeats(): Promise<void> {
    const connectedDevices = this.connectionManager.getConnectedDevices();
    
    for (const device of connectedDevices) {
      try {
        await this.webrtcManager.sendData(device.id, {
          type: 'heartbeat',
          timestamp: Date.now()
        });
      } catch (error) {
        console.error(`Failed to send heartbeat to ${device.id}:`, error);
      }
    }
  }

  // Reconnection Management
  private scheduleReconnection(deviceId: string): void {
    if (!this.reconnectionQueue.includes(deviceId)) {
      this.reconnectionQueue.push(deviceId);
      console.log(`Scheduled reconnection for device ${deviceId}`);
    }
  }

  private async processReconnectionQueue(): Promise<void> {
    while (this.reconnectionQueue.length > 0) {
      const deviceId = this.reconnectionQueue.shift();
      if (deviceId) {
        await this.attemptReconnection(deviceId);
      }
    }
  }

  private async attemptReconnection(deviceId: string): Promise<void> {
    try {
      const health = this.connectionHealth.get(deviceId);
      if (!health || health.reconnectAttempts >= health.maxReconnectAttempts) {
        console.warn(`Max reconnection attempts reached for device ${deviceId}`);
        return;
      }

      health.reconnectAttempts++;
      console.log(`Attempting reconnection to ${deviceId} (attempt ${health.reconnectAttempts})`);

      const success = await this.connectionManager.connectToDevice(deviceId);
      
      if (success) {
        health.status = 'healthy';
        health.reconnectAttempts = 0;
        console.log(`Successfully reconnected to device ${deviceId}`);
        this.emit('deviceReconnected', deviceId);
      } else {
        console.warn(`Reconnection failed for device ${deviceId}`);
        // Will retry on next cycle
        this.scheduleReconnection(deviceId);
      }
    } catch (error) {
      console.error(`Reconnection attempt failed for device ${deviceId}:`, error);
    }
  }

  private async syncPendingData(): Promise<void> {
    // Sync any pending data that couldn't be sent due to connection issues
    console.log('Syncing pending data...');
  }

  // Public API
  getConnectionHealth(deviceId: string): ConnectionHealth | undefined {
    return this.connectionHealth.get(deviceId);
  }

  getAllConnectionHealth(): ConnectionHealth[] {
    return Array.from(this.connectionHealth.values());
  }

  updateConfig(config: Partial<BackgroundServiceConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('Connection Service config updated');
  }

  // Cleanup
  destroy(): void {
    this.isActive = false;
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    this.stopBackgroundTasks();
    AppState.removeEventListener('change', this.handleAppStateChange.bind(this));
    this.removeAllListeners();
  }
}

export default ConnectionService;
