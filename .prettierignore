# Dependencies
node_modules/

# Build outputs
dist/
build/
web-build/
.expo/

# Coverage reports
coverage/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Generated files
*.tsbuildinfo
expo-env.d.ts

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Documentation that shouldn't be formatted
CHANGELOG.md
LICENSE
