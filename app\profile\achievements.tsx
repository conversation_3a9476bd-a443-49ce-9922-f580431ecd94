import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  FlatList,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import AchievementManager, { Achievement, AchievementCategory } from '../../services/achievements/AchievementManager';

const { width } = Dimensions.get('window');

export default function AchievementsScreen() {
  const userProfile = useSelector((state: RootState) => state.user.profile);
  
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<AchievementCategory | 'all'>('all');
  const [loading, setLoading] = useState(true);
  const [totalPoints, setTotalPoints] = useState(0);
  const [completionPercentage, setCompletionPercentage] = useState(0);

  const categories: { key: AchievementCategory | 'all'; name: string; icon: string }[] = [
    { key: 'all', name: 'All', icon: 'grid' },
    { key: 'connection', name: 'Connection', icon: 'link' },
    { key: 'gaming', name: 'Gaming', icon: 'game-controller' },
    { key: 'social', name: 'Social', icon: 'people' },
    { key: 'technical', name: 'Technical', icon: 'settings' },
    { key: 'milestone', name: 'Milestone', icon: 'trophy' },
    { key: 'special', name: 'Special', icon: 'star' },
  ];

  useEffect(() => {
    loadAchievements();
  }, []);

  const loadAchievements = async () => {
    try {
      setLoading(true);

      if (userProfile?.id) {
        await AchievementManager.initialize(userProfile.id);
        
        const allAchievements = AchievementManager.getAchievements();
        setAchievements(allAchievements);
        
        const points = AchievementManager.getTotalPoints();
        setTotalPoints(points);
        
        const completion = AchievementManager.getCompletionPercentage();
        setCompletionPercentage(completion);
      }
    } catch (error) {
      console.error('Failed to load achievements:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredAchievements = () => {
    if (selectedCategory === 'all') {
      return achievements;
    }
    return achievements.filter(achievement => achievement.category === selectedCategory);
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#CCCCCC';
      case 'uncommon': return '#00FF88';
      case 'rare': return '#00D4FF';
      case 'epic': return '#9D4EDD';
      case 'legendary': return '#FFD700';
      default: return '#CCCCCC';
    }
  };

  const getRarityGradient = (rarity: string) => {
    switch (rarity) {
      case 'common': return ['#CCCCCC', '#999999'];
      case 'uncommon': return ['#00FF88', '#00CC6A'];
      case 'rare': return ['#00D4FF', '#0099CC'];
      case 'epic': return ['#9D4EDD', '#7B2CBF'];
      case 'legendary': return ['#FFD700', '#FFA500'];
      default: return ['#CCCCCC', '#999999'];
    }
  };

  const CategoryButton = ({ category }: { category: typeof categories[0] }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        selectedCategory === category.key && styles.categoryButtonActive
      ]}
      onPress={() => setSelectedCategory(category.key)}
    >
      <BlurView intensity={15} style={styles.categoryBlur}>
        <Ionicons 
          name={category.icon as any} 
          size={20} 
          color={selectedCategory === category.key ? '#00D4FF' : '#CCCCCC'} 
        />
        <Text style={[
          styles.categoryText,
          selectedCategory === category.key && styles.categoryTextActive
        ]}>
          {category.name}
        </Text>
      </BlurView>
    </TouchableOpacity>
  );

  const AchievementCard = ({ achievement }: { achievement: Achievement }) => (
    <View style={styles.achievementCard}>
      <BlurView intensity={15} style={styles.achievementBlur}>
        <LinearGradient
          colors={getRarityGradient(achievement.rarity)}
          style={styles.achievementGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.achievementContent}>
            <View style={styles.achievementHeader}>
              <View style={[
                styles.achievementIcon,
                { backgroundColor: achievement.isUnlocked ? getRarityColor(achievement.rarity) : 'rgba(255,255,255,0.1)' }
              ]}>
                <Ionicons 
                  name={achievement.icon as any} 
                  size={24} 
                  color={achievement.isUnlocked ? '#000' : '#666'} 
                />
              </View>
              
              <View style={styles.achievementInfo}>
                <Text style={[
                  styles.achievementName,
                  !achievement.isUnlocked && styles.achievementNameLocked
                ]}>
                  {achievement.isHidden && !achievement.isUnlocked ? '???' : achievement.name}
                </Text>
                <Text style={[
                  styles.achievementDescription,
                  !achievement.isUnlocked && styles.achievementDescriptionLocked
                ]}>
                  {achievement.isHidden && !achievement.isUnlocked ? 'Hidden achievement' : achievement.description}
                </Text>
              </View>

              <View style={styles.achievementReward}>
                <Text style={styles.achievementPoints}>+{achievement.reward.points}</Text>
                <Text style={styles.achievementRarity}>{achievement.rarity}</Text>
              </View>
            </View>

            {/* Progress Bar */}
            {!achievement.isUnlocked && achievement.maxProgress > 1 && (
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill,
                      { 
                        width: `${(achievement.progress / achievement.maxProgress) * 100}%`,
                        backgroundColor: getRarityColor(achievement.rarity)
                      }
                    ]} 
                  />
                </View>
                <Text style={styles.progressText}>
                  {achievement.progress}/{achievement.maxProgress}
                </Text>
              </View>
            )}

            {/* Unlocked Date */}
            {achievement.isUnlocked && achievement.unlockedAt && (
              <View style={styles.unlockedContainer}>
                <Ionicons name="checkmark-circle" size={16} color="#00FF88" />
                <Text style={styles.unlockedText}>
                  Unlocked {new Date(achievement.unlockedAt).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </BlurView>
    </View>
  );

  const renderAchievement = ({ item }: { item: Achievement }) => (
    <AchievementCard achievement={item} />
  );

  const filteredAchievements = getFilteredAchievements();
  const unlockedCount = filteredAchievements.filter(a => a.isUnlocked).length;

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header Stats */}
        <View style={styles.header}>
          <BlurView intensity={15} style={styles.statsCard}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{totalPoints}</Text>
              <Text style={styles.statLabel}>Total Points</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{completionPercentage}%</Text>
              <Text style={styles.statLabel}>Complete</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{unlockedCount}/{filteredAchievements.length}</Text>
              <Text style={styles.statLabel}>Unlocked</Text>
            </View>
          </BlurView>
        </View>

        {/* Category Filter */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.categoryScroll}
          contentContainerStyle={styles.categoryContainer}
        >
          {categories.map((category) => (
            <CategoryButton key={category.key} category={category} />
          ))}
        </ScrollView>

        {/* Achievements List */}
        <FlatList
          data={filteredAchievements}
          renderItem={renderAchievement}
          keyExtractor={(item) => item.id}
          style={styles.achievementsList}
          contentContainerStyle={styles.achievementsContent}
          showsVerticalScrollIndicator={false}
          numColumns={1}
        />
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 100,
  },
  statsCard: {
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
    overflow: 'hidden',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  categoryScroll: {
    maxHeight: 60,
  },
  categoryContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  categoryButton: {
    marginRight: 12,
  },
  categoryButtonActive: {
    transform: [{ scale: 1.05 }],
  },
  categoryBlur: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  categoryText: {
    fontSize: 14,
    color: '#CCCCCC',
    marginLeft: 8,
  },
  categoryTextActive: {
    color: '#00D4FF',
    fontWeight: '600',
  },
  achievementsList: {
    flex: 1,
  },
  achievementsContent: {
    padding: 20,
  },
  achievementCard: {
    marginBottom: 16,
  },
  achievementBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  achievementGradient: {
    padding: 2,
  },
  achievementContent: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 14,
    padding: 16,
  },
  achievementHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementInfo: {
    flex: 1,
    marginLeft: 12,
  },
  achievementName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  achievementNameLocked: {
    color: '#CCCCCC',
  },
  achievementDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
    lineHeight: 20,
  },
  achievementDescriptionLocked: {
    color: '#888888',
  },
  achievementReward: {
    alignItems: 'flex-end',
  },
  achievementPoints: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFD700',
  },
  achievementRarity: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 2,
    textTransform: 'capitalize',
  },
  progressContainer: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#CCCCCC',
    marginLeft: 8,
    minWidth: 40,
    textAlign: 'right',
  },
  unlockedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  unlockedText: {
    fontSize: 12,
    color: '#00FF88',
    marginLeft: 4,
  },
});
