import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserPreferences {
  hasCompletedOnboarding: boolean;
  deviceName: string;
  userId: string;
  preferredConnectionType: 'wifi' | 'bluetooth' | 'auto';
  autoScanEnabled: boolean;
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  theme: 'dark' | 'light' | 'auto';
  language: string;
  lastAppVersion: string;
  firstLaunchDate: number;
  totalSessions: number;
  totalPlaytime: number; // in minutes
  favoriteGames: string[];
  recentConnections: string[];
}

const DEFAULT_PREFERENCES: UserPreferences = {
  hasCompletedOnboarding: false,
  deviceName: '',
  userId: '',
  preferredConnectionType: 'auto',
  autoScanEnabled: true,
  notificationsEnabled: true,
  soundEnabled: true,
  vibrationEnabled: true,
  theme: 'dark',
  language: 'en',
  lastAppVersion: '',
  firstLaunchDate: Date.now(),
  totalSessions: 0,
  totalPlaytime: 0,
  favoriteGames: [],
  recentConnections: [],
};

const STORAGE_KEY = '@logaco_user_preferences';

class UserPreferencesService {
  private static instance: UserPreferencesService;
  private preferences: UserPreferences = DEFAULT_PREFERENCES;
  private isLoaded = false;

  static getInstance(): UserPreferencesService {
    if (!UserPreferencesService.instance) {
      UserPreferencesService.instance = new UserPreferencesService();
    }
    return UserPreferencesService.instance;
  }

  async loadPreferences(): Promise<UserPreferences> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.preferences = { ...DEFAULT_PREFERENCES, ...parsed };
      } else {
        // First time user - generate user ID and device name
        this.preferences = {
          ...DEFAULT_PREFERENCES,
          userId: this.generateUserId(),
          deviceName: this.generateDeviceName(),
          firstLaunchDate: Date.now(),
        };
        await this.savePreferences();
      }
      this.isLoaded = true;
      return this.preferences;
    } catch (error) {
      console.error('Failed to load user preferences:', error);
      this.preferences = DEFAULT_PREFERENCES;
      this.isLoaded = true;
      return this.preferences;
    }
  }

  async savePreferences(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }

  async getPreferences(): Promise<UserPreferences> {
    if (!this.isLoaded) {
      await this.loadPreferences();
    }
    return this.preferences;
  }

  async updatePreferences(updates: Partial<UserPreferences>): Promise<void> {
    if (!this.isLoaded) {
      await this.loadPreferences();
    }
    
    this.preferences = { ...this.preferences, ...updates };
    await this.savePreferences();
  }

  async hasCompletedOnboarding(): Promise<boolean> {
    const prefs = await this.getPreferences();
    return prefs.hasCompletedOnboarding;
  }

  async completeOnboarding(): Promise<void> {
    await this.updatePreferences({ hasCompletedOnboarding: true });
  }

  async getUserId(): Promise<string> {
    const prefs = await this.getPreferences();
    return prefs.userId;
  }

  async getDeviceName(): Promise<string> {
    const prefs = await this.getPreferences();
    return prefs.deviceName;
  }

  async setDeviceName(name: string): Promise<void> {
    await this.updatePreferences({ deviceName: name });
  }

  async getPreferredConnectionType(): Promise<'wifi' | 'bluetooth' | 'auto'> {
    const prefs = await this.getPreferences();
    return prefs.preferredConnectionType;
  }

  async setPreferredConnectionType(type: 'wifi' | 'bluetooth' | 'auto'): Promise<void> {
    await this.updatePreferences({ preferredConnectionType: type });
  }

  async isAutoScanEnabled(): Promise<boolean> {
    const prefs = await this.getPreferences();
    return prefs.autoScanEnabled;
  }

  async setAutoScanEnabled(enabled: boolean): Promise<void> {
    await this.updatePreferences({ autoScanEnabled: enabled });
  }

  async areNotificationsEnabled(): Promise<boolean> {
    const prefs = await this.getPreferences();
    return prefs.notificationsEnabled;
  }

  async setNotificationsEnabled(enabled: boolean): Promise<void> {
    await this.updatePreferences({ notificationsEnabled: enabled });
  }

  async isSoundEnabled(): Promise<boolean> {
    const prefs = await this.getPreferences();
    return prefs.soundEnabled;
  }

  async setSoundEnabled(enabled: boolean): Promise<void> {
    await this.updatePreferences({ soundEnabled: enabled });
  }

  async isVibrationEnabled(): Promise<boolean> {
    const prefs = await this.getPreferences();
    return prefs.vibrationEnabled;
  }

  async setVibrationEnabled(enabled: boolean): Promise<void> {
    await this.updatePreferences({ vibrationEnabled: enabled });
  }

  async getTheme(): Promise<'dark' | 'light' | 'auto'> {
    const prefs = await this.getPreferences();
    return prefs.theme;
  }

  async setTheme(theme: 'dark' | 'light' | 'auto'): Promise<void> {
    await this.updatePreferences({ theme });
  }

  async addFavoriteGame(packageName: string): Promise<void> {
    const prefs = await this.getPreferences();
    const favoriteGames = [...prefs.favoriteGames];
    
    if (!favoriteGames.includes(packageName)) {
      favoriteGames.push(packageName);
      await this.updatePreferences({ favoriteGames });
    }
  }

  async removeFavoriteGame(packageName: string): Promise<void> {
    const prefs = await this.getPreferences();
    const favoriteGames = prefs.favoriteGames.filter(game => game !== packageName);
    await this.updatePreferences({ favoriteGames });
  }

  async getFavoriteGames(): Promise<string[]> {
    const prefs = await this.getPreferences();
    return prefs.favoriteGames;
  }

  async addRecentConnection(deviceId: string): Promise<void> {
    const prefs = await this.getPreferences();
    const recentConnections = [deviceId, ...prefs.recentConnections.filter(id => id !== deviceId)];
    
    // Keep only the last 10 connections
    const trimmedConnections = recentConnections.slice(0, 10);
    await this.updatePreferences({ recentConnections: trimmedConnections });
  }

  async getRecentConnections(): Promise<string[]> {
    const prefs = await this.getPreferences();
    return prefs.recentConnections;
  }

  async incrementSessionCount(): Promise<void> {
    const prefs = await this.getPreferences();
    await this.updatePreferences({ totalSessions: prefs.totalSessions + 1 });
  }

  async addPlaytime(minutes: number): Promise<void> {
    const prefs = await this.getPreferences();
    await this.updatePreferences({ totalPlaytime: prefs.totalPlaytime + minutes });
  }

  async getTotalPlaytime(): Promise<number> {
    const prefs = await this.getPreferences();
    return prefs.totalPlaytime;
  }

  async getTotalSessions(): Promise<number> {
    const prefs = await this.getPreferences();
    return prefs.totalSessions;
  }

  async resetPreferences(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
      this.preferences = DEFAULT_PREFERENCES;
      this.isLoaded = false;
    } catch (error) {
      console.error('Failed to reset preferences:', error);
    }
  }

  private generateUserId(): string {
    return 'user_' + Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  private generateDeviceName(): string {
    const adjectives = ['Swift', 'Bright', 'Cool', 'Smart', 'Quick', 'Bold', 'Sleek', 'Fast'];
    const nouns = ['Gamer', 'Player', 'Device', 'Phone', 'Tablet', 'Mobile', 'Gadget', 'Tech'];
    
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const number = Math.floor(Math.random() * 999) + 1;
    
    return `${adjective}${noun}${number}`;
  }

  // Export/Import functionality for backup
  async exportPreferences(): Promise<string> {
    const prefs = await this.getPreferences();
    return JSON.stringify(prefs, null, 2);
  }

  async importPreferences(data: string): Promise<boolean> {
    try {
      const imported = JSON.parse(data);
      
      // Validate the imported data has required fields
      if (typeof imported === 'object' && imported.userId && imported.deviceName) {
        this.preferences = { ...DEFAULT_PREFERENCES, ...imported };
        await this.savePreferences();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to import preferences:', error);
      return false;
    }
  }
}

export default UserPreferencesService.getInstance();
