import { Platform, Linking, Alert } from 'react-native';
import * as StoreReview from 'expo-store-review';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface RatingConfig {
  appStoreId: string; // iOS App Store ID
  playStoreId: string; // Android Package Name
  minimumSessionsBeforePrompt: number;
  minimumDaysBeforePrompt: number;
  minimumDaysBetweenPrompts: number;
}

export interface RatingState {
  hasRated: boolean;
  lastPromptDate: number | null;
  promptCount: number;
  sessionCount: number;
  firstLaunchDate: number;
  userDeclinedRating: boolean;
  userPostponedRating: boolean;
}

class AppStoreRatingService {
  private static instance: AppStoreRatingService;
  private config: RatingConfig;
  private state: RatingState | null = null;

  private readonly STORAGE_KEY = '@app_rating_state';
  private readonly DEFAULT_CONFIG: RatingConfig = {
    appStoreId: '*********', // Replace with actual App Store ID
    playStoreId: 'com.yourcompany.logaco', // Replace with actual package name
    minimumSessionsBeforePrompt: 5,
    minimumDaysBeforePrompt: 3,
    minimumDaysBetweenPrompts: 30,
  };

  static getInstance(): AppStoreRatingService {
    if (!AppStoreRatingService.instance) {
      AppStoreRatingService.instance = new AppStoreRatingService();
    }
    return AppStoreRatingService.instance;
  }

  constructor() {
    this.config = this.DEFAULT_CONFIG;
  }

  async initialize(config?: Partial<RatingConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.DEFAULT_CONFIG, ...config };
    }
    await this.loadState();
  }

  private async loadState(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.state = JSON.parse(stored);
      } else {
        this.state = {
          hasRated: false,
          lastPromptDate: null,
          promptCount: 0,
          sessionCount: 0,
          firstLaunchDate: Date.now(),
          userDeclinedRating: false,
          userPostponedRating: false,
        };
        await this.saveState();
      }
    } catch (error) {
      console.error('Failed to load rating state:', error);
      this.state = {
        hasRated: false,
        lastPromptDate: null,
        promptCount: 0,
        sessionCount: 0,
        firstLaunchDate: Date.now(),
        userDeclinedRating: false,
        userPostponedRating: false,
      };
    }
  }

  private async saveState(): Promise<void> {
    if (!this.state) return;
    
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.state));
    } catch (error) {
      console.error('Failed to save rating state:', error);
    }
  }

  async incrementSessionCount(): Promise<void> {
    if (!this.state) await this.loadState();
    if (!this.state) return;

    this.state.sessionCount++;
    await this.saveState();
  }

  async shouldShowRatingPrompt(): Promise<boolean> {
    if (!this.state) await this.loadState();
    if (!this.state) return false;

    // Don't show if user has already rated
    if (this.state.hasRated) return false;

    // Don't show if user declined rating
    if (this.state.userDeclinedRating) return false;

    // Check minimum sessions
    if (this.state.sessionCount < this.config.minimumSessionsBeforePrompt) return false;

    // Check minimum days since first launch
    const daysSinceFirstLaunch = (Date.now() - this.state.firstLaunchDate) / (1000 * 60 * 60 * 24);
    if (daysSinceFirstLaunch < this.config.minimumDaysBeforePrompt) return false;

    // Check minimum days between prompts
    if (this.state.lastPromptDate) {
      const daysSinceLastPrompt = (Date.now() - this.state.lastPromptDate) / (1000 * 60 * 60 * 24);
      if (daysSinceLastPrompt < this.config.minimumDaysBetweenPrompts) return false;
    }

    return true;
  }

  async showRatingPrompt(): Promise<void> {
    if (!this.state) await this.loadState();
    if (!this.state) return;

    const shouldShow = await this.shouldShowRatingPrompt();
    if (!shouldShow) return;

    this.state.lastPromptDate = Date.now();
    this.state.promptCount++;
    await this.saveState();

    Alert.alert(
      'Rate LoGaCo',
      'Are you enjoying LoGaCo? Please take a moment to rate us in the app store. It really helps!',
      [
        {
          text: 'Not Now',
          style: 'cancel',
          onPress: () => this.handlePostponeRating(),
        },
        {
          text: 'Never',
          style: 'destructive',
          onPress: () => this.handleDeclineRating(),
        },
        {
          text: 'Rate App',
          onPress: () => this.handleRateApp(),
        },
      ]
    );
  }

  private async handlePostponeRating(): Promise<void> {
    if (!this.state) return;
    
    this.state.userPostponedRating = true;
    await this.saveState();
  }

  private async handleDeclineRating(): Promise<void> {
    if (!this.state) return;
    
    this.state.userDeclinedRating = true;
    await this.saveState();
  }

  private async handleRateApp(): Promise<void> {
    try {
      await this.openAppStore();
      
      if (this.state) {
        this.state.hasRated = true;
        await this.saveState();
      }
    } catch (error) {
      console.error('Failed to open app store:', error);
      Alert.alert(
        'Error',
        'Unable to open the app store. Please try again later.',
        [{ text: 'OK' }]
      );
    }
  }

  async openAppStore(): Promise<void> {
    try {
      // Try to use the native in-app review first (iOS 10.3+ and Android 5.0+)
      const isAvailable = await StoreReview.isAvailableAsync();
      
      if (isAvailable) {
        await StoreReview.requestReview();
        return;
      }

      // Fallback to opening the store manually
      await this.openStoreManually();
    } catch (error) {
      console.error('Failed to open store review:', error);
      await this.openStoreManually();
    }
  }

  private async openStoreManually(): Promise<void> {
    let storeUrl: string;

    if (Platform.OS === 'ios') {
      storeUrl = `https://apps.apple.com/app/id${this.config.appStoreId}?action=write-review`;
    } else {
      storeUrl = `market://details?id=${this.config.playStoreId}`;
    }

    const canOpen = await Linking.canOpenURL(storeUrl);
    
    if (canOpen) {
      await Linking.openURL(storeUrl);
    } else {
      // Fallback to web version
      const webUrl = Platform.OS === 'ios'
        ? `https://apps.apple.com/app/id${this.config.appStoreId}`
        : `https://play.google.com/store/apps/details?id=${this.config.playStoreId}`;
      
      await Linking.openURL(webUrl);
    }
  }

  async forceShowRatingPrompt(): Promise<void> {
    // Force show rating prompt (for settings screen)
    Alert.alert(
      'Rate LoGaCo',
      'Help us improve by rating LoGaCo in the app store!',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Rate App',
          onPress: () => this.handleRateApp(),
        },
      ]
    );
  }

  async resetRatingState(): Promise<void> {
    this.state = {
      hasRated: false,
      lastPromptDate: null,
      promptCount: 0,
      sessionCount: 0,
      firstLaunchDate: Date.now(),
      userDeclinedRating: false,
      userPostponedRating: false,
    };
    await this.saveState();
  }

  async getRatingState(): Promise<RatingState | null> {
    if (!this.state) await this.loadState();
    return this.state;
  }

  async hasUserRated(): Promise<boolean> {
    if (!this.state) await this.loadState();
    return this.state?.hasRated || false;
  }

  async getPromptCount(): Promise<number> {
    if (!this.state) await this.loadState();
    return this.state?.promptCount || 0;
  }

  async getSessionCount(): Promise<number> {
    if (!this.state) await this.loadState();
    return this.state?.sessionCount || 0;
  }

  updateConfig(config: Partial<RatingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  getConfig(): RatingConfig {
    return { ...this.config };
  }

  // Helper method to check if we can show native review
  async canShowNativeReview(): Promise<boolean> {
    try {
      return await StoreReview.isAvailableAsync();
    } catch (error) {
      return false;
    }
  }

  // Helper method to get store URLs
  getStoreUrls(): { ios: string; android: string } {
    return {
      ios: `https://apps.apple.com/app/id${this.config.appStoreId}`,
      android: `https://play.google.com/store/apps/details?id=${this.config.playStoreId}`,
    };
  }
}

export default AppStoreRatingService.getInstance();
