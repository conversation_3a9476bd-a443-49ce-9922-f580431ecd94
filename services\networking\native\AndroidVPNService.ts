import { Platform, NativeModules, NativeEventEmitter } from 'react-native';
import { EventEmitter } from 'events';

export interface VPNStatus {
  isRunning: boolean;
  isServiceBound: boolean;
  bytesReceived: number;
  bytesSent: number;
  packetsProcessed: number;
}

export interface NetworkInterface {
  name: string;
  displayName: string;
  isUp: boolean;
  isLoopback: boolean;
  isVirtual: boolean;
  addresses: Record<string, string>;
}

export interface SystemNetworkInfo {
  type: string;
  subtype: string;
  isConnected: boolean;
  isAvailable: boolean;
  state: string;
  reason?: string;
  extraInfo?: string;
}

export interface GamingPacketInfo {
  sourceIP: string;
  destIP: string;
  protocol: number;
  size: number;
  timestamp: number;
}

class AndroidVPNService extends EventEmitter {
  private static instance: AndroidVPNService;
  private nativeModule: any;
  private eventEmitter: NativeEventEmitter | null = null;
  private isInitialized: boolean = false;
  private eventSubscriptions: any[] = [];

  private constructor() {
    super();
    
    if (Platform.OS === 'android') {
      this.nativeModule = NativeModules.VPNServiceBridge;
      if (this.nativeModule) {
        this.eventEmitter = new NativeEventEmitter(this.nativeModule);
        this.setupEventListeners();
        this.isInitialized = true;
      } else {
        console.warn('VPNServiceBridge native module not found');
      }
    } else {
      console.warn('Android VPN Service only available on Android platform');
    }
  }

  static getInstance(): AndroidVPNService {
    if (!AndroidVPNService.instance) {
      AndroidVPNService.instance = new AndroidVPNService();
    }
    return AndroidVPNService.instance;
  }

  private setupEventListeners(): void {
    if (!this.eventEmitter) return;

    // VPN service events
    this.eventSubscriptions.push(
      this.eventEmitter.addListener('VPNStarted', (event: any) => {
        this.emit('vpnStarted', event);
        console.log('VPN service started');
      })
    );

    this.eventSubscriptions.push(
      this.eventEmitter.addListener('VPNStopped', (event: any) => {
        this.emit('vpnStopped', event);
        console.log('VPN service stopped');
      })
    );

    this.eventSubscriptions.push(
      this.eventEmitter.addListener('VPNError', (event: any) => {
        this.emit('vpnError', event);
        console.error('VPN service error:', event);
      })
    );

    // Gaming packet detection
    this.eventSubscriptions.push(
      this.eventEmitter.addListener('GamingPacketDetected', (event: GamingPacketInfo) => {
        this.emit('gamingPacketDetected', event);
        console.log(`Gaming packet detected: ${event.sourceIP} -> ${event.destIP} (${event.size} bytes)`);
      })
    );
  }

  // Permission management
  async requestVPNPermission(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      const result = await this.nativeModule.requestVPNPermission();
      console.log('VPN permission requested:', result.status);
      return result;
    } catch (error) {
      console.error('Failed to request VPN permission:', error);
      throw error;
    }
  }

  async isVPNPermissionGranted(): Promise<{ granted: boolean }> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      const result = await this.nativeModule.isVPNPermissionGranted();
      return result;
    } catch (error) {
      console.error('Failed to check VPN permission:', error);
      throw error;
    }
  }

  // VPN service control
  async startVPN(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      // Check permission first
      const permissionStatus = await this.isVPNPermissionGranted();
      if (!permissionStatus.granted) {
        throw new Error('VPN permission not granted');
      }

      const result = await this.nativeModule.startVPN();
      this.emit('vpnStarting');
      console.log('VPN service starting:', result.status);
      return result;
    } catch (error) {
      console.error('Failed to start VPN service:', error);
      throw error;
    }
  }

  async stopVPN(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      const result = await this.nativeModule.stopVPN();
      this.emit('vpnStopping');
      console.log('VPN service stopping:', result.status);
      return result;
    } catch (error) {
      console.error('Failed to stop VPN service:', error);
      throw error;
    }
  }

  async getVPNStatus(): Promise<VPNStatus> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      const status = await this.nativeModule.getVPNStatus();
      return status;
    } catch (error) {
      console.error('Failed to get VPN status:', error);
      throw error;
    }
  }

  // Network information
  async getNetworkInterfaces(): Promise<Record<string, NetworkInterface>> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      const interfaces = await this.nativeModule.getNetworkInterfaces();
      return interfaces;
    } catch (error) {
      console.error('Failed to get network interfaces:', error);
      throw error;
    }
  }

  async getSystemNetworkInfo(): Promise<SystemNetworkInfo> {
    if (!this.isAvailable()) {
      throw new Error('Android VPN Service not available');
    }

    try {
      const networkInfo = await this.nativeModule.getSystemNetworkInfo();
      return networkInfo;
    } catch (error) {
      console.error('Failed to get system network info:', error);
      throw error;
    }
  }

  // Utility methods
  isAvailable(): boolean {
    return Platform.OS === 'android' && this.isInitialized && this.nativeModule != null;
  }

  cleanup(): void {
    // Remove event listeners
    this.eventSubscriptions.forEach(subscription => {
      subscription.remove();
    });
    this.eventSubscriptions = [];

    // Stop VPN if running
    if (this.isAvailable()) {
      this.stopVPN().catch(console.error);
    }

    console.log('Android VPN Service cleaned up');
  }

  // High-level convenience methods
  async initializeVPN(): Promise<void> {
    try {
      // Check if permission is granted
      const permissionStatus = await this.isVPNPermissionGranted();
      
      if (!permissionStatus.granted) {
        console.log('Requesting VPN permission...');
        await this.requestVPNPermission();
      }

      console.log('VPN initialized and ready');
    } catch (error) {
      console.error('Failed to initialize VPN:', error);
      throw error;
    }
  }

  async startTrafficInterception(): Promise<void> {
    try {
      await this.initializeVPN();
      await this.startVPN();
      console.log('Traffic interception started');
    } catch (error) {
      console.error('Failed to start traffic interception:', error);
      throw error;
    }
  }

  async stopTrafficInterception(): Promise<void> {
    try {
      await this.stopVPN();
      console.log('Traffic interception stopped');
    } catch (error) {
      console.error('Failed to stop traffic interception:', error);
      throw error;
    }
  }

  async getTrafficStats(): Promise<{
    bytesReceived: number;
    bytesSent: number;
    packetsProcessed: number;
    isActive: boolean;
  }> {
    try {
      const status = await this.getVPNStatus();
      return {
        bytesReceived: status.bytesReceived,
        bytesSent: status.bytesSent,
        packetsProcessed: status.packetsProcessed,
        isActive: status.isRunning
      };
    } catch (error) {
      console.error('Failed to get traffic stats:', error);
      return {
        bytesReceived: 0,
        bytesSent: 0,
        packetsProcessed: 0,
        isActive: false
      };
    }
  }

  // Event handler helpers
  onVPNStarted(callback: (event: any) => void): void {
    this.on('vpnStarted', callback);
  }

  onVPNStopped(callback: (event: any) => void): void {
    this.on('vpnStopped', callback);
  }

  onVPNError(callback: (error: any) => void): void {
    this.on('vpnError', callback);
  }

  onGamingPacketDetected(callback: (packet: GamingPacketInfo) => void): void {
    this.on('gamingPacketDetected', callback);
  }

  // Monitoring methods
  startMonitoring(interval: number = 5000): NodeJS.Timeout {
    return setInterval(async () => {
      try {
        const status = await this.getVPNStatus();
        this.emit('statusUpdate', status);
      } catch (error) {
        console.error('Error during monitoring:', error);
      }
    }, interval);
  }

  stopMonitoring(intervalId: NodeJS.Timeout): void {
    clearInterval(intervalId);
  }
}

export default AndroidVPNService.getInstance();
