import { Platform, NativeModules, NativeEventEmitter } from 'react-native';
import { EventEmitter } from 'events';

export interface MultipeerPeer {
  displayName: string;
  peerID: string;
}

export interface MultipeerSessionInfo {
  localPeerID: string;
  serviceType: string;
  isAdvertising: boolean;
  isBrowsing: boolean;
  connectedPeersCount: number;
  encryptionPreference: string;
}

export interface MultipeerDataEvent {
  data: string;
  fromPeer: string;
  bytes: number;
  timestamp: number;
}

export interface MultipeerConnectionEvent {
  peerID: string;
  state: 'connected' | 'connecting' | 'disconnected' | 'unknown';
  timestamp: number;
}

class iOSMultipeerConnectivity extends EventEmitter {
  private static instance: iOSMultipeerConnectivity;
  private nativeModule: any;
  private eventEmitter: NativeEventEmitter | null = null;
  private isInitialized: boolean = false;
  private eventSubscriptions: any[] = [];

  private constructor() {
    super();
    
    if (Platform.OS === 'ios') {
      this.nativeModule = NativeModules.MultipeerConnectivityBridge;
      if (this.nativeModule) {
        this.eventEmitter = new NativeEventEmitter(this.nativeModule);
        this.setupEventListeners();
        this.isInitialized = true;
      } else {
        console.warn('MultipeerConnectivityBridge native module not found');
      }
    } else {
      console.warn('iOS Multipeer Connectivity only available on iOS platform');
    }
  }

  static getInstance(): iOSMultipeerConnectivity {
    if (!iOSMultipeerConnectivity.instance) {
      iOSMultipeerConnectivity.instance = new iOSMultipeerConnectivity();
    }
    return iOSMultipeerConnectivity.instance;
  }

  private setupEventListeners(): void {
    if (!this.eventEmitter) return;

    // Peer discovery events
    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerPeerFound', (event: MultipeerConnectionEvent) => {
        this.emit('peerFound', event);
        console.log(`Multipeer peer found: ${event.peerID}`);
      })
    );

    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerPeerLost', (event: MultipeerConnectionEvent) => {
        this.emit('peerLost', event);
        console.log(`Multipeer peer lost: ${event.peerID}`);
      })
    );

    // Connection state events
    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerPeerConnected', (event: MultipeerConnectionEvent) => {
        this.emit('peerConnected', event);
        console.log(`Multipeer peer connected: ${event.peerID}`);
      })
    );

    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerPeerDisconnected', (event: MultipeerConnectionEvent) => {
        this.emit('peerDisconnected', event);
        console.log(`Multipeer peer disconnected: ${event.peerID}`);
      })
    );

    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerConnectionStateChanged', (event: MultipeerConnectionEvent) => {
        this.emit('connectionStateChanged', event);
        console.log(`Multipeer connection state changed: ${event.peerID} -> ${event.state}`);
      })
    );

    // Data events
    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerDataReceived', (event: MultipeerDataEvent) => {
        this.emit('dataReceived', event);
        console.log(`Multipeer data received from ${event.fromPeer}: ${event.bytes} bytes`);
      })
    );

    // Error events
    this.eventSubscriptions.push(
      this.eventEmitter.addListener('MultipeerError', (event: any) => {
        this.emit('error', event);
        console.error('Multipeer error:', event);
      })
    );
  }

  // Advertising methods
  async startAdvertising(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.startAdvertising();
      this.emit('advertisingStarted');
      console.log('Multipeer advertising started');
      return result;
    } catch (error) {
      console.error('Failed to start advertising:', error);
      throw error;
    }
  }

  async stopAdvertising(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.stopAdvertising();
      this.emit('advertisingStopped');
      console.log('Multipeer advertising stopped');
      return result;
    } catch (error) {
      console.error('Failed to stop advertising:', error);
      throw error;
    }
  }

  // Browsing methods
  async startBrowsing(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.startBrowsing();
      this.emit('browsingStarted');
      console.log('Multipeer browsing started');
      return result;
    } catch (error) {
      console.error('Failed to start browsing:', error);
      throw error;
    }
  }

  async stopBrowsing(): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.stopBrowsing();
      this.emit('browsingStopped');
      console.log('Multipeer browsing stopped');
      return result;
    } catch (error) {
      console.error('Failed to stop browsing:', error);
      throw error;
    }
  }

  // Data transmission methods
  async sendData(data: string, toPeer: string): Promise<{ status: string; bytes: number }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.sendData(data, toPeer);
      console.log(`Multipeer data sent to ${toPeer}: ${result.bytes} bytes`);
      return result;
    } catch (error) {
      console.error('Failed to send data:', error);
      throw error;
    }
  }

  async broadcastData(data: string): Promise<{ status: string; bytes: number; peers: number }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.broadcastData(data);
      console.log(`Multipeer data broadcasted: ${result.bytes} bytes to ${result.peers} peers`);
      return result;
    } catch (error) {
      console.error('Failed to broadcast data:', error);
      throw error;
    }
  }

  // Session management methods
  async getConnectedPeers(): Promise<{ peers: MultipeerPeer[]; count: number }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.getConnectedPeers();
      return result;
    } catch (error) {
      console.error('Failed to get connected peers:', error);
      throw error;
    }
  }

  async disconnectFromPeer(peerDisplayName: string): Promise<{ status: string }> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.disconnectFromPeer(peerDisplayName);
      console.log(`Disconnected from peer: ${peerDisplayName}`);
      return result;
    } catch (error) {
      console.error('Failed to disconnect from peer:', error);
      throw error;
    }
  }

  async getSessionInfo(): Promise<MultipeerSessionInfo> {
    if (!this.isAvailable()) {
      throw new Error('iOS Multipeer Connectivity not available');
    }

    try {
      const result = await this.nativeModule.getSessionInfo();
      return result;
    } catch (error) {
      console.error('Failed to get session info:', error);
      throw error;
    }
  }

  // Utility methods
  isAvailable(): boolean {
    return Platform.OS === 'ios' && this.isInitialized && this.nativeModule != null;
  }

  cleanup(): void {
    // Remove event listeners
    this.eventSubscriptions.forEach(subscription => {
      subscription.remove();
    });
    this.eventSubscriptions = [];

    // Stop advertising and browsing
    if (this.isAvailable()) {
      this.stopAdvertising().catch(console.error);
      this.stopBrowsing().catch(console.error);
    }

    console.log('iOS Multipeer Connectivity cleaned up');
  }

  // High-level convenience methods
  async createSession(): Promise<void> {
    try {
      await this.startAdvertising();
      console.log('Multipeer session created and advertising');
    } catch (error) {
      console.error('Failed to create session:', error);
      throw error;
    }
  }

  async joinSession(): Promise<void> {
    try {
      await this.startBrowsing();
      console.log('Multipeer browsing for sessions');
    } catch (error) {
      console.error('Failed to join session:', error);
      throw error;
    }
  }

  async sendGameData(gameData: any, toPeer?: string): Promise<void> {
    try {
      const dataString = JSON.stringify({
        type: 'game_data',
        payload: gameData,
        timestamp: Date.now()
      });

      if (toPeer) {
        await this.sendData(dataString, toPeer);
      } else {
        await this.broadcastData(dataString);
      }
    } catch (error) {
      console.error('Failed to send game data:', error);
      throw error;
    }
  }

  async sendControlMessage(message: string, toPeer?: string): Promise<void> {
    try {
      const dataString = JSON.stringify({
        type: 'control',
        message,
        timestamp: Date.now()
      });

      if (toPeer) {
        await this.sendData(dataString, toPeer);
      } else {
        await this.broadcastData(dataString);
      }
    } catch (error) {
      console.error('Failed to send control message:', error);
      throw error;
    }
  }

  // Event handler helpers
  onPeerFound(callback: (event: MultipeerConnectionEvent) => void): void {
    this.on('peerFound', callback);
  }

  onPeerConnected(callback: (event: MultipeerConnectionEvent) => void): void {
    this.on('peerConnected', callback);
  }

  onPeerDisconnected(callback: (event: MultipeerConnectionEvent) => void): void {
    this.on('peerDisconnected', callback);
  }

  onDataReceived(callback: (event: MultipeerDataEvent) => void): void {
    this.on('dataReceived', callback);
  }

  onError(callback: (error: any) => void): void {
    this.on('error', callback);
  }
}

export default iOSMultipeerConnectivity.getInstance();
