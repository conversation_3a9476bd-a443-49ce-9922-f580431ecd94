import { EventEmitter } from 'events';
import ProtocolEmulator from './ProtocolEmulator';
import WebRTCManager from './WebRTCManager';

export interface VirtualServer {
  id: string;
  gameId: string;
  sessionId: string;
  hostDeviceId: string;
  port: number;
  protocol: 'tcp' | 'udp' | 'websocket';
  status: 'starting' | 'running' | 'stopping' | 'stopped';
  connectedPeers: Map<string, PeerConnection>;
  gameState: any;
  lastStateUpdate: number;
  maxPlayers: number;
  currentPlayers: number;
}

export interface PeerConnection {
  deviceId: string;
  connectionId: string;
  status: 'connecting' | 'connected' | 'disconnected';
  lastSeen: number;
  latency: number;
  dataChannel?: any;
}

export interface GameStateUpdate {
  serverId: string;
  updateId: string;
  timestamp: number;
  type: 'full' | 'delta' | 'event';
  data: any;
  sourceDeviceId: string;
  sequenceNumber: number;
}

export interface ConflictResolution {
  conflictId: string;
  timestamp: number;
  conflictingUpdates: GameStateUpdate[];
  resolution: 'host_wins' | 'timestamp_wins' | 'merge' | 'rollback';
  resolvedState: any;
}

class GameServerEmulator extends EventEmitter {
  private static instance: GameServerEmulator;
  private servers: Map<string, VirtualServer> = new Map();
  private protocolEmulator: ProtocolEmulator;
  private webrtcManager: WebRTCManager;
  private stateUpdateQueue: Map<string, GameStateUpdate[]> = new Map();
  private conflictResolver: ConflictResolver;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.protocolEmulator = ProtocolEmulator.getInstance();
    this.webrtcManager = WebRTCManager.getInstance();
    this.conflictResolver = new ConflictResolver();
    this.startHeartbeat();
  }

  static getInstance(): GameServerEmulator {
    if (!GameServerEmulator.instance) {
      GameServerEmulator.instance = new GameServerEmulator();
    }
    return GameServerEmulator.instance;
  }

  // Virtual Server Management
  async createVirtualServer(
    gameId: string,
    sessionId: string,
    hostDeviceId: string,
    config: {
      port?: number;
      protocol?: 'tcp' | 'udp' | 'websocket';
      maxPlayers?: number;
    } = {}
  ): Promise<string> {
    try {
      const serverId = `${gameId}_${sessionId}_${Date.now()}`;
      
      const server: VirtualServer = {
        id: serverId,
        gameId,
        sessionId,
        hostDeviceId,
        port: config.port || this.getDefaultPort(gameId),
        protocol: config.protocol || 'tcp',
        status: 'starting',
        connectedPeers: new Map(),
        gameState: this.initializeGameState(gameId),
        lastStateUpdate: Date.now(),
        maxPlayers: config.maxPlayers || 8,
        currentPlayers: 0
      };

      this.servers.set(serverId, server);
      this.stateUpdateQueue.set(serverId, []);

      // Start the virtual server
      await this.startVirtualServer(server);

      console.log(`Virtual server created: ${serverId} for game ${gameId}`);
      this.emit('serverCreated', server);
      
      return serverId;
    } catch (error) {
      console.error('Failed to create virtual server:', error);
      throw error;
    }
  }

  async destroyVirtualServer(serverId: string): Promise<void> {
    try {
      const server = this.servers.get(serverId);
      if (!server) {
        throw new Error(`Virtual server not found: ${serverId}`);
      }

      // Disconnect all peers
      for (const peer of server.connectedPeers.values()) {
        await this.disconnectPeer(serverId, peer.deviceId);
      }

      // Stop the server
      server.status = 'stopping';
      await this.stopVirtualServer(server);

      // Cleanup
      this.servers.delete(serverId);
      this.stateUpdateQueue.delete(serverId);

      console.log(`Virtual server destroyed: ${serverId}`);
      this.emit('serverDestroyed', serverId);
    } catch (error) {
      console.error('Failed to destroy virtual server:', error);
      throw error;
    }
  }

  // Peer Connection Management
  async connectPeer(serverId: string, deviceId: string): Promise<boolean> {
    try {
      const server = this.servers.get(serverId);
      if (!server) {
        throw new Error(`Virtual server not found: ${serverId}`);
      }

      if (server.currentPlayers >= server.maxPlayers) {
        console.warn(`Server ${serverId} is full`);
        return false;
      }

      // Create WebRTC connection
      const connectionId = await this.webrtcManager.createConnection(deviceId);
      
      const peer: PeerConnection = {
        deviceId,
        connectionId,
        status: 'connecting',
        lastSeen: Date.now(),
        latency: 0
      };

      server.connectedPeers.set(deviceId, peer);
      server.currentPlayers++;

      // Send initial game state to new peer
      await this.sendGameStateToDevice(serverId, deviceId, 'full');

      peer.status = 'connected';
      console.log(`Peer ${deviceId} connected to server ${serverId}`);
      this.emit('peerConnected', { serverId, deviceId });

      return true;
    } catch (error) {
      console.error(`Failed to connect peer ${deviceId} to server ${serverId}:`, error);
      return false;
    }
  }

  async disconnectPeer(serverId: string, deviceId: string): Promise<void> {
    try {
      const server = this.servers.get(serverId);
      if (!server) {
        return;
      }

      const peer = server.connectedPeers.get(deviceId);
      if (peer) {
        // Close WebRTC connection
        if (peer.connectionId) {
          await this.webrtcManager.closeConnection(peer.connectionId);
        }

        server.connectedPeers.delete(deviceId);
        server.currentPlayers--;

        console.log(`Peer ${deviceId} disconnected from server ${serverId}`);
        this.emit('peerDisconnected', { serverId, deviceId });
      }
    } catch (error) {
      console.error(`Failed to disconnect peer ${deviceId} from server ${serverId}:`, error);
    }
  }

  // Game State Synchronization
  async updateGameState(
    serverId: string,
    sourceDeviceId: string,
    stateUpdate: Partial<GameStateUpdate>
  ): Promise<void> {
    try {
      const server = this.servers.get(serverId);
      if (!server) {
        throw new Error(`Virtual server not found: ${serverId}`);
      }

      const update: GameStateUpdate = {
        serverId,
        updateId: `${serverId}_${Date.now()}_${Math.random()}`,
        timestamp: Date.now(),
        type: stateUpdate.type || 'delta',
        data: stateUpdate.data,
        sourceDeviceId,
        sequenceNumber: this.getNextSequenceNumber(serverId)
      };

      // Add to update queue
      const queue = this.stateUpdateQueue.get(serverId) || [];
      queue.push(update);
      this.stateUpdateQueue.set(serverId, queue);

      // Process the update
      await this.processStateUpdate(server, update);

      // Broadcast to all connected peers
      await this.broadcastStateUpdate(server, update);

      console.log(`Game state updated for server ${serverId} by ${sourceDeviceId}`);
      this.emit('stateUpdated', { serverId, update });
    } catch (error) {
      console.error('Failed to update game state:', error);
      throw error;
    }
  }

  private async processStateUpdate(server: VirtualServer, update: GameStateUpdate): Promise<void> {
    try {
      // Check for conflicts
      const conflicts = await this.detectConflicts(server, update);
      
      if (conflicts.length > 0) {
        const resolution = await this.conflictResolver.resolve(conflicts, update);
        server.gameState = resolution.resolvedState;
        console.log(`Resolved ${conflicts.length} conflicts for server ${server.id}`);
      } else {
        // Apply update directly
        if (update.type === 'full') {
          server.gameState = update.data;
        } else if (update.type === 'delta') {
          server.gameState = { ...server.gameState, ...update.data };
        }
      }

      server.lastStateUpdate = Date.now();
    } catch (error) {
      console.error('Failed to process state update:', error);
      throw error;
    }
  }

  private async detectConflicts(server: VirtualServer, update: GameStateUpdate): Promise<GameStateUpdate[]> {
    const conflicts: GameStateUpdate[] = [];
    const queue = this.stateUpdateQueue.get(server.id) || [];

    // Look for conflicting updates in the recent queue
    const recentUpdates = queue.filter(u => 
      u.timestamp > (Date.now() - 5000) && // Last 5 seconds
      u.sourceDeviceId !== update.sourceDeviceId &&
      this.hasDataConflict(u.data, update.data)
    );

    conflicts.push(...recentUpdates);
    return conflicts;
  }

  private hasDataConflict(data1: any, data2: any): boolean {
    // Simple conflict detection - check for overlapping keys
    if (typeof data1 !== 'object' || typeof data2 !== 'object') {
      return false;
    }

    const keys1 = Object.keys(data1);
    const keys2 = Object.keys(data2);
    
    return keys1.some(key => keys2.includes(key) && data1[key] !== data2[key]);
  }

  private async broadcastStateUpdate(server: VirtualServer, update: GameStateUpdate): Promise<void> {
    const broadcastPromises: Promise<void>[] = [];

    for (const [deviceId, peer] of server.connectedPeers) {
      if (peer.status === 'connected' && deviceId !== update.sourceDeviceId) {
        broadcastPromises.push(this.sendUpdateToDevice(server.id, deviceId, update));
      }
    }

    await Promise.allSettled(broadcastPromises);
  }

  private async sendUpdateToDevice(serverId: string, deviceId: string, update: GameStateUpdate): Promise<void> {
    try {
      const server = this.servers.get(serverId);
      const peer = server?.connectedPeers.get(deviceId);
      
      if (peer && peer.dataChannel) {
        await this.webrtcManager.sendData(peer.connectionId, {
          type: 'gameStateUpdate',
          update
        });
      }
    } catch (error) {
      console.error(`Failed to send update to device ${deviceId}:`, error);
    }
  }

  private async sendGameStateToDevice(serverId: string, deviceId: string, type: 'full' | 'delta'): Promise<void> {
    try {
      const server = this.servers.get(serverId);
      if (!server) return;

      const stateUpdate: GameStateUpdate = {
        serverId,
        updateId: `${serverId}_initial_${Date.now()}`,
        timestamp: Date.now(),
        type,
        data: server.gameState,
        sourceDeviceId: server.hostDeviceId,
        sequenceNumber: 0
      };

      await this.sendUpdateToDevice(serverId, deviceId, stateUpdate);
    } catch (error) {
      console.error(`Failed to send game state to device ${deviceId}:`, error);
    }
  }

  // Helper Methods
  private async startVirtualServer(server: VirtualServer): Promise<void> {
    // Initialize protocol emulation for this game
    await this.protocolEmulator.enableProtocol(server.gameId);
    
    server.status = 'running';
    console.log(`Virtual server ${server.id} started on port ${server.port}`);
  }

  private async stopVirtualServer(server: VirtualServer): Promise<void> {
    server.status = 'stopped';
    console.log(`Virtual server ${server.id} stopped`);
  }

  private getDefaultPort(gameId: string): number {
    const defaultPorts: { [key: string]: number } = {
      'minecraft': 25565,
      'among_us': 22023,
      'chess': 8080
    };
    return defaultPorts[gameId] || 8000;
  }

  private initializeGameState(gameId: string): any {
    const initialStates: { [key: string]: any } = {
      'minecraft': { world: 'default', players: {}, time: 0 },
      'among_us': { gamePhase: 'lobby', players: {}, settings: {} },
      'chess': { board: 'initial', turn: 'white', moves: [] }
    };
    return initialStates[gameId] || {};
  }

  private getNextSequenceNumber(serverId: string): number {
    const queue = this.stateUpdateQueue.get(serverId) || [];
    return queue.length;
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.performHeartbeat();
    }, 5000); // Every 5 seconds
  }

  private async performHeartbeat(): Promise<void> {
    for (const server of this.servers.values()) {
      for (const [deviceId, peer] of server.connectedPeers) {
        if (Date.now() - peer.lastSeen > 30000) { // 30 seconds timeout
          console.warn(`Peer ${deviceId} timed out, disconnecting...`);
          await this.disconnectPeer(server.id, deviceId);
        }
      }
    }
  }

  // Public API
  getServer(serverId: string): VirtualServer | undefined {
    return this.servers.get(serverId);
  }

  getServers(): VirtualServer[] {
    return Array.from(this.servers.values());
  }

  getServersByGame(gameId: string): VirtualServer[] {
    return Array.from(this.servers.values()).filter(s => s.gameId === gameId);
  }

  // Cleanup
  destroy(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    // Destroy all servers
    for (const serverId of this.servers.keys()) {
      this.destroyVirtualServer(serverId);
    }
    
    this.removeAllListeners();
  }
}

// Conflict Resolution Helper Class
class ConflictResolver {
  async resolve(conflicts: GameStateUpdate[], newUpdate: GameStateUpdate): Promise<ConflictResolution> {
    // Simple timestamp-based resolution for now
    const allUpdates = [...conflicts, newUpdate];
    const latestUpdate = allUpdates.reduce((latest, current) => 
      current.timestamp > latest.timestamp ? current : latest
    );

    return {
      conflictId: `conflict_${Date.now()}`,
      timestamp: Date.now(),
      conflictingUpdates: conflicts,
      resolution: 'timestamp_wins',
      resolvedState: latestUpdate.data
    };
  }
}

export default GameServerEmulator;
