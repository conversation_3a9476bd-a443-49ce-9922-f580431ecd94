name: 🔄 Continuous Integration

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:
  schedule:
    # Run nightly builds at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  # Job 1: Code Quality and Linting
  code-quality:
    name: 🔍 Code Quality & Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Run ESLint
        run: npm run lint
        continue-on-error: false

      - name: 🎨 Check Prettier formatting
        run: npm run format:check
        continue-on-error: false

      - name: 🔧 TypeScript type checking
        run: npm run type-check
        continue-on-error: false

      - name: 📊 Upload ESLint results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: eslint-results
          path: eslint-results.json
          retention-days: 7

  # Job 2: Automated Testing
  test:
    name: 🧪 Automated Testing
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      matrix:
        node-version: ['18', '20']
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests
        run: npm run test:ci

      - name: 📊 Generate test coverage
        run: npm run test:coverage

      - name: 📈 Upload coverage to Codecov
        if: matrix.node-version == '18'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

      - name: 📊 Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-node-${{ matrix.node-version }}
          path: |
            coverage/
            test-results.xml
          retention-days: 7

  # Job 3: Security Scanning
  security:
    name: 🔒 Security Scanning
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Run npm audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: 🔒 Run security scan with Snyk
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
        continue-on-error: true

      - name: 🕵️ Scan for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  # Job 4: Build Verification
  build:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [code-quality, test]
    
    strategy:
      matrix:
        platform: [web, android]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: 🏗️ Build for ${{ matrix.platform }}
        run: |
          if [ "${{ matrix.platform }}" = "web" ]; then
            npx expo export --platform web
          elif [ "${{ matrix.platform }}" = "android" ]; then
            npx expo export --platform android
          fi

      - name: 📊 Analyze bundle size
        if: matrix.platform == 'web'
        run: |
          npm install -g bundlesize
          bundlesize

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.platform }}
          path: dist/
          retention-days: 7

  # Job 5: iOS Build (macOS runner)
  build-ios:
    name: 🍎 iOS Build Verification
    runs-on: macos-latest
    timeout-minutes: 30
    needs: [code-quality, test]
    if: github.event_name != 'schedule' # Skip for nightly builds
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: 🏗️ Build for iOS
        run: npx expo export --platform ios

      - name: 📦 Upload iOS build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-ios
          path: dist/
          retention-days: 7

  # Job 6: Quality Gate
  quality-gate:
    name: ✅ Quality Gate
    runs-on: ubuntu-latest
    needs: [code-quality, test, security, build]
    if: always()
    
    steps:
      - name: 🔍 Check job results
        run: |
          echo "Code Quality: ${{ needs.code-quality.result }}"
          echo "Tests: ${{ needs.test.result }}"
          echo "Security: ${{ needs.security.result }}"
          echo "Build: ${{ needs.build.result }}"
          
          if [ "${{ needs.code-quality.result }}" != "success" ] || 
             [ "${{ needs.test.result }}" != "success" ] || 
             [ "${{ needs.build.result }}" != "success" ]; then
            echo "❌ Quality gate failed!"
            exit 1
          else
            echo "✅ Quality gate passed!"
          fi

      - name: 📊 Create quality report
        if: always()
        run: |
          echo "# 📊 CI Pipeline Quality Report" > quality-report.md
          echo "" >> quality-report.md
          echo "| Job | Status |" >> quality-report.md
          echo "|-----|--------|" >> quality-report.md
          echo "| Code Quality | ${{ needs.code-quality.result }} |" >> quality-report.md
          echo "| Tests | ${{ needs.test.result }} |" >> quality-report.md
          echo "| Security | ${{ needs.security.result }} |" >> quality-report.md
          echo "| Build | ${{ needs.build.result }} |" >> quality-report.md

      - name: 📤 Upload quality report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: quality-report
          path: quality-report.md
          retention-days: 30
