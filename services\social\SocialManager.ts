import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from '../../utils/EventEmitter';

export interface Friend {
  id: string;
  name: string;
  avatar?: string;
  deviceType: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: number;
  gamesInCommon: string[];
  friendshipDate: number;
  isBlocked: boolean;
  isFavorite: boolean;
  stats: FriendStats;
}

export interface FriendStats {
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  totalPlaytime: number;
  achievementCount: number;
  lastGamePlayed?: string;
  favoriteGame?: string;
}

export interface FriendRequest {
  id: string;
  fromUserId: string;
  fromUserName: string;
  fromUserAvatar?: string;
  toUserId: string;
  message?: string;
  timestamp: number;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
}

export interface SocialGroup {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  ownerId: string;
  members: GroupMember[];
  maxMembers: number;
  isPrivate: boolean;
  tags: string[];
  createdAt: number;
  lastActivity: number;
  stats: GroupStats;
}

export interface GroupMember {
  userId: string;
  userName: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member';
  joinedAt: number;
  lastActive: number;
  isOnline: boolean;
}

export interface GroupStats {
  totalSessions: number;
  totalPlaytime: number;
  mostPlayedGame?: string;
  averageSessionLength: number;
  memberCount: number;
}

export interface SocialActivity {
  id: string;
  type: 'friend_added' | 'game_played' | 'achievement_unlocked' | 'group_joined' | 'session_hosted';
  userId: string;
  userName: string;
  userAvatar?: string;
  description: string;
  metadata?: Record<string, any>;
  timestamp: number;
  isVisible: boolean;
}

class SocialManager extends EventEmitter {
  private static instance: SocialManager;
  private isInitialized: boolean = false;
  private currentUserId: string = '';
  private friends: Map<string, Friend> = new Map();
  private friendRequests: Map<string, FriendRequest> = new Map();
  private groups: Map<string, SocialGroup> = new Map();
  private activities: SocialActivity[] = [];
  private blockedUsers: Set<string> = new Set();

  private constructor() {
    super();
  }

  static getInstance(): SocialManager {
    if (!SocialManager.instance) {
      SocialManager.instance = new SocialManager();
    }
    return SocialManager.instance;
  }

  async initialize(userId: string): Promise<void> {
    try {
      console.log('Initializing Social Manager...');

      this.currentUserId = userId;

      // Load social data
      await this.loadFriends();
      await this.loadFriendRequests();
      await this.loadGroups();
      await this.loadActivities();
      await this.loadBlockedUsers();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('Social Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Social Manager:', error);
      throw error;
    }
  }

  private async loadFriends(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(`friends_${this.currentUserId}`);
      if (stored) {
        const data = JSON.parse(stored);
        data.forEach((friend: Friend) => {
          this.friends.set(friend.id, friend);
        });
      }
    } catch (error) {
      console.error('Failed to load friends:', error);
    }
  }

  private async loadFriendRequests(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(`friend_requests_${this.currentUserId}`);
      if (stored) {
        const data = JSON.parse(stored);
        data.forEach((request: FriendRequest) => {
          this.friendRequests.set(request.id, request);
        });
      }
    } catch (error) {
      console.error('Failed to load friend requests:', error);
    }
  }

  private async loadGroups(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(`groups_${this.currentUserId}`);
      if (stored) {
        const data = JSON.parse(stored);
        data.forEach((group: SocialGroup) => {
          this.groups.set(group.id, group);
        });
      }
    } catch (error) {
      console.error('Failed to load groups:', error);
    }
  }

  private async loadActivities(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(`activities_${this.currentUserId}`);
      if (stored) {
        this.activities = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load activities:', error);
    }
  }

  private async loadBlockedUsers(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(`blocked_users_${this.currentUserId}`);
      if (stored) {
        const data = JSON.parse(stored);
        this.blockedUsers = new Set(data);
      }
    } catch (error) {
      console.error('Failed to load blocked users:', error);
    }
  }

  private async saveFriends(): Promise<void> {
    try {
      const data = Array.from(this.friends.values());
      await AsyncStorage.setItem(`friends_${this.currentUserId}`, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save friends:', error);
    }
  }

  private async saveFriendRequests(): Promise<void> {
    try {
      const data = Array.from(this.friendRequests.values());
      await AsyncStorage.setItem(`friend_requests_${this.currentUserId}`, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save friend requests:', error);
    }
  }

  private async saveGroups(): Promise<void> {
    try {
      const data = Array.from(this.groups.values());
      await AsyncStorage.setItem(`groups_${this.currentUserId}`, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save groups:', error);
    }
  }

  private async saveActivities(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        `activities_${this.currentUserId}`,
        JSON.stringify(this.activities),
      );
    } catch (error) {
      console.error('Failed to save activities:', error);
    }
  }

  private async saveBlockedUsers(): Promise<void> {
    try {
      const data = Array.from(this.blockedUsers);
      await AsyncStorage.setItem(`blocked_users_${this.currentUserId}`, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save blocked users:', error);
    }
  }

  // Friend Management
  async sendFriendRequest(userId: string, userName: string, message?: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      if (this.blockedUsers.has(userId) || this.friends.has(userId)) {
        throw new Error('Cannot send friend request to this user');
      }

      const request: FriendRequest = {
        id: `${this.currentUserId}_${userId}_${Date.now()}`,
        fromUserId: this.currentUserId,
        fromUserName: 'You', // Would be replaced with actual user name
        toUserId: userId,
        message,
        timestamp: Date.now(),
        status: 'pending',
      };

      this.friendRequests.set(request.id, request);
      await this.saveFriendRequests();

      this.emit('friendRequestSent', request);
      console.log(`Friend request sent to ${userName}`);
    } catch (error) {
      console.error('Failed to send friend request:', error);
      throw error;
    }
  }

  async acceptFriendRequest(requestId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const request = this.friendRequests.get(requestId);
      if (!request || request.status !== 'pending') {
        throw new Error('Friend request not found or already processed');
      }

      // Create friend relationship
      const friend: Friend = {
        id: request.fromUserId,
        name: request.fromUserName,
        avatar: request.fromUserAvatar,
        deviceType: 'unknown',
        status: 'offline',
        lastSeen: Date.now(),
        gamesInCommon: [],
        friendshipDate: Date.now(),
        isBlocked: false,
        isFavorite: false,
        stats: {
          gamesPlayed: 0,
          gamesWon: 0,
          winRate: 0,
          totalPlaytime: 0,
          achievementCount: 0,
        },
      };

      this.friends.set(friend.id, friend);
      request.status = 'accepted';

      await this.saveFriends();
      await this.saveFriendRequests();

      // Add activity
      await this.addActivity({
        type: 'friend_added',
        userId: friend.id,
        userName: friend.name,
        userAvatar: friend.avatar,
        description: `You are now friends with ${friend.name}`,
        metadata: { friendId: friend.id },
      });

      this.emit('friendRequestAccepted', { request, friend });
      console.log(`Friend request accepted: ${friend.name}`);
    } catch (error) {
      console.error('Failed to accept friend request:', error);
      throw error;
    }
  }

  async declineFriendRequest(requestId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const request = this.friendRequests.get(requestId);
      if (!request || request.status !== 'pending') {
        throw new Error('Friend request not found or already processed');
      }

      request.status = 'declined';
      await this.saveFriendRequests();

      this.emit('friendRequestDeclined', request);
      console.log(`Friend request declined: ${request.fromUserName}`);
    } catch (error) {
      console.error('Failed to decline friend request:', error);
      throw error;
    }
  }

  async removeFriend(friendId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const friend = this.friends.get(friendId);
      if (!friend) {
        throw new Error('Friend not found');
      }

      this.friends.delete(friendId);
      await this.saveFriends();

      this.emit('friendRemoved', friend);
      console.log(`Friend removed: ${friend.name}`);
    } catch (error) {
      console.error('Failed to remove friend:', error);
      throw error;
    }
  }

  async blockUser(userId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      this.blockedUsers.add(userId);

      // Remove from friends if they are friends
      if (this.friends.has(userId)) {
        this.friends.delete(userId);
        await this.saveFriends();
      }

      await this.saveBlockedUsers();

      this.emit('userBlocked', userId);
      console.log(`User blocked: ${userId}`);
    } catch (error) {
      console.error('Failed to block user:', error);
      throw error;
    }
  }

  async unblockUser(userId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      this.blockedUsers.delete(userId);
      await this.saveBlockedUsers();

      this.emit('userUnblocked', userId);
      console.log(`User unblocked: ${userId}`);
    } catch (error) {
      console.error('Failed to unblock user:', error);
      throw error;
    }
  }

  // Group Management
  async createGroup(
    name: string,
    description?: string,
    isPrivate: boolean = false,
  ): Promise<SocialGroup> {
    if (!this.isInitialized) {
      throw new Error('Social Manager not initialized');
    }

    try {
      const group: SocialGroup = {
        id: `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        ownerId: this.currentUserId,
        members: [
          {
            userId: this.currentUserId,
            userName: 'You', // Would be replaced with actual user name
            role: 'owner',
            joinedAt: Date.now(),
            lastActive: Date.now(),
            isOnline: true,
          },
        ],
        maxMembers: 20,
        isPrivate,
        tags: [],
        createdAt: Date.now(),
        lastActivity: Date.now(),
        stats: {
          totalSessions: 0,
          totalPlaytime: 0,
          averageSessionLength: 0,
          memberCount: 1,
        },
      };

      this.groups.set(group.id, group);
      await this.saveGroups();

      this.emit('groupCreated', group);
      console.log(`Group created: ${group.name}`);

      return group;
    } catch (error) {
      console.error('Failed to create group:', error);
      throw error;
    }
  }

  async joinGroup(groupId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const group = this.groups.get(groupId);
      if (!group) {
        throw new Error('Group not found');
      }

      if (group.members.length >= group.maxMembers) {
        throw new Error('Group is full');
      }

      if (group.members.some(member => member.userId === this.currentUserId)) {
        throw new Error('Already a member of this group');
      }

      const newMember: GroupMember = {
        userId: this.currentUserId,
        userName: 'You', // Would be replaced with actual user name
        role: 'member',
        joinedAt: Date.now(),
        lastActive: Date.now(),
        isOnline: true,
      };

      group.members.push(newMember);
      group.stats.memberCount = group.members.length;
      group.lastActivity = Date.now();

      this.groups.set(groupId, group);
      await this.saveGroups();

      this.emit('groupJoined', { group, member: newMember });
      console.log(`Joined group: ${group.name}`);
    } catch (error) {
      console.error('Failed to join group:', error);
      throw error;
    }
  }

  async leaveGroup(groupId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const group = this.groups.get(groupId);
      if (!group) {
        throw new Error('Group not found');
      }

      const memberIndex = group.members.findIndex(member => member.userId === this.currentUserId);
      if (memberIndex === -1) {
        throw new Error('Not a member of this group');
      }

      // If owner is leaving, transfer ownership or delete group
      if (group.ownerId === this.currentUserId) {
        if (group.members.length > 1) {
          // Transfer ownership to next admin or oldest member
          const newOwner =
            group.members.find(m => m.role === 'admin' && m.userId !== this.currentUserId) ||
            group.members.find(m => m.userId !== this.currentUserId);
          if (newOwner) {
            group.ownerId = newOwner.userId;
            newOwner.role = 'owner';
          }
        } else {
          // Delete group if owner is the only member
          this.groups.delete(groupId);
          await this.saveGroups();
          this.emit('groupDeleted', group);
          return;
        }
      }

      group.members.splice(memberIndex, 1);
      group.stats.memberCount = group.members.length;
      group.lastActivity = Date.now();

      this.groups.set(groupId, group);
      await this.saveGroups();

      this.emit('groupLeft', group);
      console.log(`Left group: ${group.name}`);
    } catch (error) {
      console.error('Failed to leave group:', error);
      throw error;
    }
  }

  // Activity Management
  async addActivity(
    activity: Omit<SocialActivity, 'id' | 'timestamp' | 'isVisible'>,
  ): Promise<void> {
    if (!this.isInitialized) return;

    try {
      const newActivity: SocialActivity = {
        ...activity,
        id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        isVisible: true,
      };

      this.activities.unshift(newActivity);

      // Keep only last 100 activities
      if (this.activities.length > 100) {
        this.activities = this.activities.slice(0, 100);
      }

      await this.saveActivities();
      this.emit('activityAdded', newActivity);
    } catch (error) {
      console.error('Failed to add activity:', error);
    }
  }

  // Public API methods
  getFriends(): Friend[] {
    return Array.from(this.friends.values());
  }

  getOnlineFriends(): Friend[] {
    return this.getFriends().filter(friend => friend.status === 'online');
  }

  getFriend(friendId: string): Friend | null {
    return this.friends.get(friendId) || null;
  }

  getPendingFriendRequests(): FriendRequest[] {
    return Array.from(this.friendRequests.values()).filter(
      request => request.status === 'pending' && request.toUserId === this.currentUserId,
    );
  }

  getSentFriendRequests(): FriendRequest[] {
    return Array.from(this.friendRequests.values()).filter(
      request => request.status === 'pending' && request.fromUserId === this.currentUserId,
    );
  }

  getGroups(): SocialGroup[] {
    return Array.from(this.groups.values());
  }

  getGroup(groupId: string): SocialGroup | null {
    return this.groups.get(groupId) || null;
  }

  getRecentActivities(limit: number = 20): SocialActivity[] {
    return this.activities.slice(0, limit);
  }

  isBlocked(userId: string): boolean {
    return this.blockedUsers.has(userId);
  }

  isFriend(userId: string): boolean {
    return this.friends.has(userId);
  }

  getSocialStats(): {
    friendCount: number;
    groupCount: number;
    pendingRequests: number;
    recentActivities: number;
  } {
    return {
      friendCount: this.friends.size,
      groupCount: this.groups.size,
      pendingRequests: this.getPendingFriendRequests().length,
      recentActivities: this.activities.length,
    };
  }
}

export default SocialManager.getInstance();
