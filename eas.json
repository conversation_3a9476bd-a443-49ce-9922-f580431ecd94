{"cli": {"version": ">= 5.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "developmentBuild", "gradleCommand": ":app:assembleDebug", "env": {"EXPO_BUNDLE_APP": "1"}}, "channel": "development"}, "staging": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"EXPO_BUNDLE_APP": "1"}}, "channel": "staging"}, "production": {"distribution": "store", "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease", "env": {"EXPO_BUNDLE_APP": "1"}}, "channel": "production"}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "ABCDEFGHIJ"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "production"}}, "staging": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "ABCDEFGHIJ"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}, "update": {"development": {"channel": "development"}, "staging": {"channel": "staging"}, "production": {"channel": "production"}}}