---
name: 🔧 CI/CD Pipeline Issue
about: Report issues with the CI/CD pipeline, builds, or deployments
title: '[CI/CD] '
labels: ['ci/cd', 'bug']
assignees: []
---

## 🚨 Issue Description

**Brief description of the CI/CD issue:**
<!-- Describe what went wrong with the pipeline, build, or deployment -->

**Pipeline/Workflow affected:**
- [ ] Continuous Integration (CI)
- [ ] Continuous Deployment (CD)
- [ ] Release Management
- [ ] Maintenance/Security Scans
- [ ] Other: ___________

## 📋 Issue Details

**Workflow Run Information:**
- **Run ID**: <!-- GitHub Actions run ID -->
- **Workflow**: <!-- e.g., ci.yml, cd.yml, release.yml -->
- **Branch**: <!-- e.g., main, develop, feature/xyz -->
- **Commit SHA**: <!-- First 8 characters of commit hash -->
- **Triggered by**: <!-- push, pull_request, workflow_dispatch, schedule -->

**Failed Job/Step:**
- **Job Name**: <!-- e.g., test, build, deploy-production -->
- **Step Name**: <!-- e.g., Run tests, Build for iOS -->
- **Error Message**: 
```
<!-- Paste the error message here -->
```

## 🔍 Environment Information

**Platform/Environment:**
- [ ] Development
- [ ] Staging  
- [ ] Production
- [ ] All environments

**Build Platform:**
- [ ] Web
- [ ] Android
- [ ] iOS
- [ ] All platforms

**Runner Information:**
- **OS**: <!-- ubuntu-latest, macos-latest, windows-latest -->
- **Node.js Version**: <!-- e.g., 18.x -->
- **Expo CLI Version**: <!-- e.g., latest, 6.x.x -->

## 📊 Reproduction Steps

**Steps to reproduce the issue:**
1. <!-- Step 1 -->
2. <!-- Step 2 -->
3. <!-- Step 3 -->

**Expected behavior:**
<!-- What should have happened -->

**Actual behavior:**
<!-- What actually happened -->

## 📎 Additional Information

**Logs and Screenshots:**
<!-- Attach relevant logs, screenshots, or error outputs -->

**Related Issues:**
<!-- Link to any related issues or PRs -->

**Recent Changes:**
<!-- Any recent changes that might have caused this issue -->

**Workaround (if any):**
<!-- Temporary solution or workaround used -->

## 🔧 Troubleshooting Attempted

**What have you tried to fix this issue?**
- [ ] Reran the failed workflow
- [ ] Checked environment variables/secrets
- [ ] Reviewed recent code changes
- [ ] Consulted troubleshooting guide
- [ ] Tested locally
- [ ] Other: ___________

**Local Testing Results:**
<!-- Results of running the same commands locally -->

## 📈 Impact Assessment

**Severity:**
- [ ] 🔴 Critical (Production down, security issue)
- [ ] 🟠 High (Blocking deployments)
- [ ] 🟡 Medium (Affecting development workflow)
- [ ] 🟢 Low (Minor inconvenience)

**Affected Areas:**
- [ ] Development workflow
- [ ] Testing pipeline
- [ ] Build process
- [ ] Deployment process
- [ ] Security scanning
- [ ] Performance monitoring

**Business Impact:**
<!-- How does this affect the project timeline or users? -->

## 🎯 Proposed Solution

**Suggested fix (if known):**
<!-- Your ideas for fixing this issue -->

**Alternative approaches:**
<!-- Other potential solutions -->

## ✅ Acceptance Criteria

**This issue will be considered resolved when:**
- [ ] Pipeline runs successfully
- [ ] All tests pass
- [ ] Builds complete without errors
- [ ] Deployments work as expected
- [ ] Documentation is updated (if needed)
- [ ] Prevention measures are in place

## 📚 Additional Context

**Related Documentation:**
- [ ] CI/CD Setup Guide
- [ ] Troubleshooting Guide
- [ ] Security Policy
- [ ] Other: ___________

**External Dependencies:**
<!-- Any external services or tools involved -->

**Timeline Constraints:**
<!-- Any deadlines or time-sensitive aspects -->

---

**Checklist before submitting:**
- [ ] I have searched for existing issues
- [ ] I have provided all required information
- [ ] I have attached relevant logs/screenshots
- [ ] I have tested locally (if applicable)
- [ ] I have consulted the troubleshooting guide
