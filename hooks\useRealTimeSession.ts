import { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import {
  initializeSessionManager,
  createSession,
  joinSession,
  leaveSession,
  startSession,
  pauseSession,
  resumeSession,
  endSession,
  refreshActiveSessions,
  refreshUserSessions,
  updateSession,
  removeSession,
  setCurrentSession,
  clearError,
} from '../store/slices/sessionSlice';
import SessionManager, { GameSession } from '../services/sessions/SessionManager';

export interface UseRealTimeSessionReturn {
  // State
  currentSession: GameSession | null;
  activeSessions: GameSession[];
  userSessions: GameSession[];
  sessionStats: any;
  loading: boolean;
  error: string | null;
  isInitialized: boolean;

  // Actions
  initialize: (userId: string) => Promise<void>;
  createNewSession: (config: Parameters<typeof createSession>[0]) => Promise<void>;
  joinExistingSession: (sessionId: string, inviteCode?: string) => Promise<void>;
  leaveCurrentSession: (sessionId: string) => Promise<void>;
  startCurrentSession: (sessionId: string) => Promise<void>;
  pauseCurrentSession: (sessionId: string) => Promise<void>;
  resumeCurrentSession: (sessionId: string) => Promise<void>;
  endCurrentSession: (sessionId: string, reason: 'completed' | 'cancelled') => Promise<void>;
  refreshSessions: () => Promise<void>;
  clearSessionError: () => void;

  // Utilities
  isHost: (sessionId?: string) => boolean;
  isParticipant: (sessionId?: string) => boolean;
  canJoinSession: (session: GameSession) => boolean;
  canStartSession: (session: GameSession) => boolean;
}

export default function useRealTimeSession(userId?: string): UseRealTimeSessionReturn {
  const dispatch = useDispatch<AppDispatch>();
  const sessionState = useSelector((state: RootState) => state.session);

  // Initialize session manager when userId is provided
  useEffect(() => {
    if (userId && !sessionState.isInitialized) {
      dispatch(initializeSessionManager(userId));
    }
  }, [userId, sessionState.isInitialized, dispatch]);

  // Set up real-time event listeners
  useEffect(() => {
    if (!sessionState.isInitialized) return;

    const handleSessionUpdated = (session: GameSession) => {
      dispatch(updateSession(session));
    };

    const handleSessionEnded = ({ session }: { session: GameSession; reason: string }) => {
      dispatch(removeSession(session.id));
    };

    const handleSessionCreated = (session: GameSession) => {
      dispatch(setCurrentSession(session));
    };

    // Subscribe to SessionManager events
    SessionManager.on('sessionUpdated', handleSessionUpdated);
    SessionManager.on('sessionEnded', handleSessionEnded);
    SessionManager.on('sessionCreated', handleSessionCreated);

    return () => {
      SessionManager.off('sessionUpdated', handleSessionUpdated);
      SessionManager.off('sessionEnded', handleSessionEnded);
      SessionManager.off('sessionCreated', handleSessionCreated);
    };
  }, [sessionState.isInitialized, dispatch]);

  // Action creators
  const initialize = useCallback(async (userId: string) => {
    await dispatch(initializeSessionManager(userId)).unwrap();
  }, [dispatch]);

  const createNewSession = useCallback(async (config: Parameters<typeof createSession>[0]) => {
    await dispatch(createSession(config)).unwrap();
  }, [dispatch]);

  const joinExistingSession = useCallback(async (sessionId: string, inviteCode?: string) => {
    await dispatch(joinSession({ sessionId, inviteCode })).unwrap();
  }, [dispatch]);

  const leaveCurrentSession = useCallback(async (sessionId: string) => {
    await dispatch(leaveSession(sessionId)).unwrap();
  }, [dispatch]);

  const startCurrentSession = useCallback(async (sessionId: string) => {
    await dispatch(startSession(sessionId)).unwrap();
  }, [dispatch]);

  const pauseCurrentSession = useCallback(async (sessionId: string) => {
    await dispatch(pauseSession(sessionId)).unwrap();
  }, [dispatch]);

  const resumeCurrentSession = useCallback(async (sessionId: string) => {
    await dispatch(resumeSession(sessionId)).unwrap();
  }, [dispatch]);

  const endCurrentSession = useCallback(async (sessionId: string, reason: 'completed' | 'cancelled') => {
    await dispatch(endSession({ sessionId, reason })).unwrap();
  }, [dispatch]);

  const refreshSessions = useCallback(async () => {
    if (userId) {
      await Promise.all([
        dispatch(refreshActiveSessions()).unwrap(),
        dispatch(refreshUserSessions(userId)).unwrap(),
      ]);
    }
  }, [dispatch, userId]);

  const clearSessionError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Utility functions
  const isHost = useCallback((sessionId?: string) => {
    if (!userId) return false;
    
    const session = sessionId 
      ? sessionState.activeSessions.find(s => s.id === sessionId) || sessionState.currentSession
      : sessionState.currentSession;
    
    return session?.hostUserId === userId;
  }, [userId, sessionState.activeSessions, sessionState.currentSession]);

  const isParticipant = useCallback((sessionId?: string) => {
    if (!userId) return false;
    
    const session = sessionId 
      ? sessionState.activeSessions.find(s => s.id === sessionId) || sessionState.currentSession
      : sessionState.currentSession;
    
    return session?.participants.has(userId) || false;
  }, [userId, sessionState.activeSessions, sessionState.currentSession]);

  const canJoinSession = useCallback((session: GameSession) => {
    if (!userId) return false;
    
    return (
      !session.participants.has(userId) &&
      session.status === 'waiting' &&
      session.participants.size < session.maxParticipants
    );
  }, [userId]);

  const canStartSession = useCallback((session: GameSession) => {
    if (!userId) return false;
    
    return (
      session.hostUserId === userId &&
      session.status === 'waiting' &&
      session.participants.size >= 2
    );
  }, [userId]);

  return {
    // State
    currentSession: sessionState.currentSession,
    activeSessions: sessionState.activeSessions,
    userSessions: sessionState.userSessions,
    sessionStats: sessionState.sessionStats,
    loading: sessionState.loading,
    error: sessionState.error,
    isInitialized: sessionState.isInitialized,

    // Actions
    initialize,
    createNewSession,
    joinExistingSession,
    leaveCurrentSession,
    startCurrentSession,
    pauseCurrentSession,
    resumeCurrentSession,
    endCurrentSession,
    refreshSessions,
    clearSessionError,

    // Utilities
    isHost,
    isParticipant,
    canJoinSession,
    canStartSession,
  };
}
