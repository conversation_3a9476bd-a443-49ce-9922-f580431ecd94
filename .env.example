# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Expo Configuration
EXPO_TOKEN=your_expo_token_here

# App Configuration
APP_ENV=development
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# API Configuration
API_BASE_URL=https://api.logaco.app
API_TIMEOUT=10000

# Feature Flags
ENABLE_ANALYTICS=false
ENABLE_CRASH_REPORTING=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_DEBUG_MODE=true

# Third-party Services
SENTRY_DSN=your_sentry_dsn_here
ANALYTICS_API_KEY=your_analytics_key_here

# Development Settings
DEV_SERVER_URL=http://localhost:3000
MOCK_API_RESPONSES=true
ENABLE_FLIPPER=true

# Security
SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Notifications
PUSH_NOTIFICATION_KEY=your_push_notification_key_here

# Social Authentication (if needed)
GOOGLE_CLIENT_ID=your_google_client_id_here
APPLE_CLIENT_ID=your_apple_client_id_here

# Database (if using remote database)
DATABASE_URL=your_database_url_here

# Storage
STORAGE_BUCKET=your_storage_bucket_here

# Monitoring
HEALTH_CHECK_URL=https://api.logaco.app/health
METRICS_ENDPOINT=https://metrics.logaco.app

# CI/CD Specific
CI=false
SKIP_PREFLIGHT_CHECK=true
