import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface NotificationSettings {
  enabled: boolean;
  connectionAlerts: boolean;
  gameInvites: boolean;
  sessionUpdates: boolean;
  systemMessages: boolean;
  sound: boolean;
  vibration: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string; // HH:MM format
  };
}

export interface NotificationPermissions {
  status: 'granted' | 'denied' | 'undetermined';
  canAskAgain: boolean;
  allowsAlert: boolean;
  allowsBadge: boolean;
  allowsSound: boolean;
}

class NotificationService {
  private static instance: NotificationService;
  private settings: NotificationSettings | null = null;
  private permissions: NotificationPermissions | null = null;

  private readonly STORAGE_KEY = '@notification_settings';

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async initialize(): Promise<void> {
    // Configure notification behavior
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
      }),
    });

    await this.loadSettings();
    await this.checkPermissions();
  }

  private async loadSettings(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.settings = JSON.parse(stored);
      } else {
        this.settings = this.getDefaultSettings();
        await this.saveSettings();
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
      this.settings = this.getDefaultSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    if (!this.settings) return;

    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    }
  }

  private getDefaultSettings(): NotificationSettings {
    return {
      enabled: true,
      connectionAlerts: true,
      gameInvites: true,
      sessionUpdates: true,
      systemMessages: true,
      sound: true,
      vibration: true,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
    };
  }

  async requestPermissions(): Promise<NotificationPermissions> {
    try {
      const { status, canAskAgain } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: false,
        },
      });

      const settings = await Notifications.getPermissionsAsync();

      this.permissions = {
        status: status as 'granted' | 'denied' | 'undetermined',
        canAskAgain,
        allowsAlert: settings.granted && (settings.ios?.allowsAlert ?? true),
        allowsBadge: settings.granted && (settings.ios?.allowsBadge ?? true),
        allowsSound: settings.granted && (settings.ios?.allowsSound ?? true),
      };

      return this.permissions;
    } catch (error) {
      console.error('Failed to request notification permissions:', error);
      return {
        status: 'denied',
        canAskAgain: false,
        allowsAlert: false,
        allowsBadge: false,
        allowsSound: false,
      };
    }
  }

  async checkPermissions(): Promise<NotificationPermissions> {
    if (this.permissions) {
      return this.permissions;
    }

    try {
      const settings = await Notifications.getPermissionsAsync();

      this.permissions = {
        status: settings.status as 'granted' | 'denied' | 'undetermined',
        canAskAgain: settings.canAskAgain,
        allowsAlert: settings.granted && (settings.ios?.allowsAlert ?? true),
        allowsBadge: settings.granted && (settings.ios?.allowsBadge ?? true),
        allowsSound: settings.granted && (settings.ios?.allowsSound ?? true),
      };

      return this.permissions;
    } catch (error) {
      console.error('Failed to check notification permissions:', error);
      return {
        status: 'denied',
        canAskAgain: false,
        allowsAlert: false,
        allowsBadge: false,
        allowsSound: false,
      };
    }
  }

  async getSettings(): Promise<NotificationSettings> {
    if (!this.settings) {
      await this.loadSettings();
    }
    return this.settings!;
  }

  async updateSettings(updates: Partial<NotificationSettings>): Promise<void> {
    if (!this.settings) {
      await this.loadSettings();
    }

    this.settings = { ...this.settings!, ...updates };
    await this.saveSettings();
  }

  async setEnabled(enabled: boolean): Promise<void> {
    await this.updateSettings({ enabled });
  }

  async setConnectionAlerts(enabled: boolean): Promise<void> {
    await this.updateSettings({ connectionAlerts: enabled });
  }

  async setGameInvites(enabled: boolean): Promise<void> {
    await this.updateSettings({ gameInvites: enabled });
  }

  async setSessionUpdates(enabled: boolean): Promise<void> {
    await this.updateSettings({ sessionUpdates: enabled });
  }

  async setSystemMessages(enabled: boolean): Promise<void> {
    await this.updateSettings({ systemMessages: enabled });
  }

  async setSound(enabled: boolean): Promise<void> {
    await this.updateSettings({ sound: enabled });
  }

  async setVibration(enabled: boolean): Promise<void> {
    await this.updateSettings({ vibration: enabled });
  }

  async setQuietHours(quietHours: NotificationSettings['quietHours']): Promise<void> {
    await this.updateSettings({ quietHours });
  }

  private isInQuietHours(): boolean {
    if (!this.settings?.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { startTime, endTime } = this.settings.quietHours;
    
    // Handle quiet hours that span midnight
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      return currentTime >= startTime && currentTime <= endTime;
    }
  }

  async sendConnectionAlert(deviceName: string): Promise<void> {
    if (!this.shouldSendNotification('connectionAlerts')) return;

    await this.scheduleNotification({
      title: 'Device Connected',
      body: `Connected to ${deviceName}`,
      data: { type: 'connection', deviceName },
    });
  }

  async sendGameInvite(gameName: string, fromDevice: string): Promise<void> {
    if (!this.shouldSendNotification('gameInvites')) return;

    await this.scheduleNotification({
      title: 'Game Invitation',
      body: `${fromDevice} invited you to play ${gameName}`,
      data: { type: 'gameInvite', gameName, fromDevice },
    });
  }

  async sendSessionUpdate(message: string): Promise<void> {
    if (!this.shouldSendNotification('sessionUpdates')) return;

    await this.scheduleNotification({
      title: 'Session Update',
      body: message,
      data: { type: 'sessionUpdate' },
    });
  }

  async sendSystemMessage(title: string, message: string): Promise<void> {
    if (!this.shouldSendNotification('systemMessages')) return;

    await this.scheduleNotification({
      title,
      body: message,
      data: { type: 'systemMessage' },
    });
  }

  private shouldSendNotification(type: keyof NotificationSettings): boolean {
    if (!this.settings?.enabled) return false;
    if (!this.settings[type]) return false;
    if (this.isInQuietHours()) return false;
    if (this.permissions?.status !== 'granted') return false;

    return true;
  }

  private async scheduleNotification(content: {
    title: string;
    body: string;
    data?: any;
  }): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: content.title,
          body: content.body,
          data: content.data,
          sound: this.settings?.sound ? 'default' : undefined,
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to schedule notification:', error);
    }
  }

  async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.dismissAllNotificationsAsync();
    } catch (error) {
      console.error('Failed to clear notifications:', error);
    }
  }

  async openNotificationSettings(): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        await Notifications.openSettingsAsync();
      } else {
        // For Android, we would need to use a native module to open app settings
        console.log('Opening notification settings not implemented for Android');
      }
    } catch (error) {
      console.error('Failed to open notification settings:', error);
    }
  }

  // Test notification for settings screen
  async sendTestNotification(): Promise<void> {
    await this.scheduleNotification({
      title: 'Test Notification',
      body: 'This is a test notification from LoGaCo',
      data: { type: 'test' },
    });
  }

  async getPermissionStatus(): Promise<'granted' | 'denied' | 'undetermined'> {
    const permissions = await this.checkPermissions();
    return permissions.status;
  }

  async isNotificationEnabled(): Promise<boolean> {
    const settings = await this.getSettings();
    const permissions = await this.checkPermissions();
    return settings.enabled && permissions.status === 'granted';
  }
}

export default NotificationService.getInstance();
