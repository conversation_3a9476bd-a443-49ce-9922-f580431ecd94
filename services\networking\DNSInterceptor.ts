import { EventEmitter } from 'events';
import { Platform } from 'react-native';

export interface DNSMapping {
  domain: string;
  targetIP: string;
  gameId?: string;
  ttl: number;
  createdAt: number;
  priority: number;
}

export interface DNSQuery {
  id: string;
  domain: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX';
  timestamp: number;
  sourceIP: string;
  gameId?: string;
}

export interface DNSResponse {
  queryId: string;
  domain: string;
  resolvedIP: string;
  ttl: number;
  cached: boolean;
  intercepted: boolean;
  responseTime: number;
}

export interface DNSStats {
  totalQueries: number;
  interceptedQueries: number;
  cachedResponses: number;
  fallbackQueries: number;
  averageResponseTime: number;
  cacheHitRate: number;
}

class DNSInterceptor extends EventEmitter {
  private static instance: DNSInterceptor;
  private isActive: boolean = false;
  private mappings: Map<string, DNSMapping> = new Map();
  private cache: Map<string, DNSResponse> = new Map();
  private stats: DNSStats = {
    totalQueries: 0,
    interceptedQueries: 0,
    cachedResponses: 0,
    fallbackQueries: 0,
    averageResponseTime: 0,
    cacheHitRate: 0
  };
  private responseTimes: number[] = [];
  private cacheCleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.initializeGameServerMappings();
    this.startCacheCleanup();
  }

  static getInstance(): DNSInterceptor {
    if (!DNSInterceptor.instance) {
      DNSInterceptor.instance = new DNSInterceptor();
    }
    return DNSInterceptor.instance;
  }

  // Initialization
  async initialize(): Promise<void> {
    try {
      console.log('Initializing DNS Interceptor...');
      
      // Platform-specific initialization
      if (Platform.OS === 'android') {
        await this.initializeAndroidDNS();
      } else if (Platform.OS === 'ios') {
        await this.initializeiOSDNS();
      }

      this.isActive = true;
      this.emit('initialized');
      console.log('DNS Interceptor initialized successfully');
    } catch (error) {
      console.error('Failed to initialize DNS Interceptor:', error);
      throw error;
    }
  }

  private async initializeAndroidDNS(): Promise<void> {
    // Android-specific DNS interception setup
    // This would integrate with the VPN service for DNS capture
    console.log('Setting up Android DNS interception...');
  }

  private async initializeiOSDNS(): Promise<void> {
    // iOS-specific DNS interception setup
    // This would use Network Extension framework
    console.log('Setting up iOS DNS interception...');
  }

  // Core DNS Interception
  async interceptQuery(query: DNSQuery): Promise<DNSResponse> {
    const startTime = Date.now();
    this.stats.totalQueries++;

    try {
      // Check cache first
      const cacheKey = `${query.domain}_${query.type}`;
      const cachedResponse = this.cache.get(cacheKey);
      
      if (cachedResponse && this.isCacheValid(cachedResponse)) {
        this.stats.cachedResponses++;
        this.updateStats(Date.now() - startTime);
        
        console.log(`DNS cache hit for ${query.domain}`);
        return {
          ...cachedResponse,
          queryId: query.id,
          cached: true,
          responseTime: Date.now() - startTime
        };
      }

      // Check for domain mapping
      const mapping = this.findMapping(query.domain);
      if (mapping) {
        const response = await this.createMappedResponse(query, mapping, startTime);
        this.cacheResponse(cacheKey, response);
        this.stats.interceptedQueries++;
        this.updateStats(response.responseTime);
        
        console.log(`DNS intercepted: ${query.domain} -> ${mapping.targetIP}`);
        this.emit('queryIntercepted', { query, response });
        return response;
      }

      // Fallback to real DNS
      const response = await this.performRealDNSLookup(query, startTime);
      this.cacheResponse(cacheKey, response);
      this.stats.fallbackQueries++;
      this.updateStats(response.responseTime);
      
      console.log(`DNS fallback for ${query.domain} -> ${response.resolvedIP}`);
      return response;

    } catch (error) {
      console.error(`DNS query failed for ${query.domain}:`, error);
      throw error;
    }
  }

  // Domain Mapping Management
  addMapping(domain: string, targetIP: string, gameId?: string, ttl: number = 3600): void {
    const mapping: DNSMapping = {
      domain: domain.toLowerCase(),
      targetIP,
      gameId,
      ttl,
      createdAt: Date.now(),
      priority: gameId ? 1 : 0 // Game mappings have higher priority
    };

    this.mappings.set(domain.toLowerCase(), mapping);
    console.log(`Added DNS mapping: ${domain} -> ${targetIP}`);
    this.emit('mappingAdded', mapping);
  }

  removeMapping(domain: string): boolean {
    const removed = this.mappings.delete(domain.toLowerCase());
    if (removed) {
      console.log(`Removed DNS mapping for ${domain}`);
      this.emit('mappingRemoved', domain);
    }
    return removed;
  }

  addGameServerMappings(gameId: string, serverMappings: { [domain: string]: string }): void {
    Object.entries(serverMappings).forEach(([domain, ip]) => {
      this.addMapping(domain, ip, gameId);
    });
    console.log(`Added ${Object.keys(serverMappings).length} mappings for game ${gameId}`);
  }

  // Cache Management
  private isCacheValid(response: DNSResponse): boolean {
    const age = Date.now() - response.responseTime;
    return age < (response.ttl * 1000);
  }

  private cacheResponse(key: string, response: DNSResponse): void {
    this.cache.set(key, response);
  }

  clearCache(): void {
    this.cache.clear();
    console.log('DNS cache cleared');
    this.emit('cacheCleared');
  }

  // Helper Methods
  private findMapping(domain: string): DNSMapping | undefined {
    const normalizedDomain = domain.toLowerCase();
    
    // Exact match first
    let mapping = this.mappings.get(normalizedDomain);
    if (mapping) return mapping;

    // Wildcard matching
    for (const [mappedDomain, mappingData] of this.mappings) {
      if (mappedDomain.startsWith('*.')) {
        const pattern = mappedDomain.substring(2);
        if (normalizedDomain.endsWith(pattern)) {
          return mappingData;
        }
      }
    }

    return undefined;
  }

  private async createMappedResponse(
    query: DNSQuery, 
    mapping: DNSMapping, 
    startTime: number
  ): Promise<DNSResponse> {
    return {
      queryId: query.id,
      domain: query.domain,
      resolvedIP: mapping.targetIP,
      ttl: mapping.ttl,
      cached: false,
      intercepted: true,
      responseTime: Date.now() - startTime
    };
  }

  private async performRealDNSLookup(query: DNSQuery, startTime: number): Promise<DNSResponse> {
    // This would perform actual DNS lookup using platform-specific APIs
    // For now, return a mock response
    return {
      queryId: query.id,
      domain: query.domain,
      resolvedIP: '*******', // Mock IP
      ttl: 300,
      cached: false,
      intercepted: false,
      responseTime: Date.now() - startTime
    };
  }

  private initializeGameServerMappings(): void {
    // Minecraft servers
    this.addMapping('*.mojang.com', '127.0.0.1', 'minecraft');
    this.addMapping('*.minecraft.net', '127.0.0.1', 'minecraft');
    this.addMapping('sessionserver.mojang.com', '127.0.0.1', 'minecraft');

    // Among Us servers
    this.addMapping('*.innersloth.com', '127.0.0.1', 'among_us');
    this.addMapping('matchmaker.innersloth.com', '127.0.0.1', 'among_us');

    // Chess.com servers
    this.addMapping('*.chess.com', '127.0.0.1', 'chess');
    this.addMapping('live.chess.com', '127.0.0.1', 'chess');

    console.log('Initialized default game server mappings');
  }

  private updateStats(responseTime: number): void {
    this.responseTimes.push(responseTime);
    
    // Keep only last 1000 response times for average calculation
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000);
    }

    this.stats.averageResponseTime = 
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
    
    this.stats.cacheHitRate = 
      this.stats.totalQueries > 0 ? (this.stats.cachedResponses / this.stats.totalQueries) * 100 : 0;
  }

  private startCacheCleanup(): void {
    this.cacheCleanupInterval = setInterval(() => {
      const now = Date.now();
      for (const [key, response] of this.cache) {
        if (!this.isCacheValid(response)) {
          this.cache.delete(key);
        }
      }
    }, 60000); // Cleanup every minute
  }

  // Public API
  start(): void {
    this.isActive = true;
    console.log('DNS Interceptor started');
    this.emit('started');
  }

  stop(): void {
    this.isActive = false;
    console.log('DNS Interceptor stopped');
    this.emit('stopped');
  }

  getStats(): DNSStats {
    return { ...this.stats };
  }

  getMappings(): DNSMapping[] {
    return Array.from(this.mappings.values());
  }

  isRunning(): boolean {
    return this.isActive;
  }

  // Cleanup
  destroy(): void {
    this.stop();
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
    }
    this.clearCache();
    this.mappings.clear();
    this.removeAllListeners();
  }
}

export default DNSInterceptor;
