import { Stack } from 'expo-router';

export default function ProfileLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: 'transparent',
        },
        headerTintColor: '#00D4FF',
        headerTitleStyle: {
          fontWeight: 'bold',
          color: '#FFFFFF',
        },
        headerTransparent: true,
        headerBlurEffect: 'dark',
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Profile',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="edit"
        options={{
          title: 'Edit Profile',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="avatar"
        options={{
          title: 'Choose Avatar',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="achievements"
        options={{
          title: 'Achievements',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="stats"
        options={{
          title: 'Statistics',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="friends"
        options={{
          title: 'Friends',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="groups"
        options={{
          title: 'Groups',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="leaderboards"
        options={{
          title: 'Leaderboards',
          headerShown: true,
        }}
      />
    </Stack>
  );
}
