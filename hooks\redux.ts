import { useDispatch, useSelector, TypedUseSelectorHook } from "react-redux";
import { createSelector } from "@reduxjs/toolkit";
import type { RootState, AppDispatch } from "../store";

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Convenience hooks for specific slices
export const useConnection = () => useAppSelector((state) => state.connection);
export const useGame = () => useAppSelector((state) => state.game);
export const useUser = () => useAppSelector((state) => state.user);
export const useSettings = () => useAppSelector((state) => state.settings);

// Specific selectors for common use cases
export const useIsConnected = () =>
  useAppSelector((state) => state.connection.connectionStatus === "connected");

export const useConnectedDevices = () =>
  useAppSelector((state) => state.connection.connectedDevices);

export const useNearbyDevices = () =>
  useAppSelector((state) => state.connection.nearbyDevices);

export const useCurrentSession = () =>
  useAppSelector((state) => state.game.currentSession);

export const useInstalledGames = () =>
  useAppSelector((state) => state.game.installedGames);

export const useUserProfile = () =>
  useAppSelector((state) => state.user.profile);

export const useUserPreferences = () =>
  useAppSelector((state) => state.user.preferences);

export const useAppSettings = () =>
  useAppSelector((state) => state.settings.settings);

export const useTheme = () =>
  useAppSelector((state) => state.settings.settings.theme);

export const useNotificationSettings = () =>
  useAppSelector((state) => state.settings.settings.notifications);

export const useConnectionSettings = () =>
  useAppSelector((state) => state.settings.settings.connection);

export const usePrivacySettings = () =>
  useAppSelector((state) => state.settings.settings.privacy);

// Loading state selectors
export const useIsScanning = () =>
  useAppSelector((state) => state.connection.isScanning);

export const useIsConnecting = () =>
  useAppSelector((state) => state.connection.connectionStatus === "connecting");

export const useIsGameScanning = () =>
  useAppSelector((state) => state.game.isScanning);

export const useIsCreatingSession = () =>
  useAppSelector((state) => state.game.isCreatingSession);

export const useIsJoiningSession = () =>
  useAppSelector((state) => state.game.isJoiningSession);

// Error selectors
export const useConnectionError = () =>
  useAppSelector((state) => state.connection.connectionError);

export const useGameError = () => useAppSelector((state) => state.game.error);

export const useUserError = () => useAppSelector((state) => state.user.error);

export const useSettingsError = () =>
  useAppSelector((state) => state.settings.error);

// Memoized selectors
const selectFilteredGames = createSelector(
  [
    (state: RootState) => state.game.installedGames,
    (state: RootState) => state.game.customGames,
    (state: RootState) => state.game.searchQuery,
    (state: RootState) => state.game.selectedFilter,
    (state: RootState) => state.game.recentGames,
  ],
  (installedGames, customGames, searchQuery, selectedFilter, recentGames) => {
    // Early return if no games
    if (!installedGames.length && !customGames.length) {
      return [];
    }

    const allGames = [...installedGames, ...customGames];

    let filtered = allGames;

    // Apply filter
    if (selectedFilter !== "all") {
      filtered = filtered.filter((game) => {
        switch (selectedFilter) {
          case "compatible":
            return game.status === "compatible";
          case "needs_setup":
            return game.status === "needs_setup";
          case "recent":
            return recentGames.some((recent) => recent.id === game.id);
          default:
            return true;
        }
      });
    }

    // Apply search
    if (searchQuery && searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (game) =>
          game.name.toLowerCase().includes(query) ||
          (game.packageName && game.packageName.toLowerCase().includes(query))
      );
    }

    return filtered;
  }
);

// Complex selectors
export const useFilteredGames = () => useAppSelector(selectFilteredGames);

export const useAvailableConnectionMethods = () =>
  useAppSelector((state) => {
    const methods = [];

    if (state.connection.bluetoothEnabled) {
      methods.push("bluetooth");
    }

    if (state.connection.wifiEnabled) {
      methods.push("wifi");
    }

    return methods;
  });

export const useCanConnect = () =>
  useAppSelector((state) => {
    const { bluetoothEnabled, wifiEnabled, locationEnabled } = state.connection;
    return bluetoothEnabled || wifiEnabled || locationEnabled;
  });

export const useHasCompletedSetup = () =>
  useAppSelector((state) => {
    return state.user.hasCompletedOnboarding && state.user.isProfileSetup;
  });

export const useConnectionStats = () =>
  useAppSelector((state) => {
    const { stats } = state.user;
    const { connectionHistory } = state.connection;

    return {
      totalConnections: stats.totalConnections,
      uniqueDevices: stats.devicesConnected.length,
      recentConnections: connectionHistory.slice(0, 5),
      lastConnection: stats.lastConnectionDate,
    };
  });

export const useGameStats = () =>
  useAppSelector((state) => {
    const { stats } = state.user;
    const { gameHistory } = state.game;

    return {
      totalSessions: stats.totalGameSessions,
      totalPlayTime: stats.totalPlayTime,
      averageSessionLength: stats.averageSessionLength,
      longestSession: stats.longestSession,
      favoriteGame: stats.favoriteGame,
      recentSessions: gameHistory.slice(0, 5),
    };
  });
