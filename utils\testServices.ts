/**
 * Test utility to verify that all social services are working correctly
 */
import AchievementManager from '../services/achievements/AchievementManager';
import LeaderboardManager from '../services/leaderboards/LeaderboardManager';
import SocialManager from '../services/social/SocialManager';

export const testServices = async () => {
  const testUserId = 'test_user_' + Date.now();
  
  console.log('🧪 Testing Social Services...');
  
  try {
    // Test AchievementManager
    console.log('📊 Testing AchievementManager...');
    await AchievementManager.initialize(testUserId);
    
    const achievements = AchievementManager.getAchievements();
    console.log(`✅ AchievementManager: Found ${achievements.length} achievements`);
    
    const unlockedAchievements = AchievementManager.getUnlockedAchievements();
    console.log(`✅ AchievementManager: ${unlockedAchievements.length} unlocked achievements`);
    
    const totalPoints = AchievementManager.getTotalPoints();
    console.log(`✅ AchievementManager: ${totalPoints} total points`);
    
    // Test LeaderboardManager
    console.log('🏆 Testing LeaderboardManager...');
    await LeaderboardManager.initialize(testUserId);
    
    const leaderboards = LeaderboardManager.getAllLeaderboards();
    console.log(`✅ LeaderboardManager: Found ${leaderboards.length} leaderboards`);
    
    const activeLeaderboards = LeaderboardManager.getActiveLeaderboards();
    console.log(`✅ LeaderboardManager: ${activeLeaderboards.length} active leaderboards`);
    
    const playerScore = LeaderboardManager.getPlayerScore(testUserId);
    console.log(`✅ LeaderboardManager: Player score:`, playerScore);
    
    // Test SocialManager
    console.log('👥 Testing SocialManager...');
    await SocialManager.initialize(testUserId);
    
    const friends = SocialManager.getFriends();
    console.log(`✅ SocialManager: ${friends.length} friends`);
    
    const groups = SocialManager.getGroups();
    console.log(`✅ SocialManager: ${groups.length} groups`);
    
    const socialStats = SocialManager.getSocialStats();
    console.log(`✅ SocialManager: Social stats:`, socialStats);
    
    console.log('🎉 All services tested successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Service test failed:', error);
    return false;
  }
};

export const testAchievementUnlock = async (userId: string) => {
  try {
    console.log('🏅 Testing achievement unlock...');
    
    await AchievementManager.initialize(userId);
    
    // Test unlocking a connection achievement
    await AchievementManager.trackProgress('first_connection', 1);
    
    // Test unlocking a gaming achievement
    await AchievementManager.trackProgress('first_win', 1);
    
    // Test unlocking a social achievement
    await AchievementManager.trackProgress('social_butterfly', 1);
    
    const unlockedAchievements = AchievementManager.getUnlockedAchievements();
    console.log(`✅ Unlocked ${unlockedAchievements.length} achievements`);
    
    return unlockedAchievements;
  } catch (error) {
    console.error('❌ Achievement unlock test failed:', error);
    return [];
  }
};

export const testLeaderboardUpdate = async (userId: string, userName: string) => {
  try {
    console.log('📈 Testing leaderboard update...');
    
    await LeaderboardManager.initialize(userId);
    
    // Update player stats
    await LeaderboardManager.updatePlayerScore(userId, userName, {
      gamesPlayed: 10,
      gamesWon: 7,
      totalPlaytime: 3600000, // 1 hour
      achievementCount: 5,
      connectionsMade: 15,
      sessionsHosted: 3
    });
    
    const playerScore = LeaderboardManager.getPlayerScore(userId);
    console.log(`✅ Updated player score:`, playerScore);
    
    const rankings = LeaderboardManager.getCurrentPlayerRankings();
    console.log(`✅ Player rankings:`, rankings);
    
    return playerScore;
  } catch (error) {
    console.error('❌ Leaderboard update test failed:', error);
    return null;
  }
};

export const testSocialFeatures = async (userId: string) => {
  try {
    console.log('🤝 Testing social features...');
    
    await SocialManager.initialize(userId);
    
    // Test creating a group
    const group = await SocialManager.createGroup(
      'Test Gaming Group',
      'A group for testing social features',
      false
    );
    console.log(`✅ Created group:`, group.name);
    
    // Test adding activity
    await SocialManager.addActivity({
      type: 'group_joined',
      userId: userId,
      userName: 'Test User',
      description: 'Joined the Test Gaming Group',
      metadata: { groupId: group.id }
    });
    
    const activities = SocialManager.getRecentActivities(5);
    console.log(`✅ Recent activities: ${activities.length}`);
    
    const socialStats = SocialManager.getSocialStats();
    console.log(`✅ Social stats:`, socialStats);
    
    return { group, activities, socialStats };
  } catch (error) {
    console.error('❌ Social features test failed:', error);
    return null;
  }
};

export default {
  testServices,
  testAchievementUnlock,
  testLeaderboardUpdate,
  testSocialFeatures
};
