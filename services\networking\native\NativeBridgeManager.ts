import { Platform } from 'react-native';
import { EventEmitter } from 'events';
import iOSMultipeerConnectivity from './iOSMultipeerConnectivity';
import AndroidVPNService from './AndroidVPNService';

export interface NativeBridgeConfig {
  enableTrafficInterception: boolean;
  enablePeerToPeer: boolean;
  enableNetworkMonitoring: boolean;
  autoStartServices: boolean;
}

export interface PlatformCapabilities {
  hasMultipeerConnectivity: boolean;
  hasVPNService: boolean;
  hasNetworkInterfaces: boolean;
  hasTrafficInterception: boolean;
  hasDeepPacketInspection: boolean;
}

export interface UnifiedNetworkStats {
  platform: string;
  bytesReceived: number;
  bytesSent: number;
  packetsProcessed: number;
  connectedPeers: number;
  isTrafficInterceptionActive: boolean;
  isPeerToPeerActive: boolean;
  lastUpdated: number;
}

class NativeBridgeManager extends EventEmitter {
  private static instance: NativeBridgeManager;
  private config: NativeBridgeConfig;
  private isInitialized: boolean = false;
  private capabilities: PlatformCapabilities;
  private monitoringInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    
    this.config = {
      enableTrafficInterception: true,
      enablePeerToPeer: true,
      enableNetworkMonitoring: true,
      autoStartServices: false
    };

    this.capabilities = this.detectPlatformCapabilities();
  }

  static getInstance(): NativeBridgeManager {
    if (!NativeBridgeManager.instance) {
      NativeBridgeManager.instance = new NativeBridgeManager();
    }
    return NativeBridgeManager.instance;
  }

  // Initialization
  async initialize(config?: Partial<NativeBridgeConfig>): Promise<void> {
    try {
      console.log('Initializing Native Bridge Manager...');

      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Setup platform-specific services
      await this.setupPlatformServices();

      // Setup event forwarding
      this.setupEventForwarding();

      // Start monitoring if enabled
      if (this.config.enableNetworkMonitoring) {
        this.startMonitoring();
      }

      // Auto-start services if configured
      if (this.config.autoStartServices) {
        await this.startAllServices();
      }

      this.isInitialized = true;
      this.emit('initialized', this.capabilities);
      console.log('Native Bridge Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Native Bridge Manager:', error);
      throw error;
    }
  }

  private detectPlatformCapabilities(): PlatformCapabilities {
    const capabilities: PlatformCapabilities = {
      hasMultipeerConnectivity: false,
      hasVPNService: false,
      hasNetworkInterfaces: false,
      hasTrafficInterception: false,
      hasDeepPacketInspection: false
    };

    if (Platform.OS === 'ios') {
      capabilities.hasMultipeerConnectivity = iOSMultipeerConnectivity.isAvailable();
      capabilities.hasNetworkInterfaces = true;
      capabilities.hasTrafficInterception = false; // Limited on iOS
      capabilities.hasDeepPacketInspection = false;
    } else if (Platform.OS === 'android') {
      capabilities.hasVPNService = AndroidVPNService.isAvailable();
      capabilities.hasNetworkInterfaces = true;
      capabilities.hasTrafficInterception = AndroidVPNService.isAvailable();
      capabilities.hasDeepPacketInspection = AndroidVPNService.isAvailable();
    }

    console.log('Platform capabilities detected:', capabilities);
    return capabilities;
  }

  private async setupPlatformServices(): Promise<void> {
    if (Platform.OS === 'ios' && this.config.enablePeerToPeer) {
      // iOS Multipeer Connectivity setup
      iOSMultipeerConnectivity.onPeerConnected((event) => {
        this.emit('peerConnected', { platform: 'ios', ...event });
      });

      iOSMultipeerConnectivity.onPeerDisconnected((event) => {
        this.emit('peerDisconnected', { platform: 'ios', ...event });
      });

      iOSMultipeerConnectivity.onDataReceived((event) => {
        this.emit('dataReceived', { platform: 'ios', ...event });
      });

      console.log('iOS Multipeer Connectivity configured');
    }

    if (Platform.OS === 'android' && this.config.enableTrafficInterception) {
      // Android VPN Service setup
      AndroidVPNService.onVPNStarted((event) => {
        this.emit('trafficInterceptionStarted', { platform: 'android', ...event });
      });

      AndroidVPNService.onVPNStopped((event) => {
        this.emit('trafficInterceptionStopped', { platform: 'android', ...event });
      });

      AndroidVPNService.onGamingPacketDetected((packet) => {
        this.emit('gamingPacketDetected', { platform: 'android', ...packet });
      });

      console.log('Android VPN Service configured');
    }
  }

  private setupEventForwarding(): void {
    // Forward platform-specific events to unified events
    this.on('peerConnected', (event) => {
      console.log(`Peer connected on ${event.platform}:`, event.peerID);
    });

    this.on('gamingPacketDetected', (packet) => {
      console.log(`Gaming packet detected on ${packet.platform}:`, 
        `${packet.sourceIP} -> ${packet.destIP}`);
    });
  }

  // Unified Service Control
  async startAllServices(): Promise<void> {
    const results = [];

    if (Platform.OS === 'ios' && this.config.enablePeerToPeer) {
      try {
        await iOSMultipeerConnectivity.createSession();
        results.push({ service: 'multipeer', status: 'started' });
      } catch (error) {
        results.push({ service: 'multipeer', status: 'failed', error: error.message });
      }
    }

    if (Platform.OS === 'android' && this.config.enableTrafficInterception) {
      try {
        await AndroidVPNService.startTrafficInterception();
        results.push({ service: 'vpn', status: 'started' });
      } catch (error) {
        results.push({ service: 'vpn', status: 'failed', error: error.message });
      }
    }

    this.emit('servicesStarted', results);
    console.log('All services start attempted:', results);
  }

  async stopAllServices(): Promise<void> {
    const results = [];

    if (Platform.OS === 'ios') {
      try {
        await iOSMultipeerConnectivity.stopAdvertising();
        await iOSMultipeerConnectivity.stopBrowsing();
        results.push({ service: 'multipeer', status: 'stopped' });
      } catch (error) {
        results.push({ service: 'multipeer', status: 'failed', error: error.message });
      }
    }

    if (Platform.OS === 'android') {
      try {
        await AndroidVPNService.stopTrafficInterception();
        results.push({ service: 'vpn', status: 'stopped' });
      } catch (error) {
        results.push({ service: 'vpn', status: 'failed', error: error.message });
      }
    }

    this.emit('servicesStopped', results);
    console.log('All services stop attempted:', results);
  }

  // Unified Data Transmission
  async sendDataToPeer(data: any, peerId?: string): Promise<void> {
    const dataString = JSON.stringify({
      type: 'unified_data',
      payload: data,
      timestamp: Date.now(),
      platform: Platform.OS
    });

    if (Platform.OS === 'ios' && this.capabilities.hasMultipeerConnectivity) {
      if (peerId) {
        await iOSMultipeerConnectivity.sendData(dataString, peerId);
      } else {
        await iOSMultipeerConnectivity.broadcastData(dataString);
      }
    } else {
      console.warn('Peer-to-peer data transmission not available on this platform');
    }
  }

  async broadcastGameData(gameData: any): Promise<void> {
    if (Platform.OS === 'ios' && this.capabilities.hasMultipeerConnectivity) {
      await iOSMultipeerConnectivity.sendGameData(gameData);
    } else {
      console.warn('Game data broadcasting not available on this platform');
    }
  }

  // Unified Network Information
  async getUnifiedNetworkStats(): Promise<UnifiedNetworkStats> {
    const stats: UnifiedNetworkStats = {
      platform: Platform.OS,
      bytesReceived: 0,
      bytesSent: 0,
      packetsProcessed: 0,
      connectedPeers: 0,
      isTrafficInterceptionActive: false,
      isPeerToPeerActive: false,
      lastUpdated: Date.now()
    };

    try {
      if (Platform.OS === 'ios' && this.capabilities.hasMultipeerConnectivity) {
        const peers = await iOSMultipeerConnectivity.getConnectedPeers();
        const sessionInfo = await iOSMultipeerConnectivity.getSessionInfo();
        
        stats.connectedPeers = peers.count;
        stats.isPeerToPeerActive = sessionInfo.isAdvertising || sessionInfo.isBrowsing;
      }

      if (Platform.OS === 'android' && this.capabilities.hasVPNService) {
        const vpnStats = await AndroidVPNService.getTrafficStats();
        
        stats.bytesReceived = vpnStats.bytesReceived;
        stats.bytesSent = vpnStats.bytesSent;
        stats.packetsProcessed = vpnStats.packetsProcessed;
        stats.isTrafficInterceptionActive = vpnStats.isActive;
      }
    } catch (error) {
      console.error('Error getting unified network stats:', error);
    }

    return stats;
  }

  async getDetailedNetworkInfo(): Promise<any> {
    const info: any = {
      platform: Platform.OS,
      capabilities: this.capabilities,
      timestamp: Date.now()
    };

    try {
      if (Platform.OS === 'ios' && this.capabilities.hasMultipeerConnectivity) {
        info.multipeer = await iOSMultipeerConnectivity.getSessionInfo();
        info.peers = await iOSMultipeerConnectivity.getConnectedPeers();
      }

      if (Platform.OS === 'android' && this.capabilities.hasVPNService) {
        info.vpn = await AndroidVPNService.getVPNStatus();
        info.networkInterfaces = await AndroidVPNService.getNetworkInterfaces();
        info.systemNetwork = await AndroidVPNService.getSystemNetworkInfo();
      }
    } catch (error) {
      console.error('Error getting detailed network info:', error);
      info.error = error.message;
    }

    return info;
  }

  // Monitoring
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        const stats = await this.getUnifiedNetworkStats();
        this.emit('statsUpdate', stats);
      } catch (error) {
        console.error('Error during monitoring:', error);
      }
    }, 5000); // Update every 5 seconds

    console.log('Network monitoring started');
  }

  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('Network monitoring stopped');
    }
  }

  // Cleanup
  async cleanup(): Promise<void> {
    try {
      this.stopMonitoring();
      await this.stopAllServices();

      if (Platform.OS === 'ios') {
        iOSMultipeerConnectivity.cleanup();
      }

      if (Platform.OS === 'android') {
        AndroidVPNService.cleanup();
      }

      this.removeAllListeners();
      console.log('Native Bridge Manager cleaned up');
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  // Getters
  getCapabilities(): PlatformCapabilities {
    return { ...this.capabilities };
  }

  getConfig(): NativeBridgeConfig {
    return { ...this.config };
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }

  // Platform-specific access
  getiOSMultipeerConnectivity() {
    if (Platform.OS === 'ios') {
      return iOSMultipeerConnectivity;
    }
    throw new Error('iOS Multipeer Connectivity not available on this platform');
  }

  getAndroidVPNService() {
    if (Platform.OS === 'android') {
      return AndroidVPNService;
    }
    throw new Error('Android VPN Service not available on this platform');
  }
}

export default NativeBridgeManager.getInstance();
