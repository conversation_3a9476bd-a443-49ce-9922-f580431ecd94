# 🔧 CI/CD Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the LoGaCo CI/CD pipeline.

## 🚨 Common Issues and Solutions

### 1. Build Failures

#### **Issue**: "Module not found" errors during build
**Symptoms**:
```
Error: Cannot resolve module 'some-package'
```

**Solutions**:
1. **Check package.json dependencies**:
   ```bash
   npm ls some-package
   npm install some-package
   ```

2. **Clear cache and reinstall**:
   ```bash
   npm run clean
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Update transformIgnorePatterns in Jest config**:
   ```json
   "transformIgnorePatterns": [
     "node_modules/(?!(react-native|@react-native|expo|@expo|some-package)/)"
   ]
   ```

#### **Issue**: Expo CLI version mismatch
**Symptoms**:
```
Error: This project requires Expo CLI version X.X.X
```

**Solutions**:
1. **Update Expo CLI version in workflow**:
   ```yaml
   env:
     EXPO_CLI_VERSION: 'latest'  # or specific version
   ```

2. **Check local vs CI versions**:
   ```bash
   npx expo --version
   ```

### 2. Test Failures

#### **Issue**: Tests failing in CI but passing locally
**Symptoms**:
```
Tests: X failed, Y passed
```

**Solutions**:
1. **Check environment differences**:
   ```bash
   # Run tests with CI flag locally
   npm run test:ci
   ```

2. **Update Jest configuration for CI**:
   ```json
   {
     "testEnvironment": "node",
     "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"],
     "testTimeout": 10000
   }
   ```

3. **Mock platform-specific modules**:
   ```javascript
   // In jest-setup.js
   jest.mock('react-native', () => ({
     Platform: { OS: 'ios', select: jest.fn() },
     Dimensions: { get: jest.fn(() => ({ width: 375, height: 667 })) }
   }));
   ```

#### **Issue**: Coverage threshold not met
**Symptoms**:
```
Coverage threshold for lines (80%) not met: 75%
```

**Solutions**:
1. **Add more tests**:
   ```bash
   npm run test:coverage
   # Review coverage report in coverage/lcov-report/index.html
   ```

2. **Exclude files from coverage**:
   ```json
   "collectCoverageFrom": [
     "**/*.{js,jsx,ts,tsx}",
     "!**/node_modules/**",
     "!**/__tests__/**",
     "!**/coverage/**"
   ]
   ```

### 3. Deployment Issues

#### **Issue**: Expo authentication failure
**Symptoms**:
```
Error: Authentication failed
```

**Solutions**:
1. **Check EXPO_TOKEN secret**:
   - Verify token is valid and not expired
   - Generate new token: `npx expo login` → Account Settings → Access Tokens

2. **Update token in GitHub secrets**:
   - Go to repository Settings → Secrets and variables → Actions
   - Update `EXPO_TOKEN` value

#### **Issue**: EAS build failures
**Symptoms**:
```
Build failed with exit code 1
```

**Solutions**:
1. **Check EAS configuration**:
   ```bash
   npx eas build:configure
   npx eas build --platform android --profile development --local
   ```

2. **Verify credentials**:
   ```bash
   npx eas credentials
   ```

3. **Check build logs**:
   - Review detailed logs in Expo dashboard
   - Look for specific error messages

### 4. Security Scan Issues

#### **Issue**: High/Critical vulnerabilities found
**Symptoms**:
```
found X vulnerabilities (Y high, Z critical)
```

**Solutions**:
1. **Update vulnerable packages**:
   ```bash
   npm audit fix
   npm audit fix --force  # Use with caution
   ```

2. **Manual updates**:
   ```bash
   npm update package-name
   ```

3. **Add audit exceptions** (temporary):
   ```bash
   npm audit --audit-level moderate
   ```

#### **Issue**: Snyk scan failures
**Symptoms**:
```
Snyk test failed
```

**Solutions**:
1. **Check Snyk token**:
   - Verify `SNYK_TOKEN` secret is valid
   - Generate new token at snyk.io

2. **Configure Snyk policy**:
   ```yaml
   # .snyk file
   version: v1.0.0
   ignore:
     SNYK-JS-PACKAGE-123456:
       - '*':
           reason: False positive
           expires: '2024-12-31T23:59:59.999Z'
   ```

### 5. Linting and Formatting Issues

#### **Issue**: ESLint errors blocking CI
**Symptoms**:
```
Error: ESLint found problems in X files
```

**Solutions**:
1. **Fix automatically**:
   ```bash
   npm run lint:fix
   ```

2. **Check specific rules**:
   ```bash
   npx eslint . --ext .js,.jsx,.ts,.tsx
   ```

3. **Update ESLint configuration**:
   ```javascript
   // .eslintrc.js
   rules: {
     'problematic-rule': 'warn', // Change from 'error' to 'warn'
   }
   ```

#### **Issue**: Prettier formatting conflicts
**Symptoms**:
```
Code style issues found
```

**Solutions**:
1. **Format all files**:
   ```bash
   npm run format
   ```

2. **Check Prettier configuration**:
   ```bash
   npx prettier --check "**/*.{js,jsx,ts,tsx,json,md}"
   ```

## 🔍 Debugging Workflows

### Local Testing

1. **Run CI pipeline locally**:
   ```bash
   # Install act (GitHub Actions local runner)
   brew install act  # macOS
   # or
   choco install act  # Windows

   # Run workflow locally
   act -j test
   ```

2. **Test individual steps**:
   ```bash
   npm run lint
   npm run type-check
   npm run test:ci
   npm run build
   ```

### Workflow Debugging

1. **Enable debug logging**:
   ```yaml
   - name: Debug step
     run: |
       echo "Debug information"
       env
       ls -la
     env:
       ACTIONS_STEP_DEBUG: true
   ```

2. **Add debugging outputs**:
   ```yaml
   - name: Debug variables
     run: |
       echo "Node version: $(node --version)"
       echo "NPM version: $(npm --version)"
       echo "Working directory: $(pwd)"
       echo "Environment: ${{ github.event_name }}"
   ```

### Log Analysis

1. **GitHub Actions logs**:
   - Go to Actions tab in repository
   - Click on failed workflow run
   - Expand failed job steps
   - Look for error messages and stack traces

2. **Expo build logs**:
   - Check Expo dashboard for detailed build logs
   - Download logs for offline analysis

## 📊 Monitoring and Alerts

### Setting Up Notifications

1. **GitHub notifications**:
   - Go to repository Settings → Notifications
   - Configure email/Slack notifications for workflow failures

2. **Custom notifications**:
   ```yaml
   - name: Notify on failure
     if: failure()
     uses: 8398a7/action-slack@v3
     with:
       status: failure
       webhook_url: ${{ secrets.SLACK_WEBHOOK }}
   ```

### Health Checks

1. **Pipeline health dashboard**:
   ```bash
   # Create simple health check script
   #!/bin/bash
   echo "Checking pipeline health..."
   gh run list --limit 10 --json status,conclusion
   ```

2. **Automated monitoring**:
   ```yaml
   # Add to maintenance workflow
   - name: Check pipeline health
     run: |
       FAILED_RUNS=$(gh run list --limit 10 --json conclusion | jq '[.[] | select(.conclusion == "failure")] | length')
       if [ "$FAILED_RUNS" -gt 3 ]; then
         echo "Warning: High failure rate detected"
         exit 1
       fi
   ```

## 🆘 Emergency Procedures

### Emergency Deployment

1. **Skip tests for critical fixes**:
   ```bash
   gh workflow run cd.yml -f environment=production -f skip_tests=true
   ```

2. **Hotfix deployment**:
   ```bash
   git checkout main
   git checkout -b hotfix/critical-fix
   # Make changes
   git commit -m "hotfix: critical security fix"
   git push origin hotfix/critical-fix
   # Create PR and merge immediately
   ```

### Rollback Procedures

1. **Expo rollback**:
   ```bash
   npx expo publish --release-channel=production-rollback
   ```

2. **App store rollback**:
   - iOS: Use App Store Connect to rollback version
   - Android: Use Google Play Console to halt rollout

### Pipeline Recovery

1. **Reset failed workflows**:
   ```bash
   gh run rerun <run-id>
   ```

2. **Clear cache**:
   ```bash
   gh cache delete --all
   ```

## 📞 Getting Help

### Internal Resources
- Check this troubleshooting guide first
- Review workflow logs and error messages
- Test locally before pushing changes

### External Resources
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Troubleshooting](https://reactnative.dev/docs/troubleshooting)

### Community Support
- [Expo Discord](https://discord.gg/expo)
- [GitHub Community](https://github.community/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/expo)

---

**Remember**: Always test changes locally before pushing to avoid breaking the CI/CD pipeline for the entire team.
