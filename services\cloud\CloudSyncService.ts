import { Platform } from 'react-native';
import { EventEmitter } from 'events';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface CloudProvider {
  id: string;
  name: string;
  type: 'firebase' | 'aws' | 'azure' | 'google-drive' | 'icloud' | 'custom';
  isAvailable: boolean;
  isAuthenticated: boolean;
  storageQuota: {
    used: number;
    total: number;
    unit: 'bytes' | 'kb' | 'mb' | 'gb';
  };
}

export interface SyncData {
  id: string;
  type: 'settings' | 'session-history' | 'game-preferences' | 'user-profile' | 'achievements';
  data: any;
  lastModified: number;
  version: number;
  checksum: string;
  deviceId: string;
}

export interface SyncConflict {
  id: string;
  type: string;
  localData: SyncData;
  remoteData: SyncData;
  conflictReason: 'version-mismatch' | 'timestamp-conflict' | 'checksum-mismatch';
  resolutionStrategy: 'local-wins' | 'remote-wins' | 'merge' | 'manual';
}

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: number;
  pendingUploads: number;
  pendingDownloads: number;
  conflicts: SyncConflict[];
  errors: string[];
}

class CloudSyncService extends EventEmitter {
  private static instance: CloudSyncService;
  private providers: Map<string, CloudProvider> = new Map();
  private activeProvider: CloudProvider | null = null;
  private syncStatus: SyncStatus;
  private syncQueue: SyncData[] = [];
  private isInitialized: boolean = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private deviceId: string = '';

  private constructor() {
    super();
    this.syncStatus = {
      isOnline: false,
      isSyncing: false,
      lastSyncTime: 0,
      pendingUploads: 0,
      pendingDownloads: 0,
      conflicts: [],
      errors: []
    };
  }

  static getInstance(): CloudSyncService {
    if (!CloudSyncService.instance) {
      CloudSyncService.instance = new CloudSyncService();
    }
    return CloudSyncService.instance;
  }

  // Initialization
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Cloud Sync Service...');

      // Generate or retrieve device ID
      this.deviceId = await this.getOrCreateDeviceId();

      // Initialize cloud providers
      await this.initializeProviders();

      // Check network connectivity
      await this.checkConnectivity();

      // Load pending sync data
      await this.loadPendingSyncData();

      // Start automatic sync
      this.startAutoSync();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('Cloud Sync Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Cloud Sync Service:', error);
      throw error;
    }
  }

  private async getOrCreateDeviceId(): Promise<string> {
    try {
      let deviceId = await AsyncStorage.getItem('device_id');
      if (!deviceId) {
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await AsyncStorage.setItem('device_id', deviceId);
      }
      return deviceId;
    } catch (error) {
      console.error('Failed to get device ID:', error);
      return `device_${Date.now()}`;
    }
  }

  private async initializeProviders(): Promise<void> {
    // Initialize available cloud providers
    const providers: CloudProvider[] = [
      {
        id: 'firebase',
        name: 'Firebase',
        type: 'firebase',
        isAvailable: true,
        isAuthenticated: false,
        storageQuota: { used: 0, total: 1, unit: 'gb' }
      },
      {
        id: 'google-drive',
        name: 'Google Drive',
        type: 'google-drive',
        isAvailable: Platform.OS !== 'web',
        isAuthenticated: false,
        storageQuota: { used: 0, total: 15, unit: 'gb' }
      },
      {
        id: 'icloud',
        name: 'iCloud',
        type: 'icloud',
        isAvailable: Platform.OS === 'ios',
        isAuthenticated: false,
        storageQuota: { used: 0, total: 5, unit: 'gb' }
      }
    ];

    providers.forEach(provider => {
      this.providers.set(provider.id, provider);
    });

    // Set default provider
    const defaultProvider = this.providers.get('firebase');
    if (defaultProvider) {
      this.activeProvider = defaultProvider;
    }
  }

  private async checkConnectivity(): Promise<void> {
    try {
      // Check network connectivity
      if (Platform.OS === 'web') {
        this.syncStatus.isOnline = navigator.onLine;
        
        window.addEventListener('online', () => {
          this.syncStatus.isOnline = true;
          this.emit('connectivityChanged', true);
          this.resumeSync();
        });

        window.addEventListener('offline', () => {
          this.syncStatus.isOnline = false;
          this.emit('connectivityChanged', false);
          this.pauseSync();
        });
      } else {
        // React Native - would use @react-native-community/netinfo
        this.syncStatus.isOnline = true; // Assume online for now
      }
    } catch (error) {
      console.error('Failed to check connectivity:', error);
      this.syncStatus.isOnline = false;
    }
  }

  private async loadPendingSyncData(): Promise<void> {
    try {
      const pendingData = await AsyncStorage.getItem('pending_sync_data');
      if (pendingData) {
        this.syncQueue = JSON.parse(pendingData);
        this.syncStatus.pendingUploads = this.syncQueue.length;
      }
    } catch (error) {
      console.error('Failed to load pending sync data:', error);
    }
  }

  // Provider Management
  async authenticateProvider(providerId: string, credentials?: any): Promise<boolean> {
    try {
      const provider = this.providers.get(providerId);
      if (!provider) {
        throw new Error(`Provider ${providerId} not found`);
      }

      // Simulate authentication
      provider.isAuthenticated = true;
      this.activeProvider = provider;

      this.emit('providerAuthenticated', provider);
      console.log(`Authenticated with provider: ${provider.name}`);
      
      return true;
    } catch (error) {
      console.error('Provider authentication failed:', error);
      return false;
    }
  }

  async setActiveProvider(providerId: string): Promise<void> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    if (!provider.isAuthenticated) {
      throw new Error(`Provider ${providerId} not authenticated`);
    }

    this.activeProvider = provider;
    this.emit('activeProviderChanged', provider);
    console.log(`Active provider changed to: ${provider.name}`);
  }

  // Data Synchronization
  async syncData(data: Omit<SyncData, 'id' | 'lastModified' | 'version' | 'checksum' | 'deviceId'>): Promise<void> {
    try {
      if (!this.activeProvider || !this.activeProvider.isAuthenticated) {
        throw new Error('No authenticated cloud provider available');
      }

      const syncData: SyncData = {
        id: this.generateSyncId(data.type),
        type: data.type,
        data: data.data,
        lastModified: Date.now(),
        version: 1,
        checksum: this.calculateChecksum(data.data),
        deviceId: this.deviceId
      };

      // Add to sync queue
      this.syncQueue.push(syncData);
      this.syncStatus.pendingUploads = this.syncQueue.length;

      // Save pending data locally
      await this.savePendingSyncData();

      // Attempt immediate sync if online
      if (this.syncStatus.isOnline) {
        await this.processSyncQueue();
      }

      this.emit('dataSynced', syncData);
      console.log(`Data queued for sync: ${syncData.type}`);
    } catch (error) {
      console.error('Failed to sync data:', error);
      this.syncStatus.errors.push(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  async downloadData(type: string): Promise<SyncData | null> {
    try {
      if (!this.activeProvider || !this.activeProvider.isAuthenticated) {
        throw new Error('No authenticated cloud provider available');
      }

      if (!this.syncStatus.isOnline) {
        throw new Error('No internet connection');
      }

      // Simulate cloud download
      const remoteData = await this.simulateCloudDownload(type);
      
      if (remoteData) {
        // Check for conflicts with local data
        const localData = await this.getLocalData(type);
        if (localData) {
          const conflict = this.detectConflict(localData, remoteData);
          if (conflict) {
            this.syncStatus.conflicts.push(conflict);
            this.emit('conflictDetected', conflict);
            return null;
          }
        }

        // Save downloaded data locally
        await this.saveLocalData(remoteData);
        this.emit('dataDownloaded', remoteData);
        console.log(`Data downloaded: ${type}`);
      }

      return remoteData;
    } catch (error) {
      console.error('Failed to download data:', error);
      this.syncStatus.errors.push(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (this.syncStatus.isSyncing || this.syncQueue.length === 0) {
      return;
    }

    this.syncStatus.isSyncing = true;
    this.emit('syncStarted');

    try {
      while (this.syncQueue.length > 0 && this.syncStatus.isOnline) {
        const syncData = this.syncQueue.shift();
        if (syncData) {
          await this.uploadData(syncData);
          this.syncStatus.pendingUploads = this.syncQueue.length;
        }
      }

      this.syncStatus.lastSyncTime = Date.now();
      await this.savePendingSyncData();
      
      this.emit('syncCompleted');
      console.log('Sync queue processed successfully');
    } catch (error) {
      console.error('Sync queue processing failed:', error);
      this.syncStatus.errors.push(error instanceof Error ? error.message : 'Sync failed');
    } finally {
      this.syncStatus.isSyncing = false;
    }
  }

  private async uploadData(syncData: SyncData): Promise<void> {
    try {
      // Simulate cloud upload
      await this.simulateCloudUpload(syncData);
      console.log(`Data uploaded: ${syncData.type}`);
    } catch (error) {
      // Re-add to queue on failure
      this.syncQueue.unshift(syncData);
      throw error;
    }
  }

  // Conflict Resolution
  async resolveConflict(conflictId: string, resolution: SyncConflict['resolutionStrategy']): Promise<void> {
    try {
      const conflictIndex = this.syncStatus.conflicts.findIndex(c => c.id === conflictId);
      if (conflictIndex === -1) {
        throw new Error('Conflict not found');
      }

      const conflict = this.syncStatus.conflicts[conflictIndex];
      let resolvedData: SyncData;

      switch (resolution) {
        case 'local-wins':
          resolvedData = conflict.localData;
          break;
        case 'remote-wins':
          resolvedData = conflict.remoteData;
          break;
        case 'merge':
          resolvedData = await this.mergeData(conflict.localData, conflict.remoteData);
          break;
        default:
          throw new Error('Manual resolution not implemented');
      }

      // Save resolved data
      await this.saveLocalData(resolvedData);
      await this.uploadData(resolvedData);

      // Remove conflict
      this.syncStatus.conflicts.splice(conflictIndex, 1);
      
      this.emit('conflictResolved', { conflictId, resolution, data: resolvedData });
      console.log(`Conflict resolved: ${conflictId} using ${resolution}`);
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      throw error;
    }
  }

  private detectConflict(localData: SyncData, remoteData: SyncData): SyncConflict | null {
    if (localData.checksum !== remoteData.checksum) {
      return {
        id: `conflict_${Date.now()}`,
        type: localData.type,
        localData,
        remoteData,
        conflictReason: 'checksum-mismatch',
        resolutionStrategy: 'manual'
      };
    }

    if (localData.version !== remoteData.version) {
      return {
        id: `conflict_${Date.now()}`,
        type: localData.type,
        localData,
        remoteData,
        conflictReason: 'version-mismatch',
        resolutionStrategy: 'remote-wins'
      };
    }

    return null;
  }

  private async mergeData(localData: SyncData, remoteData: SyncData): Promise<SyncData> {
    // Simple merge strategy - combine objects
    const mergedData = {
      ...localData.data,
      ...remoteData.data,
      _mergedAt: Date.now()
    };

    return {
      ...localData,
      data: mergedData,
      lastModified: Date.now(),
      version: Math.max(localData.version, remoteData.version) + 1,
      checksum: this.calculateChecksum(mergedData)
    };
  }

  // Auto Sync
  private startAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      if (this.syncStatus.isOnline && !this.syncStatus.isSyncing) {
        await this.processSyncQueue();
      }
    }, 30000); // Sync every 30 seconds
  }

  private pauseSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  private resumeSync(): void {
    this.startAutoSync();
    this.processSyncQueue();
  }

  // Utility Methods
  private generateSyncId(type: string): string {
    return `${type}_${this.deviceId}_${Date.now()}`;
  }

  private calculateChecksum(data: any): string {
    // Simple checksum calculation
    const jsonString = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  private async simulateCloudUpload(data: SyncData): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
    
    // Simulate occasional failures
    if (Math.random() < 0.1) {
      throw new Error('Upload failed - network error');
    }
  }

  private async simulateCloudDownload(type: string): Promise<SyncData | null> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
    
    // Simulate data availability
    if (Math.random() < 0.7) {
      return {
        id: this.generateSyncId(type),
        type,
        data: { example: 'remote data', timestamp: Date.now() },
        lastModified: Date.now() - 60000,
        version: 1,
        checksum: 'abc123',
        deviceId: 'remote_device'
      };
    }
    
    return null;
  }

  private async saveLocalData(data: SyncData): Promise<void> {
    await AsyncStorage.setItem(`sync_${data.type}`, JSON.stringify(data));
  }

  private async getLocalData(type: string): Promise<SyncData | null> {
    try {
      const data = await AsyncStorage.getItem(`sync_${type}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      return null;
    }
  }

  private async savePendingSyncData(): Promise<void> {
    await AsyncStorage.setItem('pending_sync_data', JSON.stringify(this.syncQueue));
  }

  // Getters
  getProviders(): CloudProvider[] {
    return Array.from(this.providers.values());
  }

  getActiveProvider(): CloudProvider | null {
    return this.activeProvider;
  }

  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }
}

export default CloudSyncService.getInstance();
