import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';

import { RootState } from '../../store';
import { updateProfile } from '../../store/slices/userSlice';

export default function AvatarScreen() {
  const dispatch = useDispatch();
  const userProfile = useSelector((state: RootState) => state.user.profile);
  
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(userProfile?.avatar || null);
  const [saving, setSaving] = useState(false);

  // Predefined avatar options (icons)
  const avatarOptions = [
    { id: 'person', icon: 'person', color: '#00D4FF' },
    { id: 'person-circle', icon: 'person-circle', color: '#00FF88' },
    { id: 'happy', icon: 'happy', color: '#FFD700' },
    { id: 'game-controller', icon: 'game-controller', color: '#9D4EDD' },
    { id: 'rocket', icon: 'rocket', color: '#FF6B6B' },
    { id: 'star', icon: 'star', color: '#FFA500' },
    { id: 'flash', icon: 'flash', color: '#00BFFF' },
    { id: 'heart', icon: 'heart', color: '#FF69B4' },
    { id: 'diamond', icon: 'diamond', color: '#40E0D0' },
    { id: 'trophy', icon: 'trophy', color: '#FFD700' },
    { id: 'shield', icon: 'shield', color: '#32CD32' },
    { id: 'flame', icon: 'flame', color: '#FF4500' },
  ];

  const handleSave = async () => {
    try {
      setSaving(true);
      
      dispatch(updateProfile({
        avatar: selectedAvatar || undefined,
      }));

      Alert.alert('Success', 'Avatar updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update avatar. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const AvatarOption = ({ option }: { option: typeof avatarOptions[0] }) => (
    <TouchableOpacity
      style={[
        styles.avatarOption,
        selectedAvatar === option.id && styles.avatarOptionSelected
      ]}
      onPress={() => setSelectedAvatar(option.id)}
    >
      <BlurView intensity={15} style={styles.avatarBlur}>
        <View style={[
          styles.avatarIcon,
          { backgroundColor: selectedAvatar === option.id ? option.color : 'rgba(255,255,255,0.1)' }
        ]}>
          <Ionicons 
            name={option.icon as any} 
            size={32} 
            color={selectedAvatar === option.id ? '#000' : option.color} 
          />
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Current Avatar Preview */}
          <View style={styles.previewSection}>
            <Text style={styles.sectionTitle}>Current Avatar</Text>
            <View style={styles.currentAvatar}>
              <BlurView intensity={15} style={styles.currentAvatarBlur}>
                {selectedAvatar ? (
                  <View style={[
                    styles.currentAvatarIcon,
                    { backgroundColor: avatarOptions.find(a => a.id === selectedAvatar)?.color || '#00D4FF' }
                  ]}>
                    <Ionicons 
                      name={avatarOptions.find(a => a.id === selectedAvatar)?.icon as any || 'person'} 
                      size={48} 
                      color="#000" 
                    />
                  </View>
                ) : (
                  <View style={styles.currentAvatarIcon}>
                    <Ionicons name="person" size={48} color="#00D4FF" />
                  </View>
                )}
              </BlurView>
            </View>
          </View>

          {/* Avatar Options */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Choose Avatar</Text>
            <Text style={styles.sectionDescription}>
              Select an icon to represent your profile
            </Text>
            
            <View style={styles.avatarGrid}>
              {avatarOptions.map((option) => (
                <AvatarOption key={option.id} option={option} />
              ))}
            </View>
          </View>

          {/* Remove Avatar Option */}
          <View style={styles.section}>
            <TouchableOpacity
              style={[
                styles.removeOption,
                !selectedAvatar && styles.removeOptionSelected
              ]}
              onPress={() => setSelectedAvatar(null)}
            >
              <BlurView intensity={15} style={styles.removeBlur}>
                <Ionicons name="close-circle" size={24} color="#FF4444" />
                <Text style={styles.removeText}>Remove Avatar</Text>
              </BlurView>
            </TouchableOpacity>
          </View>

          {/* Save Button */}
          <TouchableOpacity
            style={[styles.saveButton, saving && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={saving}
          >
            <BlurView intensity={20} style={styles.saveBlur}>
              {saving ? (
                <Text style={styles.saveButtonText}>Saving...</Text>
              ) : (
                <>
                  <Ionicons name="checkmark" size={20} color="#000" />
                  <Text style={styles.saveButtonText}>Save Avatar</Text>
                </>
              )}
            </BlurView>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 100,
  },
  previewSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 16,
    lineHeight: 20,
  },
  currentAvatar: {
    marginTop: 16,
  },
  currentAvatarBlur: {
    borderRadius: 60,
    overflow: 'hidden',
  },
  currentAvatarIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#00D4FF',
  },
  avatarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  avatarOption: {
    width: '22%',
    aspectRatio: 1,
  },
  avatarOptionSelected: {
    transform: [{ scale: 1.1 }],
  },
  avatarBlur: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  avatarIcon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  removeOption: {
    marginBottom: 16,
  },
  removeOptionSelected: {
    transform: [{ scale: 1.02 }],
  },
  removeBlur: {
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  removeText: {
    fontSize: 16,
    color: '#FF4444',
    marginLeft: 8,
    fontWeight: '600',
  },
  saveButton: {
    marginTop: 20,
    marginBottom: 40,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveBlur: {
    borderRadius: 16,
    padding: 18,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00D4FF',
    overflow: 'hidden',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
