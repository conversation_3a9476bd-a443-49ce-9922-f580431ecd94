import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { AchievementBadge } from '../../components/achievements';
import { LeaderboardCard } from '../../components/leaderboards';
import AchievementManager from '../../services/achievements/AchievementManager';
import LeaderboardManager from '../../services/leaderboards/LeaderboardManager';

const { width, height } = Dimensions.get('window');

export default function ProfileTab() {
  const userProfile = useSelector((state: RootState) => state.user.profile);
  const userStats = useSelector((state: RootState) => state.user.stats);

  // Create a fallback profile if none exists
  const effectiveProfile = userProfile || {
    id: 'temp_user_' + Date.now(),
    name: 'Player',
    deviceName: 'Unknown Device',
    status: 'offline' as const,
    achievements: [],
    totalPoints: 0,
    level: 1,
    friendCount: 0,
    groupCount: 0,
  };

  const [achievements, setAchievements] = useState<any[]>([]);
  const [rankings, setRankings] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfileData();
  }, []);

  const loadProfileData = async () => {
    try {
      setLoading(true);

      // Initialize services
      await AchievementManager.initialize(effectiveProfile.id);
      await LeaderboardManager.initialize(effectiveProfile.id);

      // Load achievements
      const userAchievements = await AchievementManager.getUserAchievements();
      setAchievements(userAchievements.slice(0, 6)); // Show top 6

      // Load rankings
      const leaderboards = await LeaderboardManager.getLeaderboards();
      setRankings(leaderboards.slice(0, 3)); // Show top 3
    } catch (error) {
      console.error('Failed to load profile data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadProfileData();
    setRefreshing(false);
  };

  const navigateToFullProfile = () => {
    router.push('/profile/');
  };

  const navigateToAchievements = () => {
    router.push('/profile/achievements');
  };

  const navigateToLeaderboards = () => {
    router.push('/profile/leaderboards');
  };

  const navigateToFriends = () => {
    router.push('/profile/friends');
  };

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#1a1a2e', '#16213e', '#0f3460']} style={styles.background} />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor="#00D4FF"
              colors={['#00D4FF']}
            />
          }
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Profile</Text>
            <TouchableOpacity style={styles.editButton} onPress={navigateToFullProfile}>
              <Ionicons name="settings" size={24} color="#00D4FF" />
            </TouchableOpacity>
          </View>

          {/* Profile Card */}
          <BlurView intensity={15} style={styles.profileCard}>
            <TouchableOpacity onPress={navigateToFullProfile}>
              <View style={styles.profileContent}>
                <View style={styles.avatar}>
                  <Ionicons name="person" size={48} color="#00D4FF" />
                </View>
                <View style={styles.profileInfo}>
                  <Text style={styles.profileName}>{effectiveProfile.name}</Text>
                  <Text style={styles.profileDevice}>{effectiveProfile.deviceName}</Text>
                  <View style={styles.levelContainer}>
                    <Ionicons name="star" size={16} color="#FFB800" />
                    <Text style={styles.levelText}>Level {effectiveProfile.level}</Text>
                    <Text style={styles.pointsText}>{effectiveProfile.totalPoints} pts</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.5)" />
              </View>
            </TouchableOpacity>
          </BlurView>

          {/* Quick Stats */}
          <View style={styles.statsContainer}>
            <TouchableOpacity style={styles.statCard} onPress={navigateToFriends}>
              <BlurView intensity={10} style={styles.statBlur}>
                <Ionicons name="people" size={24} color="#00D4FF" />
                <Text style={styles.statNumber}>{effectiveProfile.friendCount}</Text>
                <Text style={styles.statLabel}>Friends</Text>
              </BlurView>
            </TouchableOpacity>

            <TouchableOpacity style={styles.statCard} onPress={navigateToAchievements}>
              <BlurView intensity={10} style={styles.statBlur}>
                <Ionicons name="trophy" size={24} color="#FFB800" />
                <Text style={styles.statNumber}>{achievements.length}</Text>
                <Text style={styles.statLabel}>Achievements</Text>
              </BlurView>
            </TouchableOpacity>

            <TouchableOpacity style={styles.statCard} onPress={navigateToLeaderboards}>
              <BlurView intensity={10} style={styles.statBlur}>
                <Ionicons name="podium" size={24} color="#00FF88" />
                <Text style={styles.statNumber}>
                  #{rankings.length > 0 ? rankings[0]?.rank || 'N/A' : 'N/A'}
                </Text>
                <Text style={styles.statLabel}>Rank</Text>
              </BlurView>
            </TouchableOpacity>
          </View>

          {/* Recent Achievements */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Achievements</Text>
              <TouchableOpacity onPress={navigateToAchievements}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>

            {achievements.length > 0 ? (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.achievementsScroll}
              >
                {achievements.map((achievement, index) => (
                  <AchievementBadge
                    key={achievement.id || index}
                    achievement={achievement}
                    size="medium"
                    style={styles.achievementBadge}
                  />
                ))}
              </ScrollView>
            ) : (
              <BlurView intensity={10} style={styles.emptyCard}>
                <Ionicons name="trophy-outline" size={32} color="rgba(255, 255, 255, 0.3)" />
                <Text style={styles.emptyText}>No achievements yet</Text>
                <Text style={styles.emptySubtext}>Start playing to earn achievements!</Text>
              </BlurView>
            )}
          </View>

          {/* Leaderboards Preview */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Leaderboards</Text>
              <TouchableOpacity onPress={navigateToLeaderboards}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>

            {rankings.length > 0 ? (
              rankings
                .slice(0, 2)
                .map((leaderboard, index) => (
                  <LeaderboardCard
                    key={leaderboard.id || index}
                    leaderboard={leaderboard}
                    variant="compact"
                    style={styles.leaderboardCard}
                  />
                ))
            ) : (
              <BlurView intensity={10} style={styles.emptyCard}>
                <Ionicons name="podium-outline" size={32} color="rgba(255, 255, 255, 0.3)" />
                <Text style={styles.emptyText}>No rankings yet</Text>
                <Text style={styles.emptySubtext}>Play games to appear on leaderboards!</Text>
              </BlurView>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  editButton: {
    padding: 8,
  },
  profileCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  profileContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  profileDevice: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 8,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  levelText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFB800',
  },
  pointsText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  statBlur: {
    padding: 16,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  seeAllText: {
    fontSize: 14,
    color: '#00D4FF',
    fontWeight: '500',
  },
  achievementsScroll: {
    paddingLeft: 20,
  },
  achievementBadge: {
    marginRight: 12,
  },
  leaderboardCard: {
    marginHorizontal: 20,
    marginBottom: 8,
  },
  emptyCard: {
    marginHorizontal: 20,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.4)',
    textAlign: 'center',
  },
});
