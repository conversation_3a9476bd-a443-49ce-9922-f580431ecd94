import GameDetector from '../../services/game/GameDetector';
import GameDetectionEnhancer from '../../services/game/GameDetectionEnhancer';
import GameDetectionTroubleshooter from '../../services/game/GameDetectionTroubleshooter';

// Mock React Native modules
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android',
    Version: 30,
  },
  NativeModules: {},
  PermissionsAndroid: {
    request: jest.fn(() => Promise.resolve('granted')),
    check: jest.fn(() => Promise.resolve(true)),
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
    },
  },
  Linking: {
    canOpenURL: jest.fn(() => Promise.resolve(true)),
  },
}));

jest.mock('expo-device', () => ({
  isDevice: true,
  modelName: 'Test Device',
  brand: 'Test Brand',
}));

jest.mock('expo-file-system', () => ({
  documentDirectory: '/test/documents/',
  getInfoAsync: jest.fn(() => Promise.resolve({ exists: true, isDirectory: true, size: 1024 })),
  readDirectoryAsync: jest.fn(() => Promise.resolve(['com.test.game', 'com.example.app'])),
  writeAsStringAsync: jest.fn(() => Promise.resolve()),
  readAsStringAsync: jest.fn(() => Promise.resolve('test')),
  deleteAsync: jest.fn(() => Promise.resolve()),
}));

describe('Game Detection Audit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GameDetector Integration', () => {
    it('should successfully scan for games with enhanced detection', async () => {
      const games = await GameDetector.scanForGames();
      
      expect(games).toBeDefined();
      expect(Array.isArray(games)).toBe(true);
      expect(games.length).toBeGreaterThan(0);
      
      // Verify game structure
      if (games.length > 0) {
        const game = games[0];
        expect(game).toHaveProperty('id');
        expect(game).toHaveProperty('name');
        expect(game).toHaveProperty('packageName');
        expect(game).toHaveProperty('isInstalled');
        expect(game).toHaveProperty('supportsMultiplayer');
        expect(game).toHaveProperty('multiplayerType');
      }
    });

    it('should handle game detection errors gracefully', async () => {
      // Mock a failure scenario
      const originalConsoleError = console.error;
      console.error = jest.fn();

      try {
        const games = await GameDetector.scanForGames();
        // Should still return an array even if detection fails
        expect(Array.isArray(games)).toBe(true);
      } finally {
        console.error = originalConsoleError;
      }
    });

    it('should provide multiplayer games filtering', async () => {
      const multiplayerGames = await GameDetector.getMultiplayerGames();
      
      expect(Array.isArray(multiplayerGames)).toBe(true);
      
      // All returned games should support multiplayer
      multiplayerGames.forEach(game => {
        expect(game.supportsMultiplayer).toBe(true);
      });
    });

    it('should provide local multiplayer games filtering', async () => {
      const localGames = await GameDetector.getLocalMultiplayerGames();
      
      expect(Array.isArray(localGames)).toBe(true);
      
      // All returned games should support local multiplayer
      localGames.forEach(game => {
        expect(game.supportsMultiplayer).toBe(true);
        expect(['local', 'both']).toContain(game.multiplayerType);
      });
    });
  });

  describe('GameDetectionEnhancer', () => {
    it('should run enhanced game detection', async () => {
      const result = await GameDetectionEnhancer.enhancedGameDetection();
      
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('games');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('permissions');
      
      expect(Array.isArray(result.games)).toBe(true);
      expect(Array.isArray(result.errors)).toBe(true);
      expect(result.permissions).toHaveProperty('granted');
      expect(result.permissions).toHaveProperty('denied');
      expect(result.permissions).toHaveProperty('required');
    });

    it('should handle permission requests', async () => {
      const result = await GameDetectionEnhancer.enhancedGameDetection();
      
      expect(result.permissions.required.length).toBeGreaterThan(0);
      // Should have attempted to grant permissions
      expect(result.permissions.granted.length + result.permissions.denied.length)
        .toBeGreaterThanOrEqual(0);
    });
  });

  describe('GameDetectionTroubleshooter', () => {
    it('should run full diagnostics', async () => {
      const report = await GameDetectionTroubleshooter.runFullDiagnostics();
      
      expect(report).toHaveProperty('deviceInfo');
      expect(report).toHaveProperty('permissions');
      expect(report).toHaveProperty('steps');
      expect(report).toHaveProperty('overallStatus');
      expect(report).toHaveProperty('recommendations');
      expect(report).toHaveProperty('autoFixAvailable');
      
      // Device info should be populated
      expect(report.deviceInfo.platform).toBeDefined();
      expect(report.deviceInfo.version).toBeDefined();
      
      // Should have troubleshooting steps
      expect(Array.isArray(report.steps)).toBe(true);
      expect(report.steps.length).toBeGreaterThan(0);
      
      // Each step should have required properties
      report.steps.forEach(step => {
        expect(step).toHaveProperty('id');
        expect(step).toHaveProperty('name');
        expect(step).toHaveProperty('description');
        expect(step).toHaveProperty('status');
        expect(['pending', 'running', 'success', 'warning', 'error']).toContain(step.status);
      });
    });

    it('should provide meaningful recommendations', async () => {
      const report = await GameDetectionTroubleshooter.runFullDiagnostics();
      
      expect(Array.isArray(report.recommendations)).toBe(true);
      
      // Should have platform-specific recommendations
      if (report.overallStatus !== 'healthy') {
        expect(report.recommendations.length).toBeGreaterThan(0);
      }
    });

    it('should identify auto-fix opportunities', async () => {
      const report = await GameDetectionTroubleshooter.runFullDiagnostics();
      
      expect(typeof report.autoFixAvailable).toBe('boolean');
      
      if (report.autoFixAvailable) {
        const autoFixResult = await GameDetectionTroubleshooter.runAutoFix();
        
        expect(autoFixResult).toHaveProperty('success');
        expect(autoFixResult).toHaveProperty('fixedSteps');
        expect(typeof autoFixResult.success).toBe('boolean');
        expect(Array.isArray(autoFixResult.fixedSteps)).toBe(true);
      }
    });
  });

  describe('Integration Tests', () => {
    it('should maintain game detection functionality after enhancements', async () => {
      // Test the complete flow
      const troubleshootingReport = await GameDetectionTroubleshooter.runFullDiagnostics();
      const enhancedResult = await GameDetectionEnhancer.enhancedGameDetection();
      const standardGames = await GameDetector.scanForGames();
      
      // All should complete without throwing
      expect(troubleshootingReport).toBeDefined();
      expect(enhancedResult).toBeDefined();
      expect(standardGames).toBeDefined();
      
      // Should have some games available (either real or mock)
      expect(standardGames.length).toBeGreaterThan(0);
    });

    it('should handle error scenarios gracefully', async () => {
      // Mock various error conditions
      const originalConsoleError = console.error;
      const originalConsoleWarn = console.warn;
      console.error = jest.fn();
      console.warn = jest.fn();

      try {
        // Should not throw even with mocked errors
        await expect(GameDetector.scanForGames()).resolves.toBeDefined();
        await expect(GameDetectionEnhancer.enhancedGameDetection()).resolves.toBeDefined();
        await expect(GameDetectionTroubleshooter.runFullDiagnostics()).resolves.toBeDefined();
      } finally {
        console.error = originalConsoleError;
        console.warn = originalConsoleWarn;
      }
    });

    it('should provide consistent game data structure', async () => {
      const games = await GameDetector.scanForGames();
      
      games.forEach(game => {
        // Required properties
        expect(typeof game.id).toBe('string');
        expect(typeof game.name).toBe('string');
        expect(typeof game.packageName).toBe('string');
        expect(typeof game.isInstalled).toBe('boolean');
        expect(typeof game.supportsMultiplayer).toBe('boolean');
        expect(['local', 'online', 'both', 'none']).toContain(game.multiplayerType);
        expect(typeof game.category).toBe('string');
        expect(typeof game.developer).toBe('string');
        
        // Optional properties should be correct type if present
        if (game.version !== undefined) {
          expect(typeof game.version).toBe('string');
        }
        if (game.lastPlayed !== undefined) {
          expect(typeof game.lastPlayed).toBe('number');
        }
        if (game.playtime !== undefined) {
          expect(typeof game.playtime).toBe('number');
        }
        if (game.rating !== undefined) {
          expect(typeof game.rating).toBe('number');
          expect(game.rating).toBeGreaterThanOrEqual(0);
          expect(game.rating).toBeLessThanOrEqual(5);
        }
      });
    });
  });

  describe('Performance Tests', () => {
    it('should complete game detection within reasonable time', async () => {
      const startTime = Date.now();
      await GameDetector.scanForGames();
      const endTime = Date.now();
      
      const duration = endTime - startTime;
      // Should complete within 10 seconds
      expect(duration).toBeLessThan(10000);
    });

    it('should handle concurrent detection requests', async () => {
      const promises = [
        GameDetector.scanForGames(),
        GameDetector.getMultiplayerGames(),
        GameDetector.getLocalMultiplayerGames(),
      ];
      
      const results = await Promise.all(promises);
      
      // All should complete successfully
      results.forEach(result => {
        expect(Array.isArray(result)).toBe(true);
      });
    });
  });
});

describe('Audit Verification', () => {
  it('should verify all critical fixes are in place', () => {
    // This test verifies that the audit fixes are properly implemented
    
    // 1. GameDetectionEnhancer should be available
    expect(GameDetectionEnhancer).toBeDefined();
    expect(typeof GameDetectionEnhancer.enhancedGameDetection).toBe('function');
    
    // 2. GameDetectionTroubleshooter should be available
    expect(GameDetectionTroubleshooter).toBeDefined();
    expect(typeof GameDetectionTroubleshooter.runFullDiagnostics).toBe('function');
    expect(typeof GameDetectionTroubleshooter.runAutoFix).toBe('function');
    
    // 3. GameDetector should have enhanced integration
    expect(GameDetector).toBeDefined();
    expect(typeof GameDetector.scanForGames).toBe('function');
    expect(typeof GameDetector.getMultiplayerGames).toBe('function');
    expect(typeof GameDetector.getLocalMultiplayerGames).toBe('function');
  });

  it('should verify error handling improvements', async () => {
    // Mock console to capture error handling
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    const warnSpy = jest.spyOn(console, 'warn').mockImplementation();

    try {
      // These should handle errors gracefully without throwing
      await GameDetector.scanForGames();
      await GameDetectionEnhancer.enhancedGameDetection();
      await GameDetectionTroubleshooter.runFullDiagnostics();
      
      // Should have proper error logging if issues occur
      // But should not throw unhandled exceptions
    } finally {
      consoleSpy.mockRestore();
      warnSpy.mockRestore();
    }
  });
});
