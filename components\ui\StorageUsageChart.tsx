import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { StorageInfo } from '../../services/DeviceInfoService';

const { width } = Dimensions.get('window');

interface StorageUsageChartProps {
  storageInfo: StorageInfo;
  onOptimize?: () => void;
  onClearCache?: () => void;
}

interface StorageSegment {
  label: string;
  value: number;
  color: string;
  icon: keyof typeof Ionicons.glyphMap;
}

export default function StorageUsageChart({
  storageInfo,
  onOptimize,
  onClearCache,
}: StorageUsageChartProps) {
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getStorageSegments = (): StorageSegment[] => {
    const { breakdown } = storageInfo;
    const total = storageInfo.totalSpace;

    return [
      {
        label: 'App',
        value: breakdown.app,
        color: '#00D4FF',
        icon: 'phone-portrait',
      },
      {
        label: 'Cache',
        value: breakdown.cache,
        color: '#8B5CF6',
        icon: 'layers',
      },
      {
        label: 'Documents',
        value: breakdown.documents,
        color: '#10B981',
        icon: 'document-text',
      },
      {
        label: 'Other',
        value: breakdown.other,
        color: '#F59E0B',
        icon: 'folder',
      },
      {
        label: 'Free',
        value: storageInfo.availableSpace,
        color: 'rgba(255, 255, 255, 0.2)',
        icon: 'checkmark-circle',
      },
    ];
  };

  const segments = getStorageSegments();
  const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;

  const renderProgressBar = () => {
    const chartWidth = width - 80;
    let currentPosition = 0;

    return (
      <View style={styles.progressBarContainer}>
        <View style={[styles.progressBar, { width: chartWidth }]}>
          {segments.map((segment, index) => {
            const segmentWidth = (segment.value / storageInfo.totalSpace) * chartWidth;
            const segmentStyle = {
              width: segmentWidth,
              backgroundColor: segment.color,
              marginLeft: index === 0 ? 0 : 0,
            };

            currentPosition += segmentWidth;

            if (segmentWidth < 2) return null; // Don't render very small segments

            return (
              <View
                key={segment.label}
                style={[styles.progressSegment, segmentStyle]}
              />
            );
          })}
        </View>
        
        <View style={styles.progressLabels}>
          <Text style={styles.progressLabel}>0</Text>
          <Text style={styles.progressLabel}>
            {formatBytes(storageInfo.totalSpace)}
          </Text>
        </View>
      </View>
    );
  };

  const renderLegend = () => {
    return (
      <View style={styles.legend}>
        {segments.map((segment) => {
          if (segment.value === 0) return null;
          
          const percentage = (segment.value / storageInfo.totalSpace) * 100;
          
          return (
            <View key={segment.label} style={styles.legendItem}>
              <View style={styles.legendItemLeft}>
                <View style={[styles.legendColor, { backgroundColor: segment.color }]} />
                <Ionicons 
                  name={segment.icon} 
                  size={16} 
                  color="rgba(255, 255, 255, 0.8)" 
                  style={styles.legendIcon}
                />
                <Text style={styles.legendLabel}>{segment.label}</Text>
              </View>
              <View style={styles.legendItemRight}>
                <Text style={styles.legendValue}>{formatBytes(segment.value)}</Text>
                <Text style={styles.legendPercentage}>
                  {percentage < 0.1 ? '<0.1' : percentage.toFixed(1)}%
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderActions = () => {
    return (
      <View style={styles.actions}>
        {onClearCache && (
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={onClearCache}
            activeOpacity={0.7}
          >
            <Ionicons name="trash" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Clear Cache</Text>
            <Text style={styles.actionButtonSubtext}>
              {formatBytes(storageInfo.cacheSize)}
            </Text>
          </TouchableOpacity>
        )}
        
        {onOptimize && (
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={onOptimize}
            activeOpacity={0.7}
          >
            <Ionicons name="speedometer" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Optimize</Text>
            <Text style={styles.actionButtonSubtext}>Free up space</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Storage Usage</Text>
          <Text style={styles.subtitle}>
            {formatBytes(storageInfo.usedSpace)} of {formatBytes(storageInfo.totalSpace)} used
          </Text>
        </View>
        <View style={styles.headerRight}>
          <Text style={styles.usagePercentage}>{usagePercentage.toFixed(1)}%</Text>
          <Text style={styles.usageLabel}>Used</Text>
        </View>
      </View>

      {/* Progress Bar */}
      {renderProgressBar()}

      {/* Legend */}
      {renderLegend()}

      {/* Actions */}
      {renderActions()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  usagePercentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#00D4FF',
  },
  usageLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 2,
  },
  progressBarContainer: {
    marginBottom: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  progressSegment: {
    height: '100%',
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  progressLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  legend: {
    marginBottom: 20,
  },
  legendItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  legendItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  legendItemRight: {
    alignItems: 'flex-end',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendIcon: {
    marginRight: 8,
  },
  legendLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    flex: 1,
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  legendPercentage: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 2,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 4,
  },
  primaryButton: {
    backgroundColor: '#00D4FF',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  actionButtonSubtext: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
});
