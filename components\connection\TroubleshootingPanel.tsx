import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import LoadingSpinner from '../ui/LoadingSpinner';
import StatusIndicator from '../ui/StatusIndicator';
import Card from '../ui/Card';
import Button from '../ui/Button';
import TroubleshootingService, { 
  TroubleshootingReport, 
  TroubleshootingResult 
} from '../../services/networking/TroubleshootingService';

interface TroubleshootingPanelProps {
  onClose?: () => void;
  deviceId?: string;
}

export default function TroubleshootingPanel({
  onClose,
  deviceId,
}: TroubleshootingPanelProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [report, setReport] = useState<TroubleshootingReport | null>(null);
  const [networkDiagnostics, setNetworkDiagnostics] = useState<any>(null);

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    setIsRunning(true);
    try {
      const [diagnosticsReport, networkInfo] = await Promise.all([
        TroubleshootingService.runDiagnostics(),
        TroubleshootingService.getNetworkDiagnostics(),
      ]);
      
      setReport(diagnosticsReport);
      setNetworkDiagnostics(networkInfo);
    } catch (error) {
      Alert.alert('Error', 'Failed to run diagnostics');
    } finally {
      setIsRunning(false);
    }
  };

  const testSpecificConnection = async () => {
    if (!deviceId) return;
    
    setIsRunning(true);
    try {
      const result = await TroubleshootingService.testSpecificConnection(deviceId);
      Alert.alert(
        'Connection Test',
        result.message,
        [
          {
            text: 'OK',
            onPress: () => {
              if (result.status === 'fail' && result.suggestion) {
                Alert.alert('Suggestion', result.suggestion);
              }
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to test connection');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return 'checkmark-circle';
      case 'warning':
        return 'warning';
      case 'fail':
        return 'close-circle';
      default:
        return 'information-circle';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return '#4CAF50';
      case 'warning':
        return '#FF9800';
      case 'fail':
        return '#F44336';
      default:
        return '#2196F3';
    }
  };

  const getOverallStatusInfo = () => {
    if (!report) return { color: '#666', text: 'Unknown', icon: 'help-circle' };
    
    switch (report.overallStatus) {
      case 'healthy':
        return { color: '#4CAF50', text: 'Healthy', icon: 'checkmark-circle' };
      case 'issues':
        return { color: '#FF9800', text: 'Minor Issues', icon: 'warning' };
      case 'critical':
        return { color: '#F44336', text: 'Critical Issues', icon: 'alert-circle' };
      default:
        return { color: '#666', text: 'Unknown', icon: 'help-circle' };
    }
  };

  const renderTestResult = (result: TroubleshootingResult, index: number) => (
    <Card key={index} style={styles.testCard}>
      <View style={styles.testHeader}>
        <View style={styles.testTitleRow}>
          <Ionicons
            name={getStatusIcon(result.status) as any}
            size={20}
            color={getStatusColor(result.status)}
          />
          <Text style={styles.testTitle}>{result.test}</Text>
        </View>
        <StatusIndicator
          status={result.status as any}
          size="small"
          variant="badge"
        />
      </View>
      
      <Text style={styles.testMessage}>{result.message}</Text>
      
      {result.details && (
        <Text style={styles.testDetails}>{result.details}</Text>
      )}
      
      {result.suggestion && (
        <View style={styles.suggestionContainer}>
          <Ionicons name="bulb" size={16} color="#FFD700" />
          <Text style={styles.suggestionText}>{result.suggestion}</Text>
        </View>
      )}
    </Card>
  );

  const renderNetworkInfo = () => {
    if (!networkDiagnostics) return null;

    return (
      <Card style={styles.networkCard}>
        <Text style={styles.sectionTitle}>Network Information</Text>
        <View style={styles.networkGrid}>
          <View style={styles.networkItem}>
            <Ionicons name="globe" size={20} color="#00D4FF" />
            <Text style={styles.networkLabel}>Local IP</Text>
            <Text style={styles.networkValue}>{networkDiagnostics.localIP}</Text>
          </View>
          
          <View style={styles.networkItem}>
            <Ionicons name="wifi" size={20} color="#00D4FF" />
            <Text style={styles.networkLabel}>Type</Text>
            <Text style={styles.networkValue}>{networkDiagnostics.networkType}</Text>
          </View>
          
          <View style={styles.networkItem}>
            <Ionicons name="cellular" size={20} color="#00D4FF" />
            <Text style={styles.networkLabel}>Signal</Text>
            <Text style={styles.networkValue}>{networkDiagnostics.signalStrength}%</Text>
          </View>
          
          <View style={styles.networkItem}>
            <Ionicons name="speedometer" size={20} color="#00D4FF" />
            <Text style={styles.networkLabel}>Latency</Text>
            <Text style={styles.networkValue}>{networkDiagnostics.latency}ms</Text>
          </View>
        </View>
      </Card>
    );
  };

  const overallStatus = getOverallStatusInfo();

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />

      <BlurView intensity={15} style={styles.panel}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.panelGradient}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Ionicons name="medical" size={24} color="#00D4FF" />
              <Text style={styles.headerTitle}>Network Diagnostics</Text>
            </View>
            {onClose && (
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Ionicons name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>

          {/* Overall Status */}
          <Card style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Ionicons
                name={overallStatus.icon as any}
                size={32}
                color={overallStatus.color}
              />
              <View style={styles.statusInfo}>
                <Text style={styles.statusTitle}>System Status</Text>
                <Text style={[styles.statusText, { color: overallStatus.color }]}>
                  {overallStatus.text}
                </Text>
              </View>
            </View>
            {report && (
              <Text style={styles.statusTimestamp}>
                Last checked: {new Date(report.timestamp).toLocaleTimeString()}
              </Text>
            )}
          </Card>

          {/* Loading or Results */}
          {isRunning ? (
            <View style={styles.loadingContainer}>
              <LoadingSpinner size="large" variant="gradient" />
              <Text style={styles.loadingText}>Running diagnostics...</Text>
            </View>
          ) : (
            <ScrollView style={styles.resultsContainer} showsVerticalScrollIndicator={false}>
              {/* Network Information */}
              {renderNetworkInfo()}

              {/* Test Results */}
              {report && (
                <View style={styles.testsSection}>
                  <Text style={styles.sectionTitle}>Diagnostic Tests</Text>
                  {report.results.map(renderTestResult)}
                </View>
              )}

              {/* Recommendations */}
              {report && report.recommendations.length > 0 && (
                <Card style={styles.recommendationsCard}>
                  <Text style={styles.sectionTitle}>Recommendations</Text>
                  {report.recommendations.map((recommendation, index) => (
                    <View key={index} style={styles.recommendationItem}>
                      <Ionicons name="arrow-forward" size={16} color="#00D4FF" />
                      <Text style={styles.recommendationText}>{recommendation}</Text>
                    </View>
                  ))}
                </Card>
              )}
            </ScrollView>
          )}

          {/* Actions */}
          <View style={styles.actions}>
            <Button
              title="Run Diagnostics"
              onPress={runDiagnostics}
              loading={isRunning}
              variant="secondary"
              size="medium"
              style={styles.actionButton}
            />
            
            {deviceId && (
              <Button
                title="Test Connection"
                onPress={testSpecificConnection}
                loading={isRunning}
                variant="primary"
                size="medium"
                style={styles.actionButton}
              />
            )}
          </View>
        </LinearGradient>
      </BlurView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  panel: {
    flex: 1,
    margin: 20,
    borderRadius: 24,
    overflow: 'hidden',
  },
  panelGradient: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusCard: {
    marginBottom: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 8,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statusTimestamp: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  resultsContainer: {
    flex: 1,
  },
  networkCard: {
    marginBottom: 20,
  },
  networkGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  networkItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    gap: 4,
  },
  networkLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  networkValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  testsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  testCard: {
    marginBottom: 12,
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  testTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  testTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  testMessage: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4,
  },
  testDetails: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 8,
  },
  suggestionContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    padding: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#FFD700',
  },
  suggestionText: {
    fontSize: 12,
    color: '#FFD700',
    flex: 1,
    lineHeight: 16,
  },
  recommendationsCard: {
    marginBottom: 20,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    flex: 1,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
  },
});
