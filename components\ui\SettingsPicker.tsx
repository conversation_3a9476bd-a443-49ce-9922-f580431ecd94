import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import Modal from './Modal';

const { width } = Dimensions.get('window');

export interface PickerOption {
  label: string;
  value: string;
  description?: string;
  icon?: keyof typeof Ionicons.glyphMap;
}

interface SettingsPickerProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  subtitle?: string;
  options: PickerOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  multiSelect?: boolean;
  selectedValues?: string[];
  onMultiSelect?: (values: string[]) => void;
}

export default function SettingsPicker({
  visible,
  onClose,
  title,
  subtitle,
  options,
  selectedValue,
  onSelect,
  multiSelect = false,
  selectedValues = [],
  onMultiSelect,
}: SettingsPickerProps) {
  const [localSelectedValues, setLocalSelectedValues] = useState<string[]>(
    multiSelect ? selectedValues : [selectedValue]
  );

  const handleOptionPress = (value: string) => {
    if (multiSelect) {
      const newValues = localSelectedValues.includes(value)
        ? localSelectedValues.filter(v => v !== value)
        : [...localSelectedValues, value];
      
      setLocalSelectedValues(newValues);
      onMultiSelect?.(newValues);
    } else {
      onSelect(value);
      onClose();
    }
  };

  const isSelected = (value: string) => {
    return multiSelect 
      ? localSelectedValues.includes(value)
      : selectedValue === value;
  };

  const renderOption = (option: PickerOption) => {
    const selected = isSelected(option.value);
    
    return (
      <TouchableOpacity
        key={option.value}
        style={[styles.option, selected && styles.selectedOption]}
        onPress={() => handleOptionPress(option.value)}
        activeOpacity={0.7}
      >
        <View style={styles.optionContent}>
          {option.icon && (
            <View style={[styles.optionIcon, selected && styles.selectedOptionIcon]}>
              <Ionicons 
                name={option.icon} 
                size={20} 
                color={selected ? '#FFFFFF' : '#00D4FF'} 
              />
            </View>
          )}
          
          <View style={styles.optionText}>
            <Text style={[styles.optionLabel, selected && styles.selectedOptionLabel]}>
              {option.label}
            </Text>
            {option.description && (
              <Text style={[styles.optionDescription, selected && styles.selectedOptionDescription]}>
                {option.description}
              </Text>
            )}
          </View>
          
          <View style={styles.optionIndicator}>
            {multiSelect ? (
              <View style={[styles.checkbox, selected && styles.checkedCheckbox]}>
                {selected && (
                  <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                )}
              </View>
            ) : (
              selected && <Ionicons name="checkmark" size={20} color="#00D4FF" />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title={title}
      subtitle={subtitle}
      variant="bottom"
    >
      <View style={styles.container}>
        <ScrollView 
          style={styles.optionsList}
          showsVerticalScrollIndicator={false}
        >
          {options.map(renderOption)}
        </ScrollView>
        
        {multiSelect && (
          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onClose}
              activeOpacity={0.7}
            >
              <Text style={styles.actionButtonText}>Done</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    maxHeight: 400,
  },
  optionsList: {
    maxHeight: 320,
  },
  option: {
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
  },
  selectedOption: {
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  optionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedOptionIcon: {
    backgroundColor: '#00D4FF',
  },
  optionText: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  selectedOptionLabel: {
    color: '#FFFFFF',
  },
  optionDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  selectedOptionDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  optionIndicator: {
    marginLeft: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#00D4FF',
    borderColor: '#00D4FF',
  },
  actions: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  actionButton: {
    backgroundColor: '#00D4FF',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

// Preset configurations for common settings
export const ConnectionTimeoutOptions: PickerOption[] = [
  { label: '15 seconds', value: '15', description: 'Quick timeout for fast connections' },
  { label: '30 seconds', value: '30', description: 'Recommended for most situations' },
  { label: '60 seconds', value: '60', description: 'Longer timeout for slow networks' },
  { label: '2 minutes', value: '120', description: 'Maximum timeout for poor connections' },
];

export const ThemeColorOptions: PickerOption[] = [
  { label: 'Cyan Blue', value: '#00D4FF', icon: 'water' },
  { label: 'Electric Purple', value: '#8B5CF6', icon: 'flash' },
  { label: 'Emerald Green', value: '#10B981', icon: 'leaf' },
  { label: 'Sunset Orange', value: '#F59E0B', icon: 'sunny' },
  { label: 'Rose Pink', value: '#EC4899', icon: 'rose' },
  { label: 'Steel Blue', value: '#3B82F6', icon: 'hardware-chip' },
];

export const FontSizeOptions: PickerOption[] = [
  { label: 'Small', value: 'small', description: 'Compact text for more content' },
  { label: 'Medium', value: 'medium', description: 'Standard size for most users' },
  { label: 'Large', value: 'large', description: 'Easier to read text' },
];

export const DeviceVisibilityOptions: PickerOption[] = [
  { 
    label: 'Everyone', 
    value: 'everyone', 
    description: 'Your device is visible to all nearby players',
    icon: 'globe'
  },
  { 
    label: 'Contacts Only', 
    value: 'contacts', 
    description: 'Only visible to players you\'ve connected with before',
    icon: 'people'
  },
  { 
    label: 'Nobody', 
    value: 'nobody', 
    description: 'Your device is hidden from all players',
    icon: 'eye-off'
  },
];

export const LogLevelOptions: PickerOption[] = [
  { label: 'Error', value: 'error', description: 'Only log errors' },
  { label: 'Warning', value: 'warn', description: 'Log errors and warnings' },
  { label: 'Info', value: 'info', description: 'Log general information' },
  { label: 'Debug', value: 'debug', description: 'Log everything (verbose)' },
];
