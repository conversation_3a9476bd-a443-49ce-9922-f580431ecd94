import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GameInfo } from '../../services/game/GameDetector';
import GameCard from './GameCard';
import LoadingSpinner from '../ui/LoadingSpinner';
import Input from '../ui/Input';
import Button from '../ui/Button';

interface GameListProps {
  games: GameInfo[];
  loading?: boolean;
  onRefresh?: () => Promise<void>;
  onGamePress?: (game: GameInfo) => void;
  onGameLongPress?: (game: GameInfo) => void;
  style?: ViewStyle;
  variant?: 'default' | 'compact' | 'featured';
  showSearch?: boolean;
  showFilter?: boolean;
  showCompatibility?: boolean;
  emptyMessage?: string;
  emptyIcon?: keyof typeof Ionicons.glyphMap;
}

type FilterType = 'all' | 'multiplayer' | 'local' | 'recent' | 'category';
type SortType = 'name' | 'rating' | 'playtime' | 'lastPlayed';

export default function GameList({
  games,
  loading = false,
  onRefresh,
  onGamePress,
  onGameLongPress,
  style,
  variant = 'default',
  showSearch = true,
  showFilter = true,
  showCompatibility = false,
  emptyMessage = 'No games found',
  emptyIcon = 'game-controller',
}: GameListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [selectedSort, setSelectedSort] = useState<SortType>('name');
  const [filteredGames, setFilteredGames] = useState<GameInfo[]>(games);
  const [refreshing, setRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    filterAndSortGames();
  }, [games, searchQuery, selectedFilter, selectedSort]);

  const filterAndSortGames = () => {
    let filtered = [...games];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(game =>
        game.name.toLowerCase().includes(query) ||
        game.category.toLowerCase().includes(query) ||
        game.developer.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    switch (selectedFilter) {
      case 'multiplayer':
        filtered = filtered.filter(game => game.supportsMultiplayer);
        break;
      case 'local':
        filtered = filtered.filter(game => 
          game.supportsMultiplayer && 
          (game.multiplayerType === 'local' || game.multiplayerType === 'both')
        );
        break;
      case 'recent':
        filtered = filtered.filter(game => game.lastPlayed);
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (selectedSort) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'playtime':
          return (b.playtime || 0) - (a.playtime || 0);
        case 'lastPlayed':
          return (b.lastPlayed || 0) - (a.lastPlayed || 0);
        default:
          return 0;
      }
    });

    setFilteredGames(filtered);
  };

  const handleRefresh = async () => {
    if (!onRefresh) return;
    
    setRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const getFilterOptions = (): { key: FilterType; label: string; icon: keyof typeof Ionicons.glyphMap }[] => [
    { key: 'all', label: 'All Games', icon: 'apps' },
    { key: 'multiplayer', label: 'Multiplayer', icon: 'people' },
    { key: 'local', label: 'Local Play', icon: 'bluetooth' },
    { key: 'recent', label: 'Recent', icon: 'time' },
  ];

  const getSortOptions = (): { key: SortType; label: string; icon: keyof typeof Ionicons.glyphMap }[] => [
    { key: 'name', label: 'Name', icon: 'text' },
    { key: 'rating', label: 'Rating', icon: 'star' },
    { key: 'playtime', label: 'Playtime', icon: 'time' },
    { key: 'lastPlayed', label: 'Last Played', icon: 'calendar' },
  ];

  const renderFilterButton = (option: { key: FilterType; label: string; icon: keyof typeof Ionicons.glyphMap }) => (
    <TouchableOpacity
      key={option.key}
      style={[
        styles.filterButton,
        selectedFilter === option.key && styles.filterButtonActive,
      ]}
      onPress={() => setSelectedFilter(option.key)}
    >
      <Ionicons
        name={option.icon}
        size={16}
        color={selectedFilter === option.key ? '#1a1a2e' : '#00D4FF'}
      />
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === option.key && styles.filterButtonTextActive,
        ]}
      >
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const renderSortButton = (option: { key: SortType; label: string; icon: keyof typeof Ionicons.glyphMap }) => (
    <TouchableOpacity
      key={option.key}
      style={[
        styles.sortButton,
        selectedSort === option.key && styles.sortButtonActive,
      ]}
      onPress={() => setSelectedSort(option.key)}
    >
      <Ionicons
        name={option.icon}
        size={14}
        color={selectedSort === option.key ? '#1a1a2e' : '#00D4FF'}
      />
      <Text
        style={[
          styles.sortButtonText,
          selectedSort === option.key && styles.sortButtonTextActive,
        ]}
      >
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {showSearch && (
        <Input
          placeholder="Search games..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search"
          style={styles.searchInput}
        />
      )}

      {showFilter && (
        <View style={styles.filterSection}>
          <View style={styles.filterHeader}>
            <Text style={styles.filterTitle}>Filters & Sort</Text>
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Ionicons
                name={showFilters ? 'chevron-up' : 'chevron-down'}
                size={20}
                color="#00D4FF"
              />
            </TouchableOpacity>
          </View>

          {showFilters && (
            <View style={styles.filterContent}>
              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>Filter:</Text>
                <View style={styles.filterButtons}>
                  {getFilterOptions().map(renderFilterButton)}
                </View>
              </View>

              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>Sort:</Text>
                <View style={styles.sortButtons}>
                  {getSortOptions().map(renderSortButton)}
                </View>
              </View>
            </View>
          )}
        </View>
      )}

      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {filteredGames.length} game{filteredGames.length !== 1 ? 's' : ''}
        </Text>
        {onRefresh && (
          <Button
            title="Refresh"
            onPress={handleRefresh}
            variant="ghost"
            size="small"
            icon={<Ionicons name="refresh" size={16} color="#00D4FF" />}
          />
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name={emptyIcon} size={64} color="rgba(255, 255, 255, 0.3)" />
      <Text style={styles.emptyMessage}>{emptyMessage}</Text>
      {searchQuery && (
        <Text style={styles.emptySubMessage}>
          Try adjusting your search or filters
        </Text>
      )}
    </View>
  );

  const renderGameItem = ({ item }: { item: GameInfo }) => (
    <GameCard
      game={item}
      variant={variant}
      onPress={() => onGamePress?.(item)}
      onLongPress={() => onGameLongPress?.(item)}
      showCompatibility={showCompatibility}
      showLastPlayed={selectedSort === 'lastPlayed'}
      showPlaytime={selectedSort === 'playtime'}
    />
  );

  if (loading && filteredGames.length === 0) {
    return (
      <View style={[styles.container, styles.loadingContainer, style]}>
        <LoadingSpinner size="large" variant="gradient" />
        <Text style={styles.loadingText}>Loading games...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <FlatList
        data={filteredGames}
        renderItem={renderGameItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor="#00D4FF"
              colors={['#00D4FF']}
            />
          ) : undefined
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  listContent: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    marginBottom: 16,
  },
  searchInput: {
    marginBottom: 16,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  toggleButton: {
    padding: 4,
  },
  filterContent: {
    gap: 12,
  },
  filterRow: {
    gap: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(0, 212, 255, 0.3)',
  },
  filterButtonActive: {
    backgroundColor: '#00D4FF',
    borderColor: '#00D4FF',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#00D4FF',
  },
  filterButtonTextActive: {
    color: '#1a1a2e',
  },
  sortButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(0, 212, 255, 0.3)',
  },
  sortButtonActive: {
    backgroundColor: '#00D4FF',
    borderColor: '#00D4FF',
  },
  sortButtonText: {
    fontSize: 11,
    fontWeight: '500',
    color: '#00D4FF',
  },
  sortButtonTextActive: {
    color: '#1a1a2e',
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultsCount: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
    gap: 16,
  },
  emptyMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
  },
  emptySubMessage: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.4)',
    textAlign: 'center',
  },
});
