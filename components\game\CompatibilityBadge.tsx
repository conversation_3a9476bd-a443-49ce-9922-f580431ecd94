import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { CompatibilityCheck } from '../../services/game/GameCompatibility';

interface CompatibilityBadgeProps {
  compatibility: CompatibilityCheck;
  onPress?: () => void;
  style?: ViewStyle;
  variant?: 'badge' | 'card' | 'minimal';
  showDetails?: boolean;
}

export default function CompatibilityBadge({
  compatibility,
  onPress,
  style,
  variant = 'badge',
  showDetails = false,
}: CompatibilityBadgeProps) {
  const getCompatibilityLevel = () => {
    if (compatibility.score >= 80) {
      return {
        level: 'excellent',
        color: '#4CAF50',
        icon: 'checkmark-circle',
        text: 'Excellent',
        description: 'Fully compatible',
      };
    } else if (compatibility.score >= 60) {
      return {
        level: 'good',
        color: '#8BC34A',
        icon: 'checkmark',
        text: 'Good',
        description: 'Minor issues',
      };
    } else if (compatibility.score >= 40) {
      return {
        level: 'fair',
        color: '#FF9800',
        icon: 'warning',
        text: 'Fair',
        description: 'Some limitations',
      };
    } else if (compatibility.score >= 20) {
      return {
        level: 'poor',
        color: '#FF5722',
        icon: 'alert-circle',
        text: 'Poor',
        description: 'Major issues',
      };
    } else {
      return {
        level: 'incompatible',
        color: '#F44336',
        icon: 'close-circle',
        text: 'Incompatible',
        description: 'Cannot connect',
      };
    }
  };

  const getIssuesByType = () => {
    const issueTypes = {
      errors: compatibility.issues.filter(issue => issue.type === 'error'),
      warnings: compatibility.issues.filter(issue => issue.type === 'warning'),
      info: compatibility.issues.filter(issue => issue.type === 'info'),
    };
    return issueTypes;
  };

  const compatLevel = getCompatibilityLevel();
  const issueTypes = getIssuesByType();

  const renderMinimalBadge = () => (
    <TouchableOpacity
      style={[styles.minimalBadge, style]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={0.8}
    >
      <View
        style={[
          styles.minimalDot,
          { backgroundColor: compatLevel.color },
        ]}
      />
      <Text style={[styles.minimalText, { color: compatLevel.color }]}>
        {compatLevel.text}
      </Text>
    </TouchableOpacity>
  );

  const renderBadge = () => (
    <TouchableOpacity
      style={[styles.badge, style]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={15} style={styles.badgeBlur}>
        <View
          style={[
            styles.badgeContent,
            { backgroundColor: `${compatLevel.color}20` },
          ]}
        >
          <Ionicons
            name={compatLevel.icon as any}
            size={16}
            color={compatLevel.color}
          />
          <Text style={[styles.badgeText, { color: compatLevel.color }]}>
            {compatLevel.text}
          </Text>
          <Text style={styles.scoreText}>{compatibility.score}%</Text>
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  const renderCard = () => (
    <TouchableOpacity
      style={[styles.card, style]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={15} style={styles.cardBlur}>
        <View style={styles.cardContent}>
          {/* Header */}
          <View style={styles.cardHeader}>
            <View style={styles.headerLeft}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: `${compatLevel.color}20` },
                ]}
              >
                <Ionicons
                  name={compatLevel.icon as any}
                  size={24}
                  color={compatLevel.color}
                />
              </View>
              <View style={styles.headerInfo}>
                <Text style={styles.cardTitle}>Compatibility</Text>
                <Text style={[styles.cardLevel, { color: compatLevel.color }]}>
                  {compatLevel.text}
                </Text>
              </View>
            </View>
            
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreNumber}>{compatibility.score}</Text>
              <Text style={styles.scoreLabel}>/ 100</Text>
            </View>
          </View>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <View style={styles.progressTrack}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${compatibility.score}%`,
                    backgroundColor: compatLevel.color,
                  },
                ]}
              />
            </View>
            <Text style={styles.progressText}>{compatLevel.description}</Text>
          </View>

          {/* Issues Summary */}
          {showDetails && compatibility.issues.length > 0 && (
            <View style={styles.issuesContainer}>
              <Text style={styles.issuesTitle}>Issues Found:</Text>
              <View style={styles.issuesSummary}>
                {issueTypes.errors.length > 0 && (
                  <View style={styles.issueType}>
                    <Ionicons name="close-circle" size={14} color="#F44336" />
                    <Text style={styles.issueCount}>
                      {issueTypes.errors.length} error{issueTypes.errors.length !== 1 ? 's' : ''}
                    </Text>
                  </View>
                )}
                
                {issueTypes.warnings.length > 0 && (
                  <View style={styles.issueType}>
                    <Ionicons name="warning" size={14} color="#FF9800" />
                    <Text style={styles.issueCount}>
                      {issueTypes.warnings.length} warning{issueTypes.warnings.length !== 1 ? 's' : ''}
                    </Text>
                  </View>
                )}
                
                {issueTypes.info.length > 0 && (
                  <View style={styles.issueType}>
                    <Ionicons name="information-circle" size={14} color="#2196F3" />
                    <Text style={styles.issueCount}>
                      {issueTypes.info.length} info
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Requirements */}
          {showDetails && (
            <View style={styles.requirementsContainer}>
              <Text style={styles.requirementsTitle}>Requirements:</Text>
              <View style={styles.requirementsList}>
                <View style={styles.requirement}>
                  <Ionicons name="people" size={12} color="#00D4FF" />
                  <Text style={styles.requirementText}>
                    {compatibility.requirements.minPlayers}-{compatibility.requirements.maxPlayers} players
                  </Text>
                </View>
                
                <View style={styles.requirement}>
                  <Ionicons name="wifi" size={12} color="#00D4FF" />
                  <Text style={styles.requirementText}>
                    {compatibility.requirements.networkTypes.join(', ')}
                  </Text>
                </View>
                
                <View style={styles.requirement}>
                  <Ionicons name="phone-portrait" size={12} color="#00D4FF" />
                  <Text style={styles.requirementText}>
                    {compatibility.requirements.platformSupport.join(', ')}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Recommendations */}
          {showDetails && compatibility.recommendations.length > 0 && (
            <View style={styles.recommendationsContainer}>
              <Text style={styles.recommendationsTitle}>Recommendations:</Text>
              {compatibility.recommendations.slice(0, 2).map((recommendation, index) => (
                <View key={index} style={styles.recommendation}>
                  <Ionicons name="bulb" size={12} color="#FFD700" />
                  <Text style={styles.recommendationText} numberOfLines={2}>
                    {recommendation}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  switch (variant) {
    case 'minimal':
      return renderMinimalBadge();
    case 'card':
      return renderCard();
    default:
      return renderBadge();
  }
}

const styles = StyleSheet.create({
  // Minimal variant
  minimalBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  minimalDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  minimalText: {
    fontSize: 12,
    fontWeight: '500',
  },

  // Badge variant
  badge: {
    alignSelf: 'flex-start',
  },
  badgeBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  badgeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  scoreText: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
  },

  // Card variant
  card: {
    width: '100%',
  },
  cardBlur: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  cardContent: {
    padding: 16,
    backgroundColor: 'rgba(0, 212, 255, 0.05)',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerInfo: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  cardLevel: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  scoreContainer: {
    alignItems: 'flex-end',
  },
  scoreNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  scoreLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressTrack: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  issuesContainer: {
    marginBottom: 12,
  },
  issuesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  issuesSummary: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  issueType: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  issueCount: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  requirementsContainer: {
    marginBottom: 12,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  requirementsList: {
    gap: 6,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  requirementText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    flex: 1,
  },
  recommendationsContainer: {
    marginTop: 4,
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  recommendation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    marginBottom: 6,
  },
  recommendationText: {
    fontSize: 12,
    color: '#FFD700',
    flex: 1,
    lineHeight: 16,
  },
});
