import { Platform } from 'react-native';
import * as SQLite from 'expo-sqlite';

export interface DatabaseConfig {
  name: string;
  version: string;
  displayName: string;
  estimatedSize: number;
}

export interface GameCompatibilityRecord {
  id: number;
  gameId: string;
  gameName: string;
  version: string;
  platform: string;
  isCompatible: boolean;
  protocolSupport: string[];
  lastTested: number;
  notes?: string;
}

export interface ConnectionProfileRecord {
  id: number;
  profileName: string;
  gameId: string;
  connectionMethod: string;
  configuration: string; // JSON string
  isDefault: boolean;
  createdAt: number;
  lastUsed: number;
}

export interface SessionHistoryRecord {
  id: number;
  sessionId: string;
  gameId: string;
  hostDevice: string;
  participants: string; // JSON string
  startTime: number;
  endTime?: number;
  duration?: number;
  status: 'completed' | 'failed' | 'aborted';
  notes?: string;
}

export interface UserPreferenceRecord {
  id: number;
  key: string;
  value: string;
  category: string;
  lastModified: number;
}

class SQLiteManager {
  private static instance: SQLiteManager;
  private database: SQLite.WebSQLDatabase | null = null;
  private isInitialized: boolean = false;
  private config: DatabaseConfig;

  private constructor() {
    this.config = {
      name: 'LoGaCo.db',
      version: '1.0',
      displayName: 'LoGaCo Database',
      estimatedSize: 5 * 1024 * 1024 // 5MB
    };
  }

  static getInstance(): SQLiteManager {
    if (!SQLiteManager.instance) {
      SQLiteManager.instance = new SQLiteManager();
    }
    return SQLiteManager.instance;
  }

  // Database Initialization
  async initialize(): Promise<void> {
    try {
      console.log('Initializing SQLite Database...');

      // Open database
      this.database = SQLite.openDatabase(
        this.config.name,
        this.config.version,
        this.config.displayName,
        this.config.estimatedSize
      );

      // Create tables
      await this.createTables();

      // Insert default data
      await this.insertDefaultData();

      this.isInitialized = true;
      console.log('SQLite Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SQLite Database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.database) {
      throw new Error('Database not opened');
    }

    const tables = [
      this.createGameCompatibilityTable(),
      this.createConnectionProfilesTable(),
      this.createSessionHistoryTable(),
      this.createUserPreferencesTable()
    ];

    for (const tableSQL of tables) {
      await this.executeSQL(tableSQL);
    }

    console.log('Database tables created successfully');
  }

  private createGameCompatibilityTable(): string {
    return `
      CREATE TABLE IF NOT EXISTS game_compatibility (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        gameId TEXT NOT NULL UNIQUE,
        gameName TEXT NOT NULL,
        version TEXT NOT NULL,
        platform TEXT NOT NULL,
        isCompatible INTEGER NOT NULL DEFAULT 0,
        protocolSupport TEXT NOT NULL DEFAULT '[]',
        lastTested INTEGER NOT NULL,
        notes TEXT,
        createdAt INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updatedAt INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    `;
  }

  private createConnectionProfilesTable(): string {
    return `
      CREATE TABLE IF NOT EXISTS connection_profiles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        profileName TEXT NOT NULL,
        gameId TEXT NOT NULL,
        connectionMethod TEXT NOT NULL,
        configuration TEXT NOT NULL DEFAULT '{}',
        isDefault INTEGER NOT NULL DEFAULT 0,
        createdAt INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        lastUsed INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        UNIQUE(profileName, gameId)
      )
    `;
  }

  private createSessionHistoryTable(): string {
    return `
      CREATE TABLE IF NOT EXISTS session_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sessionId TEXT NOT NULL UNIQUE,
        gameId TEXT NOT NULL,
        hostDevice TEXT NOT NULL,
        participants TEXT NOT NULL DEFAULT '[]',
        startTime INTEGER NOT NULL,
        endTime INTEGER,
        duration INTEGER,
        status TEXT NOT NULL DEFAULT 'active',
        notes TEXT,
        createdAt INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    `;
  }

  private createUserPreferencesTable(): string {
    return `
      CREATE TABLE IF NOT EXISTS user_preferences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        category TEXT NOT NULL DEFAULT 'general',
        lastModified INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    `;
  }

  // Game Compatibility Management
  async addGameCompatibility(record: Omit<GameCompatibilityRecord, 'id'>): Promise<number> {
    const sql = `
      INSERT OR REPLACE INTO game_compatibility 
      (gameId, gameName, version, platform, isCompatible, protocolSupport, lastTested, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      record.gameId,
      record.gameName,
      record.version,
      record.platform,
      record.isCompatible ? 1 : 0,
      JSON.stringify(record.protocolSupport),
      record.lastTested,
      record.notes || null
    ];

    const result = await this.executeSQL(sql, params);
    console.log(`Game compatibility record added: ${record.gameId}`);
    return result.insertId;
  }

  async getGameCompatibility(gameId: string): Promise<GameCompatibilityRecord | null> {
    const sql = 'SELECT * FROM game_compatibility WHERE gameId = ?';
    const result = await this.executeSQL(sql, [gameId]);
    
    if (result.rows.length > 0) {
      const row = result.rows.item(0);
      return {
        ...row,
        isCompatible: row.isCompatible === 1,
        protocolSupport: JSON.parse(row.protocolSupport)
      };
    }
    
    return null;
  }

  async getAllGameCompatibility(): Promise<GameCompatibilityRecord[]> {
    const sql = 'SELECT * FROM game_compatibility ORDER BY gameName';
    const result = await this.executeSQL(sql);
    
    const records: GameCompatibilityRecord[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      records.push({
        ...row,
        isCompatible: row.isCompatible === 1,
        protocolSupport: JSON.parse(row.protocolSupport)
      });
    }
    
    return records;
  }

  async updateGameCompatibility(gameId: string, updates: Partial<GameCompatibilityRecord>): Promise<void> {
    const fields = [];
    const params = [];

    if (updates.gameName !== undefined) {
      fields.push('gameName = ?');
      params.push(updates.gameName);
    }
    if (updates.version !== undefined) {
      fields.push('version = ?');
      params.push(updates.version);
    }
    if (updates.isCompatible !== undefined) {
      fields.push('isCompatible = ?');
      params.push(updates.isCompatible ? 1 : 0);
    }
    if (updates.protocolSupport !== undefined) {
      fields.push('protocolSupport = ?');
      params.push(JSON.stringify(updates.protocolSupport));
    }
    if (updates.lastTested !== undefined) {
      fields.push('lastTested = ?');
      params.push(updates.lastTested);
    }
    if (updates.notes !== undefined) {
      fields.push('notes = ?');
      params.push(updates.notes);
    }

    if (fields.length > 0) {
      fields.push('updatedAt = ?');
      params.push(Date.now());
      params.push(gameId);

      const sql = `UPDATE game_compatibility SET ${fields.join(', ')} WHERE gameId = ?`;
      await this.executeSQL(sql, params);
      console.log(`Game compatibility updated: ${gameId}`);
    }
  }

  // Connection Profiles Management
  async addConnectionProfile(record: Omit<ConnectionProfileRecord, 'id'>): Promise<number> {
    const sql = `
      INSERT OR REPLACE INTO connection_profiles 
      (profileName, gameId, connectionMethod, configuration, isDefault, lastUsed)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const params = [
      record.profileName,
      record.gameId,
      record.connectionMethod,
      record.configuration,
      record.isDefault ? 1 : 0,
      record.lastUsed
    ];

    const result = await this.executeSQL(sql, params);
    console.log(`Connection profile added: ${record.profileName}`);
    return result.insertId;
  }

  async getConnectionProfiles(gameId?: string): Promise<ConnectionProfileRecord[]> {
    let sql = 'SELECT * FROM connection_profiles';
    const params = [];

    if (gameId) {
      sql += ' WHERE gameId = ?';
      params.push(gameId);
    }

    sql += ' ORDER BY isDefault DESC, lastUsed DESC';

    const result = await this.executeSQL(sql, params);
    const records: ConnectionProfileRecord[] = [];

    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      records.push({
        ...row,
        isDefault: row.isDefault === 1
      });
    }

    return records;
  }

  async updateConnectionProfile(id: number, updates: Partial<ConnectionProfileRecord>): Promise<void> {
    const fields = [];
    const params = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id') {
        fields.push(`${key} = ?`);
        if (key === 'isDefault') {
          params.push(value ? 1 : 0);
        } else {
          params.push(value);
        }
      }
    });

    if (fields.length > 0) {
      params.push(id);
      const sql = `UPDATE connection_profiles SET ${fields.join(', ')} WHERE id = ?`;
      await this.executeSQL(sql, params);
      console.log(`Connection profile updated: ${id}`);
    }
  }

  // Session History Management
  async addSessionHistory(record: Omit<SessionHistoryRecord, 'id'>): Promise<number> {
    const sql = `
      INSERT INTO session_history 
      (sessionId, gameId, hostDevice, participants, startTime, endTime, duration, status, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      record.sessionId,
      record.gameId,
      record.hostDevice,
      record.participants,
      record.startTime,
      record.endTime || null,
      record.duration || null,
      record.status,
      record.notes || null
    ];

    const result = await this.executeSQL(sql, params);
    console.log(`Session history added: ${record.sessionId}`);
    return result.insertId;
  }

  async getSessionHistory(limit: number = 50): Promise<SessionHistoryRecord[]> {
    const sql = 'SELECT * FROM session_history ORDER BY startTime DESC LIMIT ?';
    const result = await this.executeSQL(sql, [limit]);
    
    const records: SessionHistoryRecord[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      records.push(result.rows.item(i));
    }
    
    return records;
  }

  async updateSessionHistory(sessionId: string, updates: Partial<SessionHistoryRecord>): Promise<void> {
    const fields = [];
    const params = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'sessionId') {
        fields.push(`${key} = ?`);
        params.push(value);
      }
    });

    if (fields.length > 0) {
      params.push(sessionId);
      const sql = `UPDATE session_history SET ${fields.join(', ')} WHERE sessionId = ?`;
      await this.executeSQL(sql, params);
      console.log(`Session history updated: ${sessionId}`);
    }
  }

  // User Preferences Management
  async setUserPreference(key: string, value: string, category: string = 'general'): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO user_preferences (key, value, category, lastModified)
      VALUES (?, ?, ?, ?)
    `;

    await this.executeSQL(sql, [key, value, category, Date.now()]);
    console.log(`User preference set: ${key} = ${value}`);
  }

  async getUserPreference(key: string): Promise<string | null> {
    const sql = 'SELECT value FROM user_preferences WHERE key = ?';
    const result = await this.executeSQL(sql, [key]);
    
    if (result.rows.length > 0) {
      return result.rows.item(0).value;
    }
    
    return null;
  }

  async getUserPreferences(category?: string): Promise<UserPreferenceRecord[]> {
    let sql = 'SELECT * FROM user_preferences';
    const params = [];

    if (category) {
      sql += ' WHERE category = ?';
      params.push(category);
    }

    sql += ' ORDER BY category, key';

    const result = await this.executeSQL(sql, params);
    const records: UserPreferenceRecord[] = [];

    for (let i = 0; i < result.rows.length; i++) {
      records.push(result.rows.item(i));
    }

    return records;
  }

  // Database Utilities
  private async executeSQL(sql: string, params: any[] = []): Promise<SQLite.SQLResultSet> {
    return new Promise((resolve, reject) => {
      if (!this.database) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.database.transaction(
        (tx) => {
          tx.executeSql(
            sql,
            params,
            (_, result) => resolve(result),
            (_, error) => {
              console.error('SQL Error:', error);
              reject(error);
              return false;
            }
          );
        },
        (error) => {
          console.error('Transaction Error:', error);
          reject(error);
        }
      );
    });
  }

  private async insertDefaultData(): Promise<void> {
    try {
      // Insert default game compatibility records
      const defaultGames = [
        {
          gameId: 'minecraft',
          gameName: 'Minecraft',
          version: '1.20.x',
          platform: Platform.OS,
          isCompatible: true,
          protocolSupport: ['tcp', 'http', 'https'],
          lastTested: Date.now(),
          notes: 'Fully supported with protocol emulation'
        },
        {
          gameId: 'among_us',
          gameName: 'Among Us',
          version: '2023.x',
          platform: Platform.OS,
          isCompatible: true,
          protocolSupport: ['udp', 'tcp'],
          lastTested: Date.now(),
          notes: 'Supported with custom protocol handling'
        }
      ];

      for (const game of defaultGames) {
        const existing = await this.getGameCompatibility(game.gameId);
        if (!existing) {
          await this.addGameCompatibility(game);
        }
      }

      // Insert default user preferences
      const defaultPreferences = [
        { key: 'auto_connect', value: 'true', category: 'connection' },
        { key: 'preferred_method', value: 'auto', category: 'connection' },
        { key: 'enable_analytics', value: 'true', category: 'privacy' },
        { key: 'theme', value: 'dark', category: 'ui' }
      ];

      for (const pref of defaultPreferences) {
        const existing = await this.getUserPreference(pref.key);
        if (!existing) {
          await this.setUserPreference(pref.key, pref.value, pref.category);
        }
      }

      console.log('Default data inserted successfully');
    } catch (error) {
      console.error('Failed to insert default data:', error);
    }
  }

  // Database Management
  async clearAllData(): Promise<void> {
    const tables = ['game_compatibility', 'connection_profiles', 'session_history', 'user_preferences'];
    
    for (const table of tables) {
      await this.executeSQL(`DELETE FROM ${table}`);
    }
    
    console.log('All database data cleared');
  }

  async getDatabaseInfo(): Promise<any> {
    const info = {
      name: this.config.name,
      version: this.config.version,
      isInitialized: this.isInitialized,
      tables: {}
    };

    if (this.isInitialized) {
      const tables = ['game_compatibility', 'connection_profiles', 'session_history', 'user_preferences'];
      
      for (const table of tables) {
        const result = await this.executeSQL(`SELECT COUNT(*) as count FROM ${table}`);
        info.tables[table] = result.rows.item(0).count;
      }
    }

    return info;
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }
}

export default SQLiteManager.getInstance();
