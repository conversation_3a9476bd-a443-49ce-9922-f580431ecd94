import React, { useState, forwardRef } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
  editable?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  maxLength?: number;
  error?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  variant?: 'default' | 'filled' | 'outlined';
}

const Input = forwardRef<TextInput, InputProps>(({
  label,
  placeholder,
  value,
  onChangeText,
  onFocus,
  onBlur,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = true,
  editable = true,
  multiline = false,
  numberOfLines = 1,
  maxLength,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  style,
  inputStyle,
  variant = 'default',
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const animatedValue = new Animated.Value(value ? 1 : 0);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
    if (!value) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const getContainerStyle = () => {
    const baseStyle = [styles.container];
    
    if (variant === 'filled') {
      baseStyle.push(styles.filledContainer);
    } else if (variant === 'outlined') {
      baseStyle.push(styles.outlinedContainer);
    }
    
    if (isFocused) {
      baseStyle.push(styles.focusedContainer);
    }
    
    if (error) {
      baseStyle.push(styles.errorContainer);
    }
    
    return [...baseStyle, style];
  };

  const labelStyle = {
    ...styles.label,
    transform: [
      {
        translateY: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -20],
        }),
      },
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [1, 0.8],
        }),
      },
    ],
    color: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['rgba(255, 255, 255, 0.6)', isFocused ? '#00D4FF' : 'rgba(255, 255, 255, 0.8)'],
    }),
  };

  return (
    <View style={styles.wrapper}>
      {label && variant !== 'default' && (
        <Text style={styles.topLabel}>{label}</Text>
      )}
      
      <BlurView intensity={15} style={getContainerStyle()}>
        <View style={styles.inputContainer}>
          {leftIcon && (
            <Ionicons
              name={leftIcon}
              size={20}
              color={isFocused ? '#00D4FF' : 'rgba(255, 255, 255, 0.6)'}
              style={styles.leftIcon}
            />
          )}
          
          <View style={styles.textInputWrapper}>
            {label && variant === 'default' && (
              <Animated.Text style={labelStyle}>
                {label}
              </Animated.Text>
            )}
            
            <TextInput
              ref={ref}
              style={[styles.textInput, inputStyle]}
              placeholder={placeholder}
              placeholderTextColor="rgba(255, 255, 255, 0.4)"
              value={value}
              onChangeText={onChangeText}
              onFocus={handleFocus}
              onBlur={handleBlur}
              secureTextEntry={secureTextEntry && !showPassword}
              keyboardType={keyboardType}
              autoCapitalize={autoCapitalize}
              autoCorrect={autoCorrect}
              editable={editable}
              multiline={multiline}
              numberOfLines={numberOfLines}
              maxLength={maxLength}
              selectionColor="#00D4FF"
            />
          </View>
          
          {secureTextEntry && (
            <TouchableOpacity
              onPress={togglePasswordVisibility}
              style={styles.rightIcon}
            >
              <Ionicons
                name={showPassword ? 'eye-off' : 'eye'}
                size={20}
                color={isFocused ? '#00D4FF' : 'rgba(255, 255, 255, 0.6)'}
              />
            </TouchableOpacity>
          )}
          
          {rightIcon && !secureTextEntry && (
            <TouchableOpacity
              onPress={onRightIconPress}
              style={styles.rightIcon}
            >
              <Ionicons
                name={rightIcon}
                size={20}
                color={isFocused ? '#00D4FF' : 'rgba(255, 255, 255, 0.6)'}
              />
            </TouchableOpacity>
          )}
        </View>
      </BlurView>
      
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  wrapper: {
    marginVertical: 8,
  },
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'rgba(0, 212, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  filledContainer: {
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
  },
  outlinedContainer: {
    backgroundColor: 'transparent',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
  },
  focusedContainer: {
    borderColor: '#00D4FF',
    shadowColor: '#00D4FF',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  errorContainer: {
    borderColor: '#FF6B6B',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48,
  },
  leftIcon: {
    marginRight: 12,
  },
  rightIcon: {
    marginLeft: 12,
    padding: 4,
  },
  textInputWrapper: {
    flex: 1,
    position: 'relative',
  },
  textInput: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '400',
    padding: 0,
    margin: 0,
  },
  label: {
    position: 'absolute',
    left: 0,
    top: 0,
    fontSize: 16,
    fontWeight: '400',
    pointerEvents: 'none',
  },
  topLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
    marginLeft: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginTop: 4,
    marginLeft: 4,
  },
});

Input.displayName = 'Input';

export default Input;
