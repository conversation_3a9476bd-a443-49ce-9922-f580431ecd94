import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

// Import all our components
import {
  Button,
  Card,
  Input,
  LoadingSpinner,
  StatusIndicator,
  Modal,
} from '../components/ui';
import {
  GameCard,
  GameList,
  SessionCard,
  CompatibilityBadge,
} from '../components/game';
import {
  QRCodeScanner,
  QRCodeGenerator,
  TroubleshootingPanel,
} from '../components/connection';

// Import hooks and services
import { useGameDetection, useSessionManager } from '../hooks';

export default function DemoScreen() {
  const [showModal, setShowModal] = useState(false);
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showQRGenerator, setShowQRGenerator] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);

  // Use our custom hooks
  const { games, multiplayerGames } = useGameDetection();
  const { currentSession, activeSessions } = useSessionManager();

  const handleButtonPress = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setLoading(false);
    Alert.alert('Success!', 'Button action completed');
  };

  const mockCompatibility = {
    isCompatible: true,
    score: 85,
    issues: [
      {
        type: 'warning' as const,
        category: 'network' as const,
        message: 'WiFi recommended for better performance',
        solution: 'Connect to WiFi for optimal experience',
      },
    ],
    recommendations: [
      'Enable WiFi for best performance',
      'Ensure all players have the same game version',
    ],
    requirements: {
      minPlayers: 2,
      maxPlayers: 8,
      networkTypes: ['wifi', 'bluetooth'] as const,
      platformSupport: ['ios', 'android'] as const,
      permissions: ['INTERNET', 'BLUETOOTH'],
      features: ['cross-platform', 'voice-chat'],
    },
  };

  const mockSession = currentSession || {
    id: 'demo-session',
    name: 'Demo Gaming Session',
    game: games[0] || {
      id: 'demo-game',
      name: 'Demo Game',
      packageName: 'com.demo.game',
      icon: '🎮',
      isInstalled: true,
      supportsMultiplayer: true,
      multiplayerType: 'both' as const,
      category: 'Demo',
      developer: 'Demo Studios',
      rating: 4.5,
      description: 'A demo game for showcasing LoGaCo features',
      requirements: {
        minPlayers: 2,
        maxPlayers: 8,
        networkRequired: false,
        bluetoothRequired: false,
      },
    },
    host: {
      id: 'demo-host',
      name: 'Demo Host',
      deviceType: 'android' as const,
      gameVersion: '1.0.0',
      isHost: true,
      isReady: true,
      connectionStatus: 'connected' as const,
      lastSeen: Date.now(),
    },
    players: [
      {
        id: 'demo-host',
        name: 'Demo Host',
        deviceType: 'android' as const,
        gameVersion: '1.0.0',
        isHost: true,
        isReady: true,
        connectionStatus: 'connected' as const,
        lastSeen: Date.now(),
      },
      {
        id: 'demo-player',
        name: 'Demo Player',
        deviceType: 'ios' as const,
        gameVersion: '1.0.0',
        isHost: false,
        isReady: false,
        connectionStatus: 'connected' as const,
        lastSeen: Date.now(),
      },
    ],
    maxPlayers: 8,
    status: 'waiting' as const,
    settings: {
      isPrivate: false,
      allowSpectators: true,
      maxSpectators: 10,
      autoStart: false,
    },
    createdAt: Date.now(),
    networkType: 'wifi' as const,
    connectionInfo: {
      securityToken: 'demo-token',
    },
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.title}>LoGaCo Demo</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* UI Components Section */}
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>UI Components</Text>
            
            {/* Buttons */}
            <View style={styles.componentGroup}>
              <Text style={styles.componentTitle}>Buttons</Text>
              <View style={styles.buttonRow}>
                <Button
                  title="Primary"
                  onPress={handleButtonPress}
                  variant="primary"
                  size="medium"
                  loading={loading}
                  style={styles.demoButton}
                />
                <Button
                  title="Secondary"
                  onPress={() => Alert.alert('Secondary', 'Secondary button pressed')}
                  variant="secondary"
                  size="medium"
                  style={styles.demoButton}
                />
                <Button
                  title="Ghost"
                  onPress={() => Alert.alert('Ghost', 'Ghost button pressed')}
                  variant="ghost"
                  size="medium"
                  style={styles.demoButton}
                />
              </View>
            </View>

            {/* Input */}
            <View style={styles.componentGroup}>
              <Text style={styles.componentTitle}>Input</Text>
              <Input
                label="Demo Input"
                placeholder="Type something..."
                value={inputValue}
                onChangeText={setInputValue}
                leftIcon="search"
                rightIcon="close"
                onRightIconPress={() => setInputValue('')}
              />
            </View>

            {/* Status Indicators */}
            <View style={styles.componentGroup}>
              <Text style={styles.componentTitle}>Status Indicators</Text>
              <View style={styles.statusRow}>
                <StatusIndicator status="online" label="Online" variant="badge" />
                <StatusIndicator status="warning" label="Warning" variant="badge" />
                <StatusIndicator status="error" label="Error" variant="badge" />
                <StatusIndicator status="info" label="Info" variant="badge" />
              </View>
            </View>

            {/* Loading Spinners */}
            <View style={styles.componentGroup}>
              <Text style={styles.componentTitle}>Loading Spinners</Text>
              <View style={styles.spinnerRow}>
                <LoadingSpinner variant="default" size="small" />
                <LoadingSpinner variant="gradient" size="medium" />
                <LoadingSpinner variant="pulse" size="small" />
                <LoadingSpinner variant="dots" size="medium" />
              </View>
            </View>
          </Card>

          {/* Game Components Section */}
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Game Components</Text>
            
            {/* Game Card */}
            {games.length > 0 && (
              <View style={styles.componentGroup}>
                <Text style={styles.componentTitle}>Game Card</Text>
                <GameCard
                  game={games[0]}
                  variant="default"
                  showCompatibility={true}
                  compatibilityScore={85}
                  onPress={() => Alert.alert('Game Selected', games[0].name)}
                />
              </View>
            )}

            {/* Session Card */}
            <View style={styles.componentGroup}>
              <Text style={styles.componentTitle}>Session Card</Text>
              <SessionCard
                session={mockSession}
                variant="detailed"
                showActions={true}
                onPress={() => Alert.alert('Session Selected', mockSession.name)}
              />
            </View>

            {/* Compatibility Badge */}
            <View style={styles.componentGroup}>
              <Text style={styles.componentTitle}>Compatibility Badge</Text>
              <CompatibilityBadge
                compatibility={mockCompatibility}
                variant="card"
                showDetails={true}
              />
            </View>
          </Card>

          {/* Action Buttons */}
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Interactive Features</Text>
            
            <View style={styles.actionGrid}>
              <Button
                title="Show Modal"
                onPress={() => setShowModal(true)}
                variant="secondary"
                icon={<Ionicons name="layers" size={20} color="#00D4FF" />}
                style={styles.actionButton}
              />
              
              <Button
                title="QR Scanner"
                onPress={() => setShowQRScanner(true)}
                variant="secondary"
                icon={<Ionicons name="qr-code" size={20} color="#00D4FF" />}
                style={styles.actionButton}
              />
              
              <Button
                title="QR Generator"
                onPress={() => setShowQRGenerator(true)}
                variant="secondary"
                icon={<Ionicons name="share" size={20} color="#00D4FF" />}
                style={styles.actionButton}
              />
              
              <Button
                title="Diagnostics"
                onPress={() => setShowTroubleshooting(true)}
                variant="secondary"
                icon={<Ionicons name="medical" size={20} color="#00D4FF" />}
                style={styles.actionButton}
              />
            </View>
          </Card>
        </ScrollView>
      </SafeAreaView>

      {/* Demo Modal */}
      <Modal
        visible={showModal}
        onClose={() => setShowModal(false)}
        title="Demo Modal"
        subtitle="This is a demo modal"
        icon="information-circle"
        variant="center"
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalText}>
            This modal demonstrates the glassmorphism design with blur effects and gradients.
          </Text>
          <Button
            title="Close Modal"
            onPress={() => setShowModal(false)}
            variant="primary"
            size="medium"
          />
        </View>
      </Modal>

      {/* QR Scanner Modal */}
      <Modal
        visible={showQRScanner}
        onClose={() => setShowQRScanner(false)}
        variant="fullscreen"
      >
        <QRCodeScanner
          onScan={(data) => {
            setShowQRScanner(false);
            Alert.alert('QR Scanned', `Data: ${data}`);
          }}
          onClose={() => setShowQRScanner(false)}
        />
      </Modal>

      {/* QR Generator Modal */}
      <Modal
        visible={showQRGenerator}
        onClose={() => setShowQRGenerator(false)}
        title="Share Connection"
        variant="center"
      >
        <QRCodeGenerator
          deviceInfo={{
            id: 'demo-device',
            name: 'Demo Device',
            type: 'android',
            connectionInfo: {
              ip: '*************',
              port: 8080,
            },
          }}
          onClose={() => setShowQRGenerator(false)}
        />
      </Modal>

      {/* Troubleshooting Modal */}
      <Modal
        visible={showTroubleshooting}
        onClose={() => setShowTroubleshooting(false)}
        variant="fullscreen"
      >
        <TroubleshootingPanel
          onClose={() => setShowTroubleshooting(false)}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  componentGroup: {
    marginBottom: 20,
  },
  componentTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00D4FF',
    marginBottom: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
  },
  demoButton: {
    flex: 1,
  },
  statusRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  spinnerRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 20,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    minWidth: '45%',
  },
  modalContent: {
    gap: 16,
    alignItems: 'center',
  },
  modalText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
  },
});
