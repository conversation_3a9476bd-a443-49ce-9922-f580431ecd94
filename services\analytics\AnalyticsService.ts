import { Platform } from 'react-native';
import { EventEmitter } from 'events';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface AnalyticsEvent {
  id: string;
  name: string;
  category: 'user' | 'system' | 'performance' | 'error' | 'business';
  properties: Record<string, any>;
  timestamp: number;
  sessionId: string;
  userId?: string;
  deviceInfo: DeviceInfo;
}

export interface DeviceInfo {
  platform: string;
  version: string;
  model?: string;
  screenSize: { width: number; height: number };
  locale: string;
  timezone: string;
  networkType?: string;
  batteryLevel?: number;
  memoryUsage?: number;
}

export interface CrashReport {
  id: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: {
    component?: string;
    action?: string;
    userId?: string;
    sessionId: string;
  };
  deviceInfo: DeviceInfo;
  breadcrumbs: Breadcrumb[];
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface Breadcrumb {
  id: string;
  message: string;
  category: 'navigation' | 'user-action' | 'network' | 'state-change' | 'error';
  level: 'info' | 'warning' | 'error';
  timestamp: number;
  data?: Record<string, any>;
}

export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  category: 'startup' | 'navigation' | 'network' | 'rendering' | 'memory';
  timestamp: number;
  sessionId: string;
  additionalData?: Record<string, any>;
}

export interface UserSession {
  id: string;
  userId?: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  screenViews: string[];
  eventCount: number;
  crashCount: number;
  deviceInfo: DeviceInfo;
}

class AnalyticsService extends EventEmitter {
  private static instance: AnalyticsService;
  private isInitialized: boolean = false;
  private currentSession: UserSession | null = null;
  private breadcrumbs: Breadcrumb[] = [];
  private eventQueue: AnalyticsEvent[] = [];
  private crashQueue: CrashReport[] = [];
  private performanceQueue: PerformanceMetric[] = [];
  private maxBreadcrumbs: number = 100;
  private maxQueueSize: number = 1000;
  private flushInterval: NodeJS.Timeout | null = null;
  private deviceInfo: DeviceInfo | null = null;

  private constructor() {
    super();
  }

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Initialization
  async initialize(userId?: string): Promise<void> {
    try {
      console.log('Initializing Analytics Service...');

      // Collect device information
      this.deviceInfo = await this.collectDeviceInfo();

      // Start new session
      await this.startSession(userId);

      // Setup error handling
      this.setupErrorHandling();

      // Setup performance monitoring
      this.setupPerformanceMonitoring();

      // Start automatic data flushing
      this.startAutoFlush();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('Analytics Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Analytics Service:', error);
      throw error;
    }
  }

  private async collectDeviceInfo(): Promise<DeviceInfo> {
    try {
      const deviceInfo: DeviceInfo = {
        platform: Platform.OS,
        version: Platform.Version.toString(),
        screenSize: { width: 0, height: 0 },
        locale: 'en-US',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      };

      // Platform-specific information
      if (Platform.OS === 'web') {
        deviceInfo.screenSize = {
          width: window.screen.width,
          height: window.screen.height
        };
        deviceInfo.locale = navigator.language;
        deviceInfo.networkType = (navigator as any).connection?.effectiveType || 'unknown';
        deviceInfo.model = navigator.userAgent;
      } else {
        // React Native - would use device info libraries
        deviceInfo.screenSize = { width: 375, height: 812 }; // Default iPhone size
        deviceInfo.model = 'Mobile Device';
      }

      return deviceInfo;
    } catch (error) {
      console.error('Failed to collect device info:', error);
      return {
        platform: Platform.OS,
        version: Platform.Version.toString(),
        screenSize: { width: 0, height: 0 },
        locale: 'en-US',
        timezone: 'UTC'
      };
    }
  }

  // Session Management
  async startSession(userId?: string): Promise<void> {
    try {
      // End current session if exists
      if (this.currentSession) {
        await this.endSession();
      }

      this.currentSession = {
        id: this.generateSessionId(),
        userId,
        startTime: Date.now(),
        screenViews: [],
        eventCount: 0,
        crashCount: 0,
        deviceInfo: this.deviceInfo!
      };

      this.addBreadcrumb({
        message: 'Session started',
        category: 'state-change',
        level: 'info'
      });

      this.emit('sessionStarted', this.currentSession);
      console.log(`Analytics session started: ${this.currentSession.id}`);
    } catch (error) {
      console.error('Failed to start session:', error);
    }
  }

  async endSession(): Promise<void> {
    try {
      if (!this.currentSession) return;

      this.currentSession.endTime = Date.now();
      this.currentSession.duration = this.currentSession.endTime - this.currentSession.startTime;

      this.addBreadcrumb({
        message: 'Session ended',
        category: 'state-change',
        level: 'info'
      });

      // Save session data
      await this.saveSessionData(this.currentSession);

      this.emit('sessionEnded', this.currentSession);
      console.log(`Analytics session ended: ${this.currentSession.id}`);
      
      this.currentSession = null;
    } catch (error) {
      console.error('Failed to end session:', error);
    }
  }

  // Event Tracking
  trackEvent(name: string, properties: Record<string, any> = {}, category: AnalyticsEvent['category'] = 'user'): void {
    try {
      if (!this.isInitialized || !this.currentSession || !this.deviceInfo) {
        console.warn('Analytics not initialized, event not tracked:', name);
        return;
      }

      const event: AnalyticsEvent = {
        id: this.generateEventId(),
        name,
        category,
        properties,
        timestamp: Date.now(),
        sessionId: this.currentSession.id,
        userId: this.currentSession.userId,
        deviceInfo: this.deviceInfo
      };

      this.eventQueue.push(event);
      this.currentSession.eventCount++;

      // Add breadcrumb for user events
      if (category === 'user') {
        this.addBreadcrumb({
          message: `User event: ${name}`,
          category: 'user-action',
          level: 'info',
          data: properties
        });
      }

      this.emit('eventTracked', event);
      console.log(`Event tracked: ${name}`, properties);

      // Flush if queue is getting large
      if (this.eventQueue.length >= this.maxQueueSize) {
        this.flushEvents();
      }
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  }

  trackScreenView(screenName: string, properties: Record<string, any> = {}): void {
    try {
      if (!this.currentSession) return;

      this.currentSession.screenViews.push(screenName);

      this.trackEvent('screen_view', {
        screen_name: screenName,
        ...properties
      }, 'user');

      this.addBreadcrumb({
        message: `Screen viewed: ${screenName}`,
        category: 'navigation',
        level: 'info'
      });

      console.log(`Screen view tracked: ${screenName}`);
    } catch (error) {
      console.error('Failed to track screen view:', error);
    }
  }

  // Performance Monitoring
  trackPerformance(name: string, value: number, unit: PerformanceMetric['unit'], category: PerformanceMetric['category'], additionalData?: Record<string, any>): void {
    try {
      if (!this.isInitialized || !this.currentSession) return;

      const metric: PerformanceMetric = {
        id: this.generateMetricId(),
        name,
        value,
        unit,
        category,
        timestamp: Date.now(),
        sessionId: this.currentSession.id,
        additionalData
      };

      this.performanceQueue.push(metric);

      this.emit('performanceTracked', metric);
      console.log(`Performance metric tracked: ${name} = ${value}${unit}`);
    } catch (error) {
      console.error('Failed to track performance:', error);
    }
  }

  measureExecutionTime<T>(name: string, fn: () => T | Promise<T>): T | Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = Date.now() - startTime;
          this.trackPerformance(name, duration, 'ms', 'startup');
        });
      } else {
        const duration = Date.now() - startTime;
        this.trackPerformance(name, duration, 'ms', 'startup');
        return result;
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      this.trackPerformance(name, duration, 'ms', 'startup');
      throw error;
    }
  }

  // Error and Crash Reporting
  reportError(error: Error, context: Partial<CrashReport['context']> = {}, severity: CrashReport['severity'] = 'medium'): void {
    try {
      if (!this.isInitialized || !this.currentSession || !this.deviceInfo) {
        console.warn('Analytics not initialized, error not reported');
        return;
      }

      const crashReport: CrashReport = {
        id: this.generateCrashId(),
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        context: {
          sessionId: this.currentSession.id,
          userId: this.currentSession.userId,
          ...context
        },
        deviceInfo: this.deviceInfo,
        breadcrumbs: [...this.breadcrumbs],
        timestamp: Date.now(),
        severity
      };

      this.crashQueue.push(crashReport);
      this.currentSession.crashCount++;

      this.addBreadcrumb({
        message: `Error reported: ${error.message}`,
        category: 'error',
        level: 'error'
      });

      this.emit('errorReported', crashReport);
      console.error('Error reported to analytics:', error);

      // Immediately flush critical errors
      if (severity === 'critical') {
        this.flushCrashReports();
      }
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  }

  // Breadcrumbs
  addBreadcrumb(breadcrumb: Omit<Breadcrumb, 'id' | 'timestamp'>): void {
    try {
      const fullBreadcrumb: Breadcrumb = {
        id: this.generateBreadcrumbId(),
        timestamp: Date.now(),
        ...breadcrumb
      };

      this.breadcrumbs.push(fullBreadcrumb);

      // Keep only the most recent breadcrumbs
      if (this.breadcrumbs.length > this.maxBreadcrumbs) {
        this.breadcrumbs = this.breadcrumbs.slice(-this.maxBreadcrumbs);
      }

      this.emit('breadcrumbAdded', fullBreadcrumb);
    } catch (error) {
      console.error('Failed to add breadcrumb:', error);
    }
  }

  // Data Flushing
  async flushEvents(): Promise<void> {
    try {
      if (this.eventQueue.length === 0) return;

      const events = [...this.eventQueue];
      this.eventQueue = [];

      await this.sendEvents(events);
      console.log(`Flushed ${events.length} events`);
    } catch (error) {
      console.error('Failed to flush events:', error);
      // Re-add events to queue on failure
      this.eventQueue.unshift(...this.eventQueue);
    }
  }

  async flushCrashReports(): Promise<void> {
    try {
      if (this.crashQueue.length === 0) return;

      const crashes = [...this.crashQueue];
      this.crashQueue = [];

      await this.sendCrashReports(crashes);
      console.log(`Flushed ${crashes.length} crash reports`);
    } catch (error) {
      console.error('Failed to flush crash reports:', error);
      // Re-add crashes to queue on failure
      this.crashQueue.unshift(...crashes);
    }
  }

  async flushPerformanceMetrics(): Promise<void> {
    try {
      if (this.performanceQueue.length === 0) return;

      const metrics = [...this.performanceQueue];
      this.performanceQueue = [];

      await this.sendPerformanceMetrics(metrics);
      console.log(`Flushed ${metrics.length} performance metrics`);
    } catch (error) {
      console.error('Failed to flush performance metrics:', error);
      // Re-add metrics to queue on failure
      this.performanceQueue.unshift(...metrics);
    }
  }

  async flushAll(): Promise<void> {
    await Promise.all([
      this.flushEvents(),
      this.flushCrashReports(),
      this.flushPerformanceMetrics()
    ]);
  }

  // Setup Methods
  private setupErrorHandling(): void {
    // Global error handler
    if (Platform.OS === 'web') {
      window.addEventListener('error', (event) => {
        this.reportError(new Error(event.message), {
          component: 'global',
          action: 'unhandled_error'
        }, 'high');
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.reportError(new Error(event.reason), {
          component: 'global',
          action: 'unhandled_promise_rejection'
        }, 'high');
      });
    }
  }

  private setupPerformanceMonitoring(): void {
    // Monitor memory usage periodically
    setInterval(() => {
      if (Platform.OS === 'web' && (performance as any).memory) {
        const memory = (performance as any).memory;
        this.trackPerformance('memory_used', memory.usedJSHeapSize, 'bytes', 'memory');
        this.trackPerformance('memory_total', memory.totalJSHeapSize, 'bytes', 'memory');
      }
    }, 30000); // Every 30 seconds
  }

  private startAutoFlush(): void {
    this.flushInterval = setInterval(() => {
      this.flushAll();
    }, 60000); // Flush every minute
  }

  // Network Methods (simulated)
  private async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    // Simulate sending events to analytics service
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async sendCrashReports(crashes: CrashReport[]): Promise<void> {
    // Simulate sending crash reports to crash reporting service
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async sendPerformanceMetrics(metrics: PerformanceMetric[]): Promise<void> {
    // Simulate sending performance metrics to monitoring service
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async saveSessionData(session: UserSession): Promise<void> {
    try {
      await AsyncStorage.setItem(`session_${session.id}`, JSON.stringify(session));
    } catch (error) {
      console.error('Failed to save session data:', error);
    }
  }

  // ID Generators
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCrashId(): string {
    return `crash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBreadcrumbId(): string {
    return `breadcrumb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Getters
  getCurrentSession(): UserSession | null {
    return this.currentSession;
  }

  getBreadcrumbs(): Breadcrumb[] {
    return [...this.breadcrumbs];
  }

  getQueueSizes(): { events: number; crashes: number; performance: number } {
    return {
      events: this.eventQueue.length,
      crashes: this.crashQueue.length,
      performance: this.performanceQueue.length
    };
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }
}

export default AnalyticsService.getInstance();
