package com.logaco.networking;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.net.VpnService;
import android.os.IBinder;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

public class VPNServiceBridge extends ReactContextBaseJavaModule implements ActivityEventListener {
    private static final String MODULE_NAME = "VPNServiceBridge";
    private static final int VPN_REQUEST_CODE = 1001;
    
    private ReactApplicationContext reactContext;
    private LoGaCoVPNService vpnService;
    private boolean isServiceBound = false;
    private Promise vpnStartPromise;
    
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            // VPN service doesn't return a binder in this implementation
            // Service communication happens through intents and events
            isServiceBound = true;
        }
        
        @Override
        public void onServiceDisconnected(ComponentName name) {
            isServiceBound = false;
            vpnService = null;
        }
    };
    
    public VPNServiceBridge(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        reactContext.addActivityEventListener(this);
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    @ReactMethod
    public void requestVPNPermission(Promise promise) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) {
            promise.reject("NO_ACTIVITY", "No current activity available");
            return;
        }
        
        Intent intent = VpnService.prepare(reactContext);
        if (intent != null) {
            vpnStartPromise = promise;
            currentActivity.startActivityForResult(intent, VPN_REQUEST_CODE);
        } else {
            // VPN permission already granted
            WritableMap result = Arguments.createMap();
            result.putString("status", "permission_granted");
            promise.resolve(result);
        }
    }
    
    @ReactMethod
    public void startVPN(Promise promise) {
        try {
            Intent intent = VpnService.prepare(reactContext);
            if (intent != null) {
                promise.reject("PERMISSION_REQUIRED", "VPN permission not granted");
                return;
            }
            
            Intent serviceIntent = new Intent(reactContext, LoGaCoVPNService.class);
            serviceIntent.setAction("START_VPN");
            
            ComponentName component = reactContext.startService(serviceIntent);
            if (component != null) {
                // Bind to service for communication
                reactContext.bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE);
                
                WritableMap result = Arguments.createMap();
                result.putString("status", "vpn_starting");
                promise.resolve(result);
            } else {
                promise.reject("START_FAILED", "Failed to start VPN service");
            }
            
        } catch (Exception e) {
            promise.reject("START_ERROR", "Error starting VPN: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void stopVPN(Promise promise) {
        try {
            Intent serviceIntent = new Intent(reactContext, LoGaCoVPNService.class);
            serviceIntent.setAction("STOP_VPN");
            
            reactContext.startService(serviceIntent);
            
            if (isServiceBound) {
                reactContext.unbindService(serviceConnection);
                isServiceBound = false;
            }
            
            WritableMap result = Arguments.createMap();
            result.putString("status", "vpn_stopping");
            promise.resolve(result);
            
        } catch (Exception e) {
            promise.reject("STOP_ERROR", "Error stopping VPN: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void getVPNStatus(Promise promise) {
        try {
            WritableMap status = Arguments.createMap();
            status.putBoolean("isRunning", isServiceBound);
            status.putBoolean("isServiceBound", isServiceBound);
            
            if (vpnService != null) {
                status.putDouble("bytesReceived", vpnService.getBytesReceived());
                status.putDouble("bytesSent", vpnService.getBytesSent());
                status.putDouble("packetsProcessed", vpnService.getPacketsProcessed());
            } else {
                status.putDouble("bytesReceived", 0);
                status.putDouble("bytesSent", 0);
                status.putDouble("packetsProcessed", 0);
            }
            
            promise.resolve(status);
            
        } catch (Exception e) {
            promise.reject("STATUS_ERROR", "Error getting VPN status: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void isVPNPermissionGranted(Promise promise) {
        try {
            Intent intent = VpnService.prepare(reactContext);
            boolean isGranted = (intent == null);
            
            WritableMap result = Arguments.createMap();
            result.putBoolean("granted", isGranted);
            promise.resolve(result);
            
        } catch (Exception e) {
            promise.reject("PERMISSION_CHECK_ERROR", "Error checking VPN permission: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void addListener(String eventName) {
        // Required for RN built in Event Emitter Calls
    }
    
    @ReactMethod
    public void removeListeners(Integer count) {
        // Required for RN built in Event Emitter Calls
    }
    
    // ActivityEventListener implementation
    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (requestCode == VPN_REQUEST_CODE && vpnStartPromise != null) {
            WritableMap result = Arguments.createMap();
            
            if (resultCode == Activity.RESULT_OK) {
                result.putString("status", "permission_granted");
                vpnStartPromise.resolve(result);
            } else {
                vpnStartPromise.reject("PERMISSION_DENIED", "VPN permission denied by user");
            }
            
            vpnStartPromise = null;
        }
    }
    
    @Override
    public void onNewIntent(Intent intent) {
        // Not needed for this implementation
    }
    
    // Event emission helpers
    private void sendEvent(String eventName, WritableMap params) {
        if (reactContext.hasActiveCatalystInstance()) {
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, params);
        }
    }
    
    // Utility methods
    @ReactMethod
    public void getNetworkInterfaces(Promise promise) {
        try {
            WritableMap interfaces = Arguments.createMap();
            
            // Get network interface information
            java.util.Enumeration<java.net.NetworkInterface> networkInterfaces = 
                java.net.NetworkInterface.getNetworkInterfaces();
            
            int index = 0;
            while (networkInterfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = networkInterfaces.nextElement();
                
                if (!networkInterface.isLoopback() && networkInterface.isUp()) {
                    WritableMap interfaceInfo = Arguments.createMap();
                    interfaceInfo.putString("name", networkInterface.getName());
                    interfaceInfo.putString("displayName", networkInterface.getDisplayName());
                    interfaceInfo.putBoolean("isUp", networkInterface.isUp());
                    interfaceInfo.putBoolean("isLoopback", networkInterface.isLoopback());
                    interfaceInfo.putBoolean("isVirtual", networkInterface.isVirtual());
                    
                    // Get IP addresses
                    java.util.Enumeration<java.net.InetAddress> addresses = networkInterface.getInetAddresses();
                    WritableMap addressMap = Arguments.createMap();
                    int addressIndex = 0;
                    
                    while (addresses.hasMoreElements()) {
                        java.net.InetAddress address = addresses.nextElement();
                        addressMap.putString(String.valueOf(addressIndex), address.getHostAddress());
                        addressIndex++;
                    }
                    
                    interfaceInfo.putMap("addresses", addressMap);
                    interfaces.putMap(String.valueOf(index), interfaceInfo);
                    index++;
                }
            }
            
            promise.resolve(interfaces);
            
        } catch (Exception e) {
            promise.reject("NETWORK_INTERFACES_ERROR", "Error getting network interfaces: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void getSystemNetworkInfo(Promise promise) {
        try {
            WritableMap networkInfo = Arguments.createMap();
            
            // Get basic network information
            android.net.ConnectivityManager connectivityManager = 
                (android.net.ConnectivityManager) reactContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            
            if (connectivityManager != null) {
                android.net.NetworkInfo activeNetwork = connectivityManager.getActiveNetworkInfo();
                
                if (activeNetwork != null) {
                    networkInfo.putString("type", activeNetwork.getTypeName());
                    networkInfo.putString("subtype", activeNetwork.getSubtypeName());
                    networkInfo.putBoolean("isConnected", activeNetwork.isConnected());
                    networkInfo.putBoolean("isAvailable", activeNetwork.isAvailable());
                    networkInfo.putString("state", activeNetwork.getState().toString());
                    networkInfo.putString("reason", activeNetwork.getReason());
                    networkInfo.putString("extraInfo", activeNetwork.getExtraInfo());
                }
            }
            
            promise.resolve(networkInfo);
            
        } catch (Exception e) {
            promise.reject("SYSTEM_NETWORK_ERROR", "Error getting system network info: " + e.getMessage());
        }
    }
}
