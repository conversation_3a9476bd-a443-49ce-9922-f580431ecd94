import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ViewStyle } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SessionParticipant, ConnectionStatus } from '../../services/sessions/SessionManager';

interface SessionParticipantsProps {
  participants: Map<string, SessionParticipant>;
  maxParticipants: number;
  hostUserId: string;
  currentUserId?: string;
  onKickParticipant?: (userId: string) => void;
  onTransferHost?: (userId: string) => void;
  style?: ViewStyle;
  variant?: 'list' | 'grid' | 'compact';
  showActions?: boolean;
}

export default function SessionParticipants({
  participants,
  maxParticipants,
  hostUserId,
  currentUserId,
  onKickParticipant,
  onTransferHost,
  style,
  variant = 'list',
  showActions = true,
}: SessionParticipantsProps) {
  const [expandedParticipant, setExpandedParticipant] = useState<string | null>(null);
  
  const participantsList = Array.from(participants.values());
  const isCurrentUserHost = currentUserId === hostUserId;
  const emptySlots = maxParticipants - participantsList.length;

  const getConnectionStatusColor = (status: ConnectionStatus): string => {
    switch (status) {
      case ConnectionStatus.CONNECTED:
        return '#00FF88';
      case ConnectionStatus.CONNECTING:
        return '#FFB800';
      case ConnectionStatus.RECONNECTING:
        return '#FF8C00';
      case ConnectionStatus.DISCONNECTED:
        return '#FF4757';
      default:
        return '#CCCCCC';
    }
  };

  const getConnectionStatusText = (status: ConnectionStatus): string => {
    switch (status) {
      case ConnectionStatus.CONNECTED:
        return 'Connected';
      case ConnectionStatus.CONNECTING:
        return 'Connecting...';
      case ConnectionStatus.RECONNECTING:
        return 'Reconnecting...';
      case ConnectionStatus.DISCONNECTED:
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const formatJoinTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just joined';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m ago`;
  };

  const handleParticipantPress = (participant: SessionParticipant) => {
    if (variant === 'compact') return;
    
    setExpandedParticipant(
      expandedParticipant === participant.userId ? null : participant.userId
    );
  };

  const renderParticipantActions = (participant: SessionParticipant) => {
    if (!showActions || !isCurrentUserHost || participant.userId === currentUserId) {
      return null;
    }

    return (
      <View style={styles.participantActions}>
        {!participant.isHost && (
          <TouchableOpacity
            style={[styles.actionButton, styles.transferHostButton]}
            onPress={() => onTransferHost?.(participant.userId)}
          >
            <Ionicons name="crown" size={14} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Make Host</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.kickButton]}
          onPress={() => onKickParticipant?.(participant.userId)}
        >
          <Ionicons name="remove-circle" size={14} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Kick</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderParticipantItem = ({ item: participant }: { item: SessionParticipant }) => {
    const isExpanded = expandedParticipant === participant.userId;
    const connectionColor = getConnectionStatusColor(participant.connectionStatus);

    return (
      <TouchableOpacity
        style={styles.participantItem}
        onPress={() => handleParticipantPress(participant)}
        disabled={variant === 'compact'}
      >
        <BlurView intensity={15} style={styles.participantBlur}>
          <View style={styles.participantContent}>
            <View style={styles.participantMain}>
              <View style={styles.participantLeft}>
                <View style={[
                  styles.participantAvatar,
                  { borderColor: participant.isHost ? '#FFD700' : '#00D4FF' }
                ]}>
                  <Text style={styles.participantInitial}>
                    {participant.username.charAt(0).toUpperCase()}
                  </Text>
                  {participant.isHost && (
                    <View style={styles.hostBadge}>
                      <Ionicons name="crown" size={10} color="#FFD700" />
                    </View>
                  )}
                </View>
                
                <View style={styles.participantInfo}>
                  <View style={styles.participantNameRow}>
                    <Text style={styles.participantName} numberOfLines={1}>
                      {participant.username}
                    </Text>
                    {participant.userId === currentUserId && (
                      <Text style={styles.youLabel}>(You)</Text>
                    )}
                  </View>
                  
                  <View style={styles.connectionStatus}>
                    <View style={[
                      styles.connectionDot,
                      { backgroundColor: connectionColor }
                    ]} />
                    <Text style={[
                      styles.connectionText,
                      { color: connectionColor }
                    ]}>
                      {getConnectionStatusText(participant.connectionStatus)}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.participantRight}>
                <Text style={styles.joinTime}>
                  {formatJoinTime(participant.joinedAt)}
                </Text>
                
                {variant !== 'compact' && (
                  <Ionicons
                    name={isExpanded ? 'chevron-up' : 'chevron-down'}
                    size={16}
                    color="rgba(255, 255, 255, 0.5)"
                  />
                )}
              </View>
            </View>

            {isExpanded && (
              <View style={styles.participantDetails}>
                {participant.deviceInfo && (
                  <View style={styles.deviceInfo}>
                    <Ionicons name="phone-portrait" size={14} color="rgba(255, 255, 255, 0.7)" />
                    <Text style={styles.deviceText}>
                      {participant.deviceInfo.platform} • {participant.deviceInfo.model}
                    </Text>
                  </View>
                )}
                
                {renderParticipantActions(participant)}
              </View>
            )}
          </View>
        </BlurView>
      </TouchableOpacity>
    );
  };

  const renderEmptySlot = (index: number) => (
    <View key={`empty-${index}`} style={styles.emptySlot}>
      <BlurView intensity={10} style={styles.emptySlotBlur}>
        <View style={styles.emptySlotContent}>
          <View style={styles.emptyAvatar}>
            <Ionicons name="add" size={20} color="rgba(255, 255, 255, 0.3)" />
          </View>
          <Text style={styles.emptySlotText}>Waiting for player...</Text>
        </View>
      </BlurView>
    </View>
  );

  const renderCompactView = () => (
    <View style={[styles.container, styles.compactContainer, style]}>
      <View style={styles.compactHeader}>
        <Text style={styles.participantsTitle}>
          Players ({participantsList.length}/{maxParticipants})
        </Text>
      </View>
      
      <View style={styles.compactList}>
        {participantsList.map((participant) => (
          <View key={participant.userId} style={styles.compactParticipant}>
            <View style={[
              styles.compactAvatar,
              { borderColor: participant.isHost ? '#FFD700' : '#00D4FF' }
            ]}>
              <Text style={styles.compactInitial}>
                {participant.username.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={[
              styles.compactConnectionDot,
              { backgroundColor: getConnectionStatusColor(participant.connectionStatus) }
            ]} />
          </View>
        ))}
        
        {Array.from({ length: emptySlots }, (_, index) => (
          <View key={`empty-${index}`} style={styles.compactParticipant}>
            <View style={styles.compactEmptyAvatar}>
              <Ionicons name="add" size={12} color="rgba(255, 255, 255, 0.3)" />
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const renderListView = () => (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.participantsTitle}>
          Participants ({participantsList.length}/{maxParticipants})
        </Text>
      </View>
      
      <FlatList
        data={participantsList}
        renderItem={renderParticipantItem}
        keyExtractor={(item) => item.userId}
        style={styles.participantsList}
        showsVerticalScrollIndicator={false}
      />
      
      {emptySlots > 0 && (
        <View style={styles.emptySlotsContainer}>
          {Array.from({ length: emptySlots }, (_, index) => renderEmptySlot(index))}
        </View>
      )}
    </View>
  );

  if (variant === 'compact') {
    return renderCompactView();
  }

  return renderListView();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  compactHeader: {
    marginRight: 12,
  },
  participantsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  participantsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  compactList: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  participantItem: {
    marginVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  participantBlur: {
    padding: 12,
  },
  participantContent: {
    flex: 1,
  },
  participantMain: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  participantLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  participantAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    position: 'relative',
  },
  compactAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  compactEmptyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantInitial: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  compactInitial: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  hostBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#1a1a2e',
    borderRadius: 8,
    padding: 2,
  },
  participantInfo: {
    flex: 1,
  },
  participantNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginRight: 8,
  },
  youLabel: {
    fontSize: 12,
    color: '#00D4FF',
    fontWeight: '500',
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6,
  },
  compactConnectionDot: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 2,
    borderColor: '#1a1a2e',
  },
  connectionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  participantRight: {
    alignItems: 'flex-end',
  },
  joinTime: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 4,
  },
  participantDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  deviceText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  participantActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  transferHostButton: {
    backgroundColor: '#FFD700',
  },
  kickButton: {
    backgroundColor: '#FF4757',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  emptySlotsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  emptySlot: {
    marginVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  emptySlotBlur: {
    padding: 12,
  },
  emptySlotContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  emptySlotText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.5)',
    fontStyle: 'italic',
  },
  compactParticipant: {
    position: 'relative',
  },
});
