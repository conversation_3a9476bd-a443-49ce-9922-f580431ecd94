import TrafficRouter from '../../../services/networking/TrafficRouter';
import type { TrafficRule, NetworkPacket, RoutingTable } from '../../../services/networking/TrafficRouter';

describe('TrafficRouter', () => {
  let trafficRouter: typeof TrafficRouter;

  beforeEach(async () => {
    trafficRouter = TrafficRouter;
    await trafficRouter.initialize();
  });

  afterEach(() => {
    trafficRouter.stopRouting();
  });

  describe('Initialization', () => {
    it('should initialize successfully', () => {
      expect(trafficRouter.isInitializedState()).toBe(true);
    });

    it('should load default traffic rules', () => {
      const rules = trafficRouter.getTrafficRules();
      expect(rules.length).toBeGreaterThan(0);
      
      // Check for Minecraft rule
      const minecraftRule = rules.find(rule => rule.gameId === 'minecraft');
      expect(minecraftRule).toBeDefined();
      expect(minecraftRule?.protocol).toBe('tcp');
      expect(minecraftRule?.destinationPort).toBe(25565);
    });
  });

  describe('Routing Table Management', () => {
    it('should create routing table for game session', async () => {
      const gameId = 'minecraft';
      const sessionId = 'test-session-123';

      await trafficRouter.createRoutingTable(gameId, sessionId);
      
      const routingTables = trafficRouter.getRoutingTables();
      const table = routingTables.find(t => t.sessionId === sessionId);
      
      expect(table).toBeDefined();
      expect(table?.gameId).toBe(gameId);
      expect(table?.sessionId).toBe(sessionId);
    });

    it('should add device to session', async () => {
      const gameId = 'minecraft';
      const sessionId = 'test-session-123';
      const deviceId = 'device-1';
      const ipAddress = '*************';

      await trafficRouter.createRoutingTable(gameId, sessionId);
      await trafficRouter.addDeviceToSession(sessionId, deviceId, ipAddress);

      const routingTables = trafficRouter.getRoutingTables();
      const table = routingTables.find(t => t.sessionId === sessionId);
      
      expect(table?.devices.has(deviceId)).toBe(true);
      expect(table?.devices.get(deviceId)).toBe(ipAddress);
    });

    it('should remove device from session', async () => {
      const gameId = 'minecraft';
      const sessionId = 'test-session-123';
      const deviceId = 'device-1';
      const ipAddress = '*************';

      await trafficRouter.createRoutingTable(gameId, sessionId);
      await trafficRouter.addDeviceToSession(sessionId, deviceId, ipAddress);
      await trafficRouter.removeDeviceFromSession(sessionId, deviceId);

      const routingTables = trafficRouter.getRoutingTables();
      const table = routingTables.find(t => t.sessionId === sessionId);
      
      expect(table?.devices.has(deviceId)).toBe(false);
    });

    it('should throw error when adding device to non-existent session', async () => {
      const sessionId = 'non-existent-session';
      const deviceId = 'device-1';
      const ipAddress = '*************';

      await expect(
        trafficRouter.addDeviceToSession(sessionId, deviceId, ipAddress)
      ).rejects.toThrow('Routing table not found for session');
    });
  });

  describe('Traffic Rule Management', () => {
    it('should add custom traffic rule', async () => {
      const rule: TrafficRule = {
        id: 'custom-rule-1',
        gameId: 'custom-game',
        protocol: 'udp',
        destinationPort: 8080,
        hostPattern: '.*\\.example\\.com',
        action: 'redirect',
        priority: 50,
        enabled: true
      };

      await trafficRouter.addTrafficRule(rule);
      
      const rules = trafficRouter.getTrafficRules();
      const addedRule = rules.find(r => r.id === rule.id);
      
      expect(addedRule).toBeDefined();
      expect(addedRule).toMatchObject(rule);
    });

    it('should remove traffic rule', async () => {
      const rule: TrafficRule = {
        id: 'rule-to-remove',
        gameId: 'test-game',
        protocol: 'tcp',
        hostPattern: '.*\\.test\\.com',
        action: 'block',
        priority: 10,
        enabled: true
      };

      await trafficRouter.addTrafficRule(rule);
      await trafficRouter.removeTrafficRule(rule.id);
      
      const rules = trafficRouter.getTrafficRules();
      const removedRule = rules.find(r => r.id === rule.id);
      
      expect(removedRule).toBeUndefined();
    });

    it('should throw error when removing non-existent rule', async () => {
      await expect(
        trafficRouter.removeTrafficRule('non-existent-rule')
      ).rejects.toThrow('Traffic rule not found');
    });
  });

  describe('Packet Routing', () => {
    beforeEach(async () => {
      // Setup test routing table
      await trafficRouter.createRoutingTable('minecraft', 'test-session');
      await trafficRouter.addDeviceToSession('test-session', 'device-1', '*************');
      trafficRouter.startRouting();
    });

    it('should route packet successfully', async () => {
      const packet: NetworkPacket = {
        id: 'packet-1',
        protocol: 'tcp',
        sourceIP: '************',
        destinationIP: 'minecraft.server.com',
        sourcePort: 12345,
        destinationPort: 25565,
        data: Buffer.from('test packet data'),
        timestamp: Date.now(),
        gameId: 'minecraft',
        sessionId: 'test-session'
      };

      const result = await trafficRouter.routePacket(packet);
      expect(result).toBe(true);
    });

    it('should handle packet routing failure gracefully', async () => {
      const packet: NetworkPacket = {
        id: 'packet-2',
        protocol: 'tcp',
        sourceIP: '************',
        destinationIP: 'unknown.server.com',
        sourcePort: 12345,
        destinationPort: 9999,
        data: Buffer.from('test packet data'),
        timestamp: Date.now()
      };

      const result = await trafficRouter.routePacket(packet);
      // Should still return true as packet is queued, but may be dropped during processing
      expect(typeof result).toBe('boolean');
    });

    it('should update statistics when routing packets', async () => {
      const initialStats = trafficRouter.getStats();
      
      const packet: NetworkPacket = {
        id: 'packet-3',
        protocol: 'tcp',
        sourceIP: '************',
        destinationIP: 'minecraft.server.com',
        sourcePort: 12345,
        destinationPort: 25565,
        data: Buffer.from('test packet data'),
        timestamp: Date.now(),
        gameId: 'minecraft',
        sessionId: 'test-session'
      };

      await trafficRouter.routePacket(packet);
      
      // Wait a bit for processing
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const updatedStats = trafficRouter.getStats();
      expect(updatedStats.totalPackets).toBeGreaterThan(initialStats.totalPackets);
    });
  });

  describe('Statistics', () => {
    it('should provide traffic statistics', () => {
      const stats = trafficRouter.getStats();
      
      expect(stats).toHaveProperty('totalPackets');
      expect(stats).toHaveProperty('routedPackets');
      expect(stats).toHaveProperty('droppedPackets');
      expect(stats).toHaveProperty('bytesTransferred');
      expect(stats).toHaveProperty('latencyMs');
      expect(stats).toHaveProperty('packetsPerSecond');
      expect(stats).toHaveProperty('lastUpdated');
      
      expect(typeof stats.totalPackets).toBe('number');
      expect(typeof stats.routedPackets).toBe('number');
      expect(typeof stats.droppedPackets).toBe('number');
      expect(typeof stats.bytesTransferred).toBe('number');
      expect(typeof stats.latencyMs).toBe('number');
      expect(typeof stats.packetsPerSecond).toBe('number');
      expect(typeof stats.lastUpdated).toBe('number');
    });

    it('should reset statistics when routing is restarted', () => {
      trafficRouter.startRouting();
      const initialStats = trafficRouter.getStats();
      
      trafficRouter.stopRouting();
      trafficRouter.startRouting();
      
      const resetStats = trafficRouter.getStats();
      // Stats should be maintained across start/stop cycles
      expect(resetStats.totalPackets).toBe(initialStats.totalPackets);
    });
  });

  describe('Routing Control', () => {
    it('should start routing', () => {
      trafficRouter.startRouting();
      expect(trafficRouter.isRoutingState()).toBe(true);
    });

    it('should stop routing', () => {
      trafficRouter.startRouting();
      trafficRouter.stopRouting();
      expect(trafficRouter.isRoutingState()).toBe(false);
    });

    it('should handle multiple start calls gracefully', () => {
      trafficRouter.startRouting();
      trafficRouter.startRouting();
      expect(trafficRouter.isRoutingState()).toBe(true);
    });

    it('should handle multiple stop calls gracefully', () => {
      trafficRouter.startRouting();
      trafficRouter.stopRouting();
      trafficRouter.stopRouting();
      expect(trafficRouter.isRoutingState()).toBe(false);
    });
  });

  describe('Event Emission', () => {
    it('should emit routing table created event', (done) => {
      trafficRouter.on('routingTableCreated', (event) => {
        expect(event).toHaveProperty('gameId');
        expect(event).toHaveProperty('sessionId');
        done();
      });

      trafficRouter.createRoutingTable('test-game', 'test-session');
    });

    it('should emit device added event', (done) => {
      trafficRouter.on('deviceAdded', (event) => {
        expect(event).toHaveProperty('sessionId');
        expect(event).toHaveProperty('deviceId');
        expect(event).toHaveProperty('ipAddress');
        done();
      });

      trafficRouter.createRoutingTable('test-game', 'test-session')
        .then(() => trafficRouter.addDeviceToSession('test-session', 'device-1', '*************'));
    });

    it('should emit rule added event', (done) => {
      trafficRouter.on('ruleAdded', (rule) => {
        expect(rule).toHaveProperty('id');
        expect(rule).toHaveProperty('gameId');
        done();
      });

      const rule: TrafficRule = {
        id: 'test-rule',
        gameId: 'test-game',
        protocol: 'tcp',
        hostPattern: '.*\\.test\\.com',
        action: 'redirect',
        priority: 10,
        enabled: true
      };

      trafficRouter.addTrafficRule(rule);
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      // Create a new instance to test initialization failure
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // This should not throw but log error
      expect(() => trafficRouter.isInitializedState()).not.toThrow();
      
      consoleSpy.mockRestore();
    });

    it('should handle packet processing errors gracefully', async () => {
      trafficRouter.startRouting();
      
      // Create malformed packet
      const malformedPacket = {
        id: 'malformed',
        protocol: 'invalid',
        sourceIP: 'invalid-ip',
        destinationIP: 'invalid-dest',
        sourcePort: -1,
        destinationPort: -1,
        data: null as any,
        timestamp: Date.now()
      } as NetworkPacket;

      // Should not throw error
      await expect(trafficRouter.routePacket(malformedPacket)).resolves.toBeDefined();
    });
  });
});
