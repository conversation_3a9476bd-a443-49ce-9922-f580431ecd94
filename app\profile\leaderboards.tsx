import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  FlatList,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import LeaderboardManager, { Leaderboard, LeaderboardEntry } from '../../services/leaderboards/LeaderboardManager';

const { width } = Dimensions.get('window');

export default function LeaderboardsScreen() {
  const userProfile = useSelector((state: RootState) => state.user.profile);
  
  const [leaderboards, setLeaderboards] = useState<Leaderboard[]>([]);
  const [selectedLeaderboard, setSelectedLeaderboard] = useState<Leaderboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPlayerRank, setCurrentPlayerRank] = useState<number>(0);

  useEffect(() => {
    loadLeaderboards();
  }, []);

  const loadLeaderboards = async () => {
    try {
      setLoading(true);

      if (userProfile?.id) {
        await LeaderboardManager.initialize(userProfile.id);
        
        const allLeaderboards = LeaderboardManager.getActiveLeaderboards();
        setLeaderboards(allLeaderboards);
        
        if (allLeaderboards.length > 0) {
          setSelectedLeaderboard(allLeaderboards[0]);
          const rank = LeaderboardManager.getPlayerRank(userProfile.id, allLeaderboards[0].id);
          setCurrentPlayerRank(rank);
        }
      }
    } catch (error) {
      console.error('Failed to load leaderboards:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLeaderboardSelect = (leaderboard: Leaderboard) => {
    setSelectedLeaderboard(leaderboard);
    if (userProfile?.id) {
      const rank = LeaderboardManager.getPlayerRank(userProfile.id, leaderboard.id);
      setCurrentPlayerRank(rank);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return 'trophy';
      case 2: return 'medal';
      case 3: return 'ribbon';
      default: return 'person';
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return '#FFD700';
      case 2: return '#C0C0C0';
      case 3: return '#CD7F32';
      default: return '#CCCCCC';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return 'trending-up';
      case 'down': return 'trending-down';
      default: return 'remove';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return '#00FF88';
      case 'down': return '#FF4444';
      default: return '#CCCCCC';
    }
  };

  const formatScore = (score: number, category: string) => {
    switch (category) {
      case 'playtime':
        const hours = Math.floor(score / 60);
        const minutes = score % 60;
        return `${hours}h ${minutes}m`;
      case 'wins':
        return `${score} wins`;
      case 'achievements':
        return `${score} achievements`;
      default:
        return score.toLocaleString();
    }
  };

  const LeaderboardTab = ({ leaderboard }: { leaderboard: Leaderboard }) => (
    <TouchableOpacity
      style={[
        styles.leaderboardTab,
        selectedLeaderboard?.id === leaderboard.id && styles.leaderboardTabActive
      ]}
      onPress={() => handleLeaderboardSelect(leaderboard)}
    >
      <BlurView intensity={15} style={styles.tabBlur}>
        <Text style={[
          styles.tabText,
          selectedLeaderboard?.id === leaderboard.id && styles.tabTextActive
        ]}>
          {leaderboard.name}
        </Text>
        <Text style={styles.tabSubtext}>
          {leaderboard.totalPlayers} players
        </Text>
      </BlurView>
    </TouchableOpacity>
  );

  const LeaderboardEntry = ({ entry, index }: { entry: LeaderboardEntry; index: number }) => (
    <View style={[
      styles.entryCard,
      entry.playerId === userProfile?.id && styles.currentPlayerCard
    ]}>
      <BlurView intensity={15} style={styles.entryBlur}>
        <View style={styles.entryContent}>
          {/* Rank */}
          <View style={styles.rankContainer}>
            <View style={[
              styles.rankBadge,
              { backgroundColor: getRankColor(entry.rank) }
            ]}>
              <Ionicons 
                name={getRankIcon(entry.rank) as any} 
                size={entry.rank <= 3 ? 20 : 16} 
                color={entry.rank <= 3 ? '#000' : '#FFF'} 
              />
            </View>
            <Text style={styles.rankText}>#{entry.rank}</Text>
          </View>

          {/* Player Info */}
          <View style={styles.playerInfo}>
            <View style={styles.playerHeader}>
              <Text style={[
                styles.playerName,
                entry.playerId === userProfile?.id && styles.currentPlayerName
              ]}>
                {entry.playerName}
              </Text>
              {entry.change !== 0 && (
                <View style={styles.trendContainer}>
                  <Ionicons 
                    name={getTrendIcon(entry.trend) as any} 
                    size={12} 
                    color={getTrendColor(entry.trend)} 
                  />
                  <Text style={[
                    styles.changeText,
                    { color: getTrendColor(entry.trend) }
                  ]}>
                    {Math.abs(entry.change)}
                  </Text>
                </View>
              )}
            </View>
            
            {/* Badges */}
            {entry.badges.length > 0 && (
              <View style={styles.badgesContainer}>
                {entry.badges.slice(0, 3).map((badge, badgeIndex) => (
                  <View key={badgeIndex} style={styles.badge}>
                    <Text style={styles.badgeText}>{badge}</Text>
                  </View>
                ))}
                {entry.badges.length > 3 && (
                  <Text style={styles.moreBadges}>+{entry.badges.length - 3}</Text>
                )}
              </View>
            )}
          </View>

          {/* Score */}
          <View style={styles.scoreContainer}>
            <Text style={styles.scoreValue}>
              {formatScore(entry.score, selectedLeaderboard?.category || 'overall')}
            </Text>
            <Text style={styles.scoreLabel}>
              {selectedLeaderboard?.category === 'overall' ? 'Points' : 
               selectedLeaderboard?.category.charAt(0).toUpperCase() + selectedLeaderboard?.category.slice(1)}
            </Text>
          </View>
        </View>
      </BlurView>
    </View>
  );

  const renderEntry = ({ item, index }: { item: LeaderboardEntry; index: number }) => (
    <LeaderboardEntry entry={item} index={index} />
  );

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Leaderboards</Text>
          {currentPlayerRank > 0 && selectedLeaderboard && (
            <BlurView intensity={15} style={styles.playerRankCard}>
              <Text style={styles.playerRankText}>Your Rank</Text>
              <Text style={styles.playerRankValue}>
                #{currentPlayerRank} of {selectedLeaderboard.totalPlayers}
              </Text>
            </BlurView>
          )}
        </View>

        {/* Leaderboard Tabs */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.tabsScroll}
          contentContainerStyle={styles.tabsContainer}
        >
          {leaderboards.map((leaderboard) => (
            <LeaderboardTab key={leaderboard.id} leaderboard={leaderboard} />
          ))}
        </ScrollView>

        {/* Selected Leaderboard Info */}
        {selectedLeaderboard && (
          <View style={styles.leaderboardInfo}>
            <BlurView intensity={15} style={styles.infoCard}>
              <View style={styles.infoHeader}>
                <Text style={styles.leaderboardName}>{selectedLeaderboard.name}</Text>
                <Text style={styles.leaderboardDescription}>
                  {selectedLeaderboard.description}
                </Text>
              </View>
              <View style={styles.infoStats}>
                <View style={styles.infoStat}>
                  <Text style={styles.infoStatValue}>{selectedLeaderboard.totalPlayers}</Text>
                  <Text style={styles.infoStatLabel}>Players</Text>
                </View>
                <View style={styles.infoStat}>
                  <Text style={styles.infoStatValue}>
                    {selectedLeaderboard.timeframe.replace('_', ' ').toUpperCase()}
                  </Text>
                  <Text style={styles.infoStatLabel}>Period</Text>
                </View>
                <View style={styles.infoStat}>
                  <Text style={styles.infoStatValue}>
                    {new Date(selectedLeaderboard.lastUpdated).toLocaleDateString()}
                  </Text>
                  <Text style={styles.infoStatLabel}>Updated</Text>
                </View>
              </View>
            </BlurView>
          </View>
        )}

        {/* Leaderboard Entries */}
        {selectedLeaderboard && (
          <FlatList
            data={selectedLeaderboard.entries}
            renderItem={renderEntry}
            keyExtractor={(item) => item.playerId}
            style={styles.entriesList}
            contentContainerStyle={styles.entriesContent}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Empty State */}
        {!loading && (!selectedLeaderboard || selectedLeaderboard.entries.length === 0) && (
          <View style={styles.emptyContainer}>
            <BlurView intensity={15} style={styles.emptyCard}>
              <Ionicons name="podium-outline" size={48} color="#666" />
              <Text style={styles.emptyText}>No rankings yet</Text>
              <Text style={styles.emptySubtext}>
                Play games to appear on the leaderboards!
              </Text>
            </BlurView>
          </View>
        )}
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 100,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  playerRankCard: {
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    overflow: 'hidden',
  },
  playerRankText: {
    fontSize: 12,
    color: '#CCCCCC',
  },
  playerRankValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#00D4FF',
    marginTop: 2,
  },
  tabsScroll: {
    maxHeight: 80,
  },
  tabsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  leaderboardTab: {
    marginRight: 12,
  },
  leaderboardTabActive: {
    transform: [{ scale: 1.05 }],
  },
  tabBlur: {
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    overflow: 'hidden',
    minWidth: 120,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#CCCCCC',
    textAlign: 'center',
  },
  tabTextActive: {
    color: '#00D4FF',
  },
  tabSubtext: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
    marginTop: 2,
  },
  leaderboardInfo: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  infoCard: {
    borderRadius: 16,
    padding: 16,
    overflow: 'hidden',
  },
  infoHeader: {
    marginBottom: 12,
  },
  leaderboardName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  leaderboardDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
  },
  infoStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  infoStat: {
    alignItems: 'center',
  },
  infoStatValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#00D4FF',
  },
  infoStatLabel: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 2,
  },
  entriesList: {
    flex: 1,
  },
  entriesContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  entryCard: {
    marginBottom: 12,
  },
  currentPlayerCard: {
    borderWidth: 1,
    borderColor: '#00D4FF',
    borderRadius: 16,
  },
  entryBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  entryContent: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  rankContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  rankBadge: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rankText: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 4,
  },
  playerInfo: {
    flex: 1,
  },
  playerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  playerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  currentPlayerName: {
    color: '#00D4FF',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 12,
    marginLeft: 2,
  },
  badgesContainer: {
    flexDirection: 'row',
    marginTop: 4,
    alignItems: 'center',
  },
  badge: {
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 4,
  },
  badgeText: {
    fontSize: 10,
    color: '#00D4FF',
  },
  moreBadges: {
    fontSize: 10,
    color: '#CCCCCC',
  },
  scoreContainer: {
    alignItems: 'flex-end',
  },
  scoreValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  scoreLabel: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  emptyCard: {
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    overflow: 'hidden',
  },
  emptyText: {
    fontSize: 18,
    color: '#FFFFFF',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 8,
    textAlign: 'center',
  },
});
