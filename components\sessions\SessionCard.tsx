import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { GameSession, SessionStatus, ConnectionStatus } from '../../services/sessions/SessionManager';

interface SessionCardProps {
  session: GameSession;
  onPress?: () => void;
  onJoin?: () => void;
  onLeave?: () => void;
  onStart?: () => void;
  onEnd?: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
  currentUserId?: string;
}

export default function SessionCard({
  session,
  onPress,
  onJoin,
  onLeave,
  onStart,
  onEnd,
  style,
  variant = 'default',
  showActions = true,
  currentUserId,
}: SessionCardProps) {
  const isHost = currentUserId === session.hostUserId;
  const isParticipant = currentUserId ? session.participants.has(currentUserId) : false;
  const canJoin = !isParticipant && session.status === SessionStatus.WAITING && 
                  session.participants.size < session.maxParticipants;
  const canStart = isHost && session.status === SessionStatus.WAITING && session.participants.size >= 2;

  const getStatusColor = (status: SessionStatus): string => {
    switch (status) {
      case SessionStatus.WAITING:
        return '#FFB800';
      case SessionStatus.STARTING:
        return '#00D4FF';
      case SessionStatus.ACTIVE:
        return '#00FF88';
      case SessionStatus.PAUSED:
        return '#FF8C00';
      case SessionStatus.COMPLETED:
        return '#9B59B6';
      case SessionStatus.CANCELLED:
        return '#FF4757';
      default:
        return '#CCCCCC';
    }
  };

  const getStatusText = (status: SessionStatus): string => {
    switch (status) {
      case SessionStatus.WAITING:
        return 'Waiting for players';
      case SessionStatus.STARTING:
        return 'Starting...';
      case SessionStatus.ACTIVE:
        return 'In progress';
      case SessionStatus.PAUSED:
        return 'Paused';
      case SessionStatus.COMPLETED:
        return 'Completed';
      case SessionStatus.CANCELLED:
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  const getConnectionStatusIcon = (status: ConnectionStatus): keyof typeof Ionicons.glyphMap => {
    switch (status) {
      case ConnectionStatus.CONNECTED:
        return 'checkmark-circle';
      case ConnectionStatus.CONNECTING:
        return 'time';
      case ConnectionStatus.RECONNECTING:
        return 'refresh-circle';
      case ConnectionStatus.DISCONNECTED:
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderParticipants = () => {
    const participants = Array.from(session.participants.values());
    const maxVisible = variant === 'compact' ? 3 : 5;
    const visibleParticipants = participants.slice(0, maxVisible);
    const remainingCount = participants.length - maxVisible;

    return (
      <View style={styles.participantsContainer}>
        <View style={styles.participantsList}>
          {visibleParticipants.map((participant) => (
            <View key={participant.userId} style={styles.participantItem}>
              <View style={[
                styles.participantAvatar,
                { borderColor: participant.isHost ? '#FFD700' : '#00D4FF' }
              ]}>
                <Text style={styles.participantInitial}>
                  {participant.username.charAt(0).toUpperCase()}
                </Text>
              </View>
              <Ionicons
                name={getConnectionStatusIcon(participant.connectionStatus)}
                size={12}
                color={participant.connectionStatus === ConnectionStatus.CONNECTED ? '#00FF88' : '#FF4757'}
                style={styles.connectionIcon}
              />
            </View>
          ))}
          {remainingCount > 0 && (
            <View style={[styles.participantAvatar, styles.remainingCount]}>
              <Text style={styles.remainingText}>+{remainingCount}</Text>
            </View>
          )}
        </View>
        <Text style={styles.participantCount}>
          {session.participants.size}/{session.maxParticipants}
        </Text>
      </View>
    );
  };

  const renderActions = () => {
    if (!showActions) return null;

    return (
      <View style={styles.actionsContainer}>
        {canJoin && (
          <TouchableOpacity style={[styles.actionButton, styles.joinButton]} onPress={onJoin}>
            <Ionicons name="add" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Join</Text>
          </TouchableOpacity>
        )}
        
        {isParticipant && !isHost && (
          <TouchableOpacity style={[styles.actionButton, styles.leaveButton]} onPress={onLeave}>
            <Ionicons name="exit" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Leave</Text>
          </TouchableOpacity>
        )}
        
        {canStart && (
          <TouchableOpacity style={[styles.actionButton, styles.startButton]} onPress={onStart}>
            <Ionicons name="play" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Start</Text>
          </TouchableOpacity>
        )}
        
        {isHost && session.status === SessionStatus.ACTIVE && (
          <TouchableOpacity style={[styles.actionButton, styles.endButton]} onPress={onEnd}>
            <Ionicons name="stop" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>End</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderCompactView = () => (
    <TouchableOpacity style={[styles.container, styles.compactContainer, style]} onPress={onPress}>
      <BlurView intensity={20} style={styles.blurContainer}>
        <View style={styles.compactContent}>
          <View style={styles.gameInfo}>
            <Text style={styles.gameName} numberOfLines={1}>{session.gameName}</Text>
            <View style={styles.statusRow}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(session.status) }]} />
              <Text style={styles.statusText}>{getStatusText(session.status)}</Text>
            </View>
          </View>
          
          <View style={styles.compactParticipants}>
            {renderParticipants()}
          </View>
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  const renderDetailedView = () => (
    <TouchableOpacity style={[styles.container, styles.detailedContainer, style]} onPress={onPress}>
      <BlurView intensity={20} style={styles.blurContainer}>
        <View style={styles.detailedContent}>
          <View style={styles.header}>
            <View style={styles.gameInfo}>
              <Text style={styles.gameName}>{session.gameName}</Text>
              <Text style={styles.gameMode}>
                {session.metadata.gameMode || 'Standard'} • {session.metadata.difficulty || 'Normal'}
              </Text>
            </View>
            
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(session.status) }]} />
              <Text style={styles.statusText}>{getStatusText(session.status)}</Text>
            </View>
          </View>
          
          <View style={styles.sessionInfo}>
            <View style={styles.infoItem}>
              <Ionicons name="time" size={16} color="rgba(255, 255, 255, 0.7)" />
              <Text style={styles.infoText}>
                Created {formatTimestamp(session.timestamps.created)}
              </Text>
            </View>
            
            {session.duration > 0 && (
              <View style={styles.infoItem}>
                <Ionicons name="timer" size={16} color="rgba(255, 255, 255, 0.7)" />
                <Text style={styles.infoText}>
                  Duration: {formatDuration(session.duration)}
                </Text>
              </View>
            )}
            
            <View style={styles.infoItem}>
              <Ionicons name="wifi" size={16} color="rgba(255, 255, 255, 0.7)" />
              <Text style={styles.infoText}>
                {session.networkConfig.protocol.toUpperCase()}
              </Text>
            </View>
          </View>
          
          {renderParticipants()}
          {renderActions()}
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  const renderDefaultView = () => (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress}>
      <BlurView intensity={20} style={styles.blurContainer}>
        <View style={styles.content}>
          <View style={styles.header}>
            <View style={styles.gameInfo}>
              <Text style={styles.gameName}>{session.gameName}</Text>
              {session.metadata.gameMode && (
                <Text style={styles.gameMode}>{session.metadata.gameMode}</Text>
              )}
            </View>
            
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(session.status) }]} />
              <Text style={styles.statusText}>{getStatusText(session.status)}</Text>
            </View>
          </View>
          
          {renderParticipants()}
          {renderActions()}
        </View>
      </BlurView>
    </TouchableOpacity>
  );

  switch (variant) {
    case 'compact':
      return renderCompactView();
    case 'detailed':
      return renderDetailedView();
    default:
      return renderDefaultView();
  }
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginVertical: 4,
  },
  compactContainer: {
    height: 80,
  },
  detailedContainer: {
    minHeight: 160,
  },
  blurContainer: {
    flex: 1,
    padding: 16,
  },
  content: {
    flex: 1,
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailedContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  gameInfo: {
    flex: 1,
    marginRight: 12,
  },
  gameName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  gameMode: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  sessionInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  infoText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  participantsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  compactParticipants: {
    marginLeft: 12,
  },
  participantsList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantItem: {
    position: 'relative',
    marginRight: 8,
  },
  participantAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    borderWidth: 2,
    borderColor: '#00D4FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantInitial: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  connectionIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#1a1a2e',
    borderRadius: 8,
    padding: 1,
  },
  remainingCount: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  remainingText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  participantCount: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 4,
  },
  joinButton: {
    backgroundColor: '#00FF88',
  },
  leaveButton: {
    backgroundColor: '#FF4757',
  },
  startButton: {
    backgroundColor: '#00D4FF',
  },
  endButton: {
    backgroundColor: '#FF8C00',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
