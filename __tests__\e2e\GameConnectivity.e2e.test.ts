import { Platform } from 'react-native';
import GameDetector from '../../services/game/GameDetector';
import ConnectionManager from '../../services/networking/ConnectionManager';
import TrafficRouter from '../../services/networking/TrafficRouter';
import ProtocolEmulator from '../../services/networking/ProtocolEmulator';
import WebRTCManager from '../../services/networking/WebRTCManager';
import SQLiteManager from '../../services/database/SQLiteManager';
import NativeBridgeManager from '../../services/networking/native/NativeBridgeManager';

// Mock native modules for E2E testing
jest.mock('../../services/networking/native/NativeBridgeManager');
jest.mock('../../services/networking/BluetoothService');
jest.mock('../../services/networking/WiFiDirectService');

describe('End-to-End Game Connectivity Tests', () => {
  let gameDetector: typeof GameDetector;
  let connectionManager: typeof ConnectionManager;
  let trafficRouter: typeof TrafficRouter;
  let protocolEmulator: typeof ProtocolEmulator;
  let webrtcManager: typeof WebRTCManager;
  let sqliteManager: typeof SQLiteManager;

  beforeAll(async () => {
    // Initialize all services
    gameDetector = GameDetector;
    connectionManager = ConnectionManager;
    trafficRouter = TrafficRouter;
    protocolEmulator = ProtocolEmulator;
    webrtcManager = WebRTCManager;
    sqliteManager = SQLiteManager;

    // Initialize services in correct order
    await sqliteManager.initialize();
    await gameDetector.initialize();
    await connectionManager.initialize();
    await trafficRouter.initialize();
    await protocolEmulator.initialize();
    await webrtcManager.initialize('test-device-e2e');
  });

  afterAll(async () => {
    // Cleanup all services
    await webrtcManager.closeAllConnections();
    await connectionManager.cleanup();
    trafficRouter.stopRouting();
    await sqliteManager.clearAllData();
  });

  describe('Complete Game Session Workflow', () => {
    it('should complete full Minecraft session workflow', async () => {
      // 1. Detect Minecraft game
      const games = await gameDetector.getInstalledGames();
      const minecraft = games.find(game => game.id === 'minecraft');
      expect(minecraft).toBeDefined();

      // 2. Check game compatibility
      const compatibility = await gameDetector.checkGameCompatibility('minecraft');
      expect(compatibility.isCompatible).toBe(true);

      // 3. Start device discovery
      await connectionManager.startEnhancedDiscovery();
      expect(connectionManager.isDiscovering()).toBe(true);

      // 4. Create routing table for Minecraft
      await trafficRouter.createRoutingTable('minecraft', 'e2e-session-minecraft');
      const routingTables = trafficRouter.getRoutingTables();
      const minecraftTable = routingTables.find(t => t.gameId === 'minecraft');
      expect(minecraftTable).toBeDefined();

      // 5. Start traffic routing
      trafficRouter.startRouting();
      expect(trafficRouter.isRoutingState()).toBe(true);

      // 6. Process a Minecraft protocol request
      const minecraftRequest = {
        id: 'e2e-minecraft-req',
        gameId: 'minecraft',
        protocol: 'https' as const,
        method: 'POST',
        url: 'https://sessionserver.mojang.com/session/minecraft/join',
        headers: { 'Content-Type': 'application/json' },
        body: { accessToken: 'test-token' },
        timestamp: Date.now(),
        sourceDevice: 'test-device'
      };

      const response = await protocolEmulator.processRequest(minecraftRequest);
      expect(response.statusCode).toBe(200);
      expect(response.body).toHaveProperty('status', 'OK');

      // 7. Simulate packet routing
      const minecraftPacket = {
        id: 'e2e-minecraft-packet',
        protocol: 'tcp',
        sourceIP: '*************',
        destinationIP: 'minecraft.server.com',
        sourcePort: 12345,
        destinationPort: 25565,
        data: Buffer.from('Minecraft packet data'),
        timestamp: Date.now(),
        gameId: 'minecraft',
        sessionId: 'e2e-session-minecraft'
      };

      const routingResult = await trafficRouter.routePacket(minecraftPacket);
      expect(routingResult).toBe(true);

      // 8. Check statistics
      const stats = trafficRouter.getStats();
      expect(stats.totalPackets).toBeGreaterThan(0);

      // 9. Store session in database
      await sqliteManager.addSessionHistory({
        sessionId: 'e2e-session-minecraft',
        gameId: 'minecraft',
        hostDevice: 'test-device',
        participants: JSON.stringify(['test-device']),
        startTime: Date.now(),
        status: 'completed'
      });

      const sessionHistory = await sqliteManager.getSessionHistory(1);
      expect(sessionHistory.length).toBe(1);
      expect(sessionHistory[0].gameId).toBe('minecraft');
    }, 30000);

    it('should complete full Among Us session workflow', async () => {
      // 1. Detect Among Us game
      const games = await gameDetector.getInstalledGames();
      const amongUs = games.find(game => game.id === 'among_us');
      expect(amongUs).toBeDefined();

      // 2. Create WebRTC connection for real-time communication
      const connectionId = await webrtcManager.createConnection('peer-device');
      expect(connectionId).toBeDefined();

      // 3. Create routing table for Among Us
      await trafficRouter.createRoutingTable('among_us', 'e2e-session-amongus');
      await trafficRouter.addDeviceToSession('e2e-session-amongus', 'peer-device', '*************');

      // 4. Process Among Us UDP request
      const amongUsRequest = {
        id: 'e2e-amongus-req',
        gameId: 'among_us',
        protocol: 'udp' as const,
        method: 'POST',
        url: 'game_data',
        headers: {},
        body: { action: 'get_state' },
        timestamp: Date.now(),
        sourceDevice: 'test-device'
      };

      const response = await protocolEmulator.processRequest(amongUsRequest);
      expect(response.statusCode).toBe(200);
      expect(response.body).toHaveProperty('gameState');

      // 5. Send game data through WebRTC
      await webrtcManager.sendMessage(connectionId, {
        type: 'game_data',
        payload: { players: ['player1', 'player2'] },
        priority: 'high',
        reliable: true
      });

      // 6. Verify connection statistics
      const connectionStats = webrtcManager.getConnectionStats();
      expect(connectionStats.total).toBeGreaterThan(0);

      // 7. Store game compatibility data
      await sqliteManager.addGameCompatibility({
        gameId: 'among_us',
        gameName: 'Among Us',
        version: '2023.x',
        platform: Platform.OS,
        isCompatible: true,
        protocolSupport: ['udp', 'webrtc'],
        lastTested: Date.now(),
        notes: 'E2E test verified compatibility'
      });

      const compatibility = await sqliteManager.getGameCompatibility('among_us');
      expect(compatibility).toBeDefined();
      expect(compatibility?.isCompatible).toBe(true);
    }, 30000);
  });

  describe('Multi-Device Session Simulation', () => {
    it('should handle multiple devices in a session', async () => {
      const sessionId = 'e2e-multi-device-session';
      const gameId = 'minecraft';

      // 1. Create routing table
      await trafficRouter.createRoutingTable(gameId, sessionId);

      // 2. Add multiple devices
      const devices = [
        { id: 'device-1', ip: '*************' },
        { id: 'device-2', ip: '*************' },
        { id: 'device-3', ip: '*************' }
      ];

      for (const device of devices) {
        await trafficRouter.addDeviceToSession(sessionId, device.id, device.ip);
      }

      // 3. Verify all devices are in the session
      const routingTables = trafficRouter.getRoutingTables();
      const sessionTable = routingTables.find(t => t.sessionId === sessionId);
      expect(sessionTable?.devices.size).toBe(devices.length);

      // 4. Create WebRTC connections for each device
      const connectionIds = [];
      for (const device of devices) {
        const connectionId = await webrtcManager.createConnection(device.id);
        connectionIds.push(connectionId);
      }

      expect(connectionIds.length).toBe(devices.length);

      // 5. Broadcast message to all devices
      await webrtcManager.broadcastMessage({
        type: 'sync',
        payload: { gameState: 'lobby', players: devices.length },
        priority: 'normal',
        reliable: true
      });

      // 6. Simulate packet routing for each device
      for (let i = 0; i < devices.length; i++) {
        const packet = {
          id: `multi-packet-${i}`,
          protocol: 'tcp',
          sourceIP: devices[i].ip,
          destinationIP: 'game.server.com',
          sourcePort: 12345 + i,
          destinationPort: 25565,
          data: Buffer.from(`Packet from ${devices[i].id}`),
          timestamp: Date.now(),
          gameId,
          sessionId
        };

        const result = await trafficRouter.routePacket(packet);
        expect(result).toBe(true);
      }

      // 7. Verify statistics reflect multiple devices
      const stats = trafficRouter.getStats();
      expect(stats.totalPackets).toBeGreaterThanOrEqual(devices.length);

      // 8. Store session with multiple participants
      await sqliteManager.addSessionHistory({
        sessionId,
        gameId,
        hostDevice: 'device-1',
        participants: JSON.stringify(devices.map(d => d.id)),
        startTime: Date.now(),
        status: 'completed'
      });

      const sessionHistory = await sqliteManager.getSessionHistory(1);
      const multiDeviceSession = sessionHistory.find(s => s.sessionId === sessionId);
      expect(multiDeviceSession).toBeDefined();
      
      const participants = JSON.parse(multiDeviceSession!.participants);
      expect(participants.length).toBe(devices.length);
    }, 45000);
  });

  describe('Error Recovery and Resilience', () => {
    it('should handle service failures gracefully', async () => {
      const sessionId = 'e2e-error-recovery-session';
      const gameId = 'minecraft';

      // 1. Start normal operation
      await trafficRouter.createRoutingTable(gameId, sessionId);
      trafficRouter.startRouting();

      // 2. Simulate traffic router failure
      trafficRouter.stopRouting();
      
      // 3. Attempt packet routing (should handle gracefully)
      const packet = {
        id: 'error-recovery-packet',
        protocol: 'tcp',
        sourceIP: '*************',
        destinationIP: 'minecraft.server.com',
        sourcePort: 12345,
        destinationPort: 25565,
        data: Buffer.from('Error recovery test'),
        timestamp: Date.now(),
        gameId,
        sessionId
      };

      // Should not throw error even when routing is stopped
      await expect(trafficRouter.routePacket(packet)).resolves.toBeDefined();

      // 4. Restart routing
      trafficRouter.startRouting();
      
      // 5. Verify normal operation resumes
      const result = await trafficRouter.routePacket(packet);
      expect(result).toBe(true);

      // 6. Test protocol emulator error handling
      const invalidRequest = {
        id: 'invalid-request',
        gameId: 'non-existent-game',
        protocol: 'invalid' as any,
        method: 'INVALID',
        url: 'not-a-url',
        headers: {},
        timestamp: Date.now(),
        sourceDevice: 'test-device'
      };

      const response = await protocolEmulator.processRequest(invalidRequest);
      expect(response.statusCode).toBe(500);
      expect(response.body).toHaveProperty('error');

      // 7. Test WebRTC connection failure handling
      await expect(webrtcManager.closeConnection('non-existent-connection')).resolves.not.toThrow();
    }, 30000);

    it('should maintain data consistency during failures', async () => {
      // 1. Create initial data
      await sqliteManager.addGameCompatibility({
        gameId: 'test-consistency-game',
        gameName: 'Test Game',
        version: '1.0.0',
        platform: Platform.OS,
        isCompatible: true,
        protocolSupport: ['tcp'],
        lastTested: Date.now()
      });

      // 2. Verify data exists
      const initialData = await sqliteManager.getGameCompatibility('test-consistency-game');
      expect(initialData).toBeDefined();

      // 3. Simulate database operation during stress
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          sqliteManager.addSessionHistory({
            sessionId: `consistency-session-${i}`,
            gameId: 'test-consistency-game',
            hostDevice: 'test-device',
            participants: JSON.stringify([`device-${i}`]),
            startTime: Date.now(),
            status: 'completed'
          })
        );
      }

      await Promise.all(promises);

      // 4. Verify all data was stored correctly
      const sessionHistory = await sqliteManager.getSessionHistory(20);
      const consistencySessions = sessionHistory.filter(s => 
        s.sessionId.startsWith('consistency-session-')
      );
      expect(consistencySessions.length).toBe(10);

      // 5. Verify original data is still intact
      const finalData = await sqliteManager.getGameCompatibility('test-consistency-game');
      expect(finalData).toEqual(initialData);
    }, 30000);
  });

  describe('Performance and Scalability', () => {
    it('should handle high-volume packet processing', async () => {
      const sessionId = 'e2e-performance-session';
      const gameId = 'minecraft';

      await trafficRouter.createRoutingTable(gameId, sessionId);
      trafficRouter.startRouting();

      const startTime = Date.now();
      const packetCount = 100;
      const promises = [];

      // Generate many packets simultaneously
      for (let i = 0; i < packetCount; i++) {
        const packet = {
          id: `perf-packet-${i}`,
          protocol: 'tcp',
          sourceIP: '*************',
          destinationIP: 'minecraft.server.com',
          sourcePort: 12345 + i,
          destinationPort: 25565,
          data: Buffer.from(`Performance test packet ${i}`),
          timestamp: Date.now(),
          gameId,
          sessionId
        };

        promises.push(trafficRouter.routePacket(packet));
      }

      await Promise.all(promises);
      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should process 100 packets in reasonable time (< 5 seconds)
      expect(processingTime).toBeLessThan(5000);

      // Verify statistics
      const stats = trafficRouter.getStats();
      expect(stats.totalPackets).toBeGreaterThanOrEqual(packetCount);

      console.log(`Processed ${packetCount} packets in ${processingTime}ms`);
    }, 30000);

    it('should handle concurrent WebRTC connections', async () => {
      const connectionCount = 5;
      const connectionIds = [];

      // Create multiple connections
      for (let i = 0; i < connectionCount; i++) {
        const connectionId = await webrtcManager.createConnection(`perf-device-${i}`);
        connectionIds.push(connectionId);
      }

      expect(connectionIds.length).toBe(connectionCount);

      // Send messages through all connections
      const messagePromises = [];
      for (const connectionId of connectionIds) {
        messagePromises.push(
          webrtcManager.sendMessage(connectionId, {
            type: 'custom',
            payload: { test: 'concurrent message' },
            priority: 'normal',
            reliable: true
          })
        );
      }

      await Promise.all(messagePromises);

      // Verify connection statistics
      const stats = webrtcManager.getConnectionStats();
      expect(stats.total).toBeGreaterThanOrEqual(connectionCount);

      // Cleanup connections
      for (const connectionId of connectionIds) {
        await webrtcManager.closeConnection(connectionId);
      }
    }, 30000);
  });
});
