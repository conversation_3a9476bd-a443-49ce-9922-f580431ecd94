import { Platform } from 'react-native';
import { EventEmitter } from 'events';

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  dataChannelOptions: RTCDataChannelInit;
  connectionTimeout: number;
  maxRetries: number;
  enableLogging: boolean;
}

export interface DataChannelMessage {
  id: string;
  type: 'game_data' | 'control' | 'sync' | 'heartbeat' | 'custom';
  payload: any;
  timestamp: number;
  priority: 'low' | 'normal' | 'high' | 'critical';
  reliable: boolean;
}

export interface PeerConnection {
  id: string;
  deviceId: string;
  connection: RTCPeerConnection;
  dataChannel: RTCDataChannel | null;
  state: 'connecting' | 'connected' | 'disconnected' | 'failed';
  lastActivity: number;
  stats: {
    bytesSent: number;
    bytesReceived: number;
    messagesSent: number;
    messagesReceived: number;
    latency: number;
  };
}

export interface ConnectionOffer {
  id: string;
  fromDevice: string;
  toDevice: string;
  offer: RTCSessionDescriptionInit;
  timestamp: number;
}

export interface ConnectionAnswer {
  id: string;
  offerId: string;
  fromDevice: string;
  answer: RTCSessionDescriptionInit;
  timestamp: number;
}

export interface ICECandidate {
  id: string;
  connectionId: string;
  candidate: RTCIceCandidateInit;
  timestamp: number;
}

class WebRTCManager extends EventEmitter {
  private static instance: WebRTCManager;
  private config: WebRTCConfig;
  private isInitialized: boolean = false;
  private localDeviceId: string = '';
  private peerConnections: Map<string, PeerConnection> = new Map();
  private pendingOffers: Map<string, ConnectionOffer> = new Map();
  private messageQueue: Map<string, DataChannelMessage[]> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ],
      dataChannelOptions: {
        ordered: false,
        maxRetransmits: 3,
        maxPacketLifeTime: 3000
      },
      connectionTimeout: 30000,
      maxRetries: 3,
      enableLogging: true
    };
  }

  static getInstance(): WebRTCManager {
    if (!WebRTCManager.instance) {
      WebRTCManager.instance = new WebRTCManager();
    }
    return WebRTCManager.instance;
  }

  // Initialization
  async initialize(deviceId: string, config?: Partial<WebRTCConfig>): Promise<void> {
    try {
      console.log('Initializing WebRTC Manager...');

      this.localDeviceId = deviceId;
      
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Check WebRTC support
      await this.checkWebRTCSupport();

      // Start heartbeat monitoring
      this.startHeartbeat();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('WebRTC Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize WebRTC Manager:', error);
      throw error;
    }
  }

  private async checkWebRTCSupport(): Promise<void> {
    if (Platform.OS === 'web') {
      if (!window.RTCPeerConnection) {
        throw new Error('WebRTC not supported in this browser');
      }
    } else {
      // React Native WebRTC support check
      try {
        // This would require react-native-webrtc library
        console.log('Checking React Native WebRTC support...');
      } catch (error) {
        console.warn('React Native WebRTC not available, using fallback');
      }
    }
  }

  // Connection Management
  async createConnection(targetDeviceId: string): Promise<string> {
    try {
      const connectionId = this.generateConnectionId();
      
      // Create RTCPeerConnection
      const peerConnection = new RTCPeerConnection({
        iceServers: this.config.iceServers
      });

      // Setup event handlers
      this.setupPeerConnectionHandlers(peerConnection, connectionId, targetDeviceId);

      // Create data channel
      const dataChannel = peerConnection.createDataChannel('gameData', this.config.dataChannelOptions);
      this.setupDataChannelHandlers(dataChannel, connectionId);

      // Store connection
      const connection: PeerConnection = {
        id: connectionId,
        deviceId: targetDeviceId,
        connection: peerConnection,
        dataChannel,
        state: 'connecting',
        lastActivity: Date.now(),
        stats: {
          bytesSent: 0,
          bytesReceived: 0,
          messagesSent: 0,
          messagesReceived: 0,
          latency: 0
        }
      };

      this.peerConnections.set(connectionId, connection);
      this.messageQueue.set(connectionId, []);

      // Create and send offer
      const offer = await peerConnection.createOffer();
      await peerConnection.setLocalDescription(offer);

      const connectionOffer: ConnectionOffer = {
        id: this.generateOfferId(),
        fromDevice: this.localDeviceId,
        toDevice: targetDeviceId,
        offer,
        timestamp: Date.now()
      };

      this.pendingOffers.set(connectionOffer.id, connectionOffer);
      this.emit('offerCreated', connectionOffer);

      console.log(`WebRTC connection offer created for device ${targetDeviceId}`);
      return connectionId;
    } catch (error) {
      console.error('Failed to create WebRTC connection:', error);
      throw error;
    }
  }

  async acceptConnection(offer: ConnectionOffer): Promise<string> {
    try {
      const connectionId = this.generateConnectionId();

      // Create RTCPeerConnection
      const peerConnection = new RTCPeerConnection({
        iceServers: this.config.iceServers
      });

      // Setup event handlers
      this.setupPeerConnectionHandlers(peerConnection, connectionId, offer.fromDevice);

      // Set remote description
      await peerConnection.setRemoteDescription(offer.offer);

      // Create answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Store connection
      const connection: PeerConnection = {
        id: connectionId,
        deviceId: offer.fromDevice,
        connection: peerConnection,
        dataChannel: null, // Will be set when data channel is received
        state: 'connecting',
        lastActivity: Date.now(),
        stats: {
          bytesSent: 0,
          bytesReceived: 0,
          messagesSent: 0,
          messagesReceived: 0,
          latency: 0
        }
      };

      this.peerConnections.set(connectionId, connection);
      this.messageQueue.set(connectionId, []);

      // Send answer
      const connectionAnswer: ConnectionAnswer = {
        id: this.generateAnswerId(),
        offerId: offer.id,
        fromDevice: this.localDeviceId,
        answer,
        timestamp: Date.now()
      };

      this.emit('answerCreated', connectionAnswer);

      console.log(`WebRTC connection answer created for device ${offer.fromDevice}`);
      return connectionId;
    } catch (error) {
      console.error('Failed to accept WebRTC connection:', error);
      throw error;
    }
  }

  async handleAnswer(answer: ConnectionAnswer): Promise<void> {
    try {
      // Find the connection by offer ID
      const offer = this.pendingOffers.get(answer.offerId);
      if (!offer) {
        throw new Error(`Offer not found for answer ${answer.id}`);
      }

      // Find the peer connection
      const connection = Array.from(this.peerConnections.values())
        .find(conn => conn.deviceId === answer.fromDevice);

      if (!connection) {
        throw new Error(`Connection not found for device ${answer.fromDevice}`);
      }

      // Set remote description
      await connection.connection.setRemoteDescription(answer.answer);

      // Clean up pending offer
      this.pendingOffers.delete(answer.offerId);

      console.log(`WebRTC connection answer handled for device ${answer.fromDevice}`);
    } catch (error) {
      console.error('Failed to handle WebRTC answer:', error);
      throw error;
    }
  }

  async addICECandidate(iceCandidate: ICECandidate): Promise<void> {
    try {
      const connection = this.peerConnections.get(iceCandidate.connectionId);
      if (!connection) {
        throw new Error(`Connection not found: ${iceCandidate.connectionId}`);
      }

      await connection.connection.addIceCandidate(iceCandidate.candidate);
      console.log(`ICE candidate added for connection ${iceCandidate.connectionId}`);
    } catch (error) {
      console.error('Failed to add ICE candidate:', error);
    }
  }

  // Data Channel Communication
  async sendMessage(connectionId: string, message: Omit<DataChannelMessage, 'id' | 'timestamp'>): Promise<void> {
    try {
      const connection = this.peerConnections.get(connectionId);
      if (!connection || !connection.dataChannel) {
        throw new Error(`Data channel not available for connection ${connectionId}`);
      }

      if (connection.dataChannel.readyState !== 'open') {
        // Queue message for later delivery
        const fullMessage: DataChannelMessage = {
          id: this.generateMessageId(),
          timestamp: Date.now(),
          ...message
        };
        
        const queue = this.messageQueue.get(connectionId) || [];
        queue.push(fullMessage);
        this.messageQueue.set(connectionId, queue);
        
        console.log(`Message queued for connection ${connectionId}`);
        return;
      }

      const fullMessage: DataChannelMessage = {
        id: this.generateMessageId(),
        timestamp: Date.now(),
        ...message
      };

      const messageData = JSON.stringify(fullMessage);
      connection.dataChannel.send(messageData);

      // Update statistics
      connection.stats.messagesSent++;
      connection.stats.bytesSent += messageData.length;
      connection.lastActivity = Date.now();

      this.emit('messageSent', { connectionId, message: fullMessage });
      console.log(`Message sent to connection ${connectionId}: ${message.type}`);
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  async broadcastMessage(message: Omit<DataChannelMessage, 'id' | 'timestamp'>): Promise<void> {
    const promises = Array.from(this.peerConnections.keys()).map(connectionId =>
      this.sendMessage(connectionId, message)
    );

    await Promise.allSettled(promises);
    console.log(`Message broadcasted to ${promises.length} connections`);
  }

  // Event Handlers
  private setupPeerConnectionHandlers(
    peerConnection: RTCPeerConnection,
    connectionId: string,
    deviceId: string
  ): void {
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        const iceCandidate: ICECandidate = {
          id: this.generateICEId(),
          connectionId,
          candidate: event.candidate.toJSON(),
          timestamp: Date.now()
        };
        
        this.emit('iceCandidateGenerated', iceCandidate);
      }
    };

    peerConnection.onconnectionstatechange = () => {
      const connection = this.peerConnections.get(connectionId);
      if (connection) {
        const state = peerConnection.connectionState;
        connection.state = state as any;
        
        this.emit('connectionStateChanged', { connectionId, state });
        console.log(`Connection ${connectionId} state changed to: ${state}`);
        
        if (state === 'connected') {
          this.processQueuedMessages(connectionId);
        } else if (state === 'failed' || state === 'disconnected') {
          this.handleConnectionFailure(connectionId);
        }
      }
    };

    peerConnection.ondatachannel = (event) => {
      const dataChannel = event.channel;
      const connection = this.peerConnections.get(connectionId);
      
      if (connection) {
        connection.dataChannel = dataChannel;
        this.setupDataChannelHandlers(dataChannel, connectionId);
      }
    };
  }

  private setupDataChannelHandlers(dataChannel: RTCDataChannel, connectionId: string): void {
    dataChannel.onopen = () => {
      console.log(`Data channel opened for connection ${connectionId}`);
      this.processQueuedMessages(connectionId);
    };

    dataChannel.onmessage = (event) => {
      try {
        const message: DataChannelMessage = JSON.parse(event.data);
        const connection = this.peerConnections.get(connectionId);
        
        if (connection) {
          connection.stats.messagesReceived++;
          connection.stats.bytesReceived += event.data.length;
          connection.lastActivity = Date.now();
        }

        this.emit('messageReceived', { connectionId, message });
        console.log(`Message received from connection ${connectionId}: ${message.type}`);
      } catch (error) {
        console.error('Failed to parse received message:', error);
      }
    };

    dataChannel.onclose = () => {
      console.log(`Data channel closed for connection ${connectionId}`);
    };

    dataChannel.onerror = (error) => {
      console.error(`Data channel error for connection ${connectionId}:`, error);
    };
  }

  private processQueuedMessages(connectionId: string): void {
    const queue = this.messageQueue.get(connectionId);
    if (queue && queue.length > 0) {
      console.log(`Processing ${queue.length} queued messages for connection ${connectionId}`);
      
      queue.forEach(message => {
        this.sendMessage(connectionId, message).catch(error => {
          console.error('Failed to send queued message:', error);
        });
      });
      
      this.messageQueue.set(connectionId, []);
    }
  }

  private handleConnectionFailure(connectionId: string): void {
    const connection = this.peerConnections.get(connectionId);
    if (connection) {
      console.log(`Handling connection failure for ${connectionId}`);
      
      // Clean up resources
      if (connection.dataChannel) {
        connection.dataChannel.close();
      }
      connection.connection.close();
      
      // Remove from maps
      this.peerConnections.delete(connectionId);
      this.messageQueue.delete(connectionId);
      
      this.emit('connectionFailed', { connectionId, deviceId: connection.deviceId });
    }
  }

  // Heartbeat and Monitoring
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeats();
      this.checkConnectionHealth();
    }, 5000); // Every 5 seconds
  }

  private sendHeartbeats(): void {
    const heartbeatMessage: Omit<DataChannelMessage, 'id' | 'timestamp'> = {
      type: 'heartbeat',
      payload: { timestamp: Date.now() },
      priority: 'low',
      reliable: false
    };

    this.broadcastMessage(heartbeatMessage).catch(error => {
      console.error('Failed to send heartbeat:', error);
    });
  }

  private checkConnectionHealth(): void {
    const now = Date.now();
    const timeout = 30000; // 30 seconds

    for (const [connectionId, connection] of this.peerConnections) {
      if (now - connection.lastActivity > timeout) {
        console.warn(`Connection ${connectionId} appears inactive, closing...`);
        this.closeConnection(connectionId);
      }
    }
  }

  // Connection Management
  async closeConnection(connectionId: string): Promise<void> {
    try {
      const connection = this.peerConnections.get(connectionId);
      if (connection) {
        if (connection.dataChannel) {
          connection.dataChannel.close();
        }
        connection.connection.close();
        
        this.peerConnections.delete(connectionId);
        this.messageQueue.delete(connectionId);
        
        this.emit('connectionClosed', { connectionId, deviceId: connection.deviceId });
        console.log(`Connection ${connectionId} closed`);
      }
    } catch (error) {
      console.error('Failed to close connection:', error);
    }
  }

  async closeAllConnections(): Promise<void> {
    const connectionIds = Array.from(this.peerConnections.keys());
    const promises = connectionIds.map(id => this.closeConnection(id));
    
    await Promise.allSettled(promises);
    console.log('All WebRTC connections closed');
  }

  // Utility Methods
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateOfferId(): string {
    return `offer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAnswerId(): string {
    return `answer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateICEId(): string {
    return `ice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Getters
  getConnections(): PeerConnection[] {
    return Array.from(this.peerConnections.values());
  }

  getConnection(connectionId: string): PeerConnection | null {
    return this.peerConnections.get(connectionId) || null;
  }

  getConnectionStats(): { total: number; connected: number; connecting: number; failed: number } {
    const connections = Array.from(this.peerConnections.values());
    return {
      total: connections.length,
      connected: connections.filter(c => c.state === 'connected').length,
      connecting: connections.filter(c => c.state === 'connecting').length,
      failed: connections.filter(c => c.state === 'failed').length
    };
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }
}

export default WebRTCManager.getInstance();
