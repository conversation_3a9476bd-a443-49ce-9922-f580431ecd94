import { Platform } from 'react-native';
import { EventEmitter } from 'events';

export interface VoiceChatConfig {
  codec: 'opus' | 'aac' | 'pcm';
  sampleRate: 8000 | 16000 | 44100 | 48000;
  bitrate: number;
  channels: 1 | 2;
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
}

export interface AudioDevice {
  id: string;
  name: string;
  type: 'microphone' | 'speaker' | 'headset' | 'bluetooth';
  isDefault: boolean;
  isAvailable: boolean;
}

export interface VoiceChatParticipant {
  id: string;
  name: string;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  audioLevel: number;
  connectionQuality: 'excellent' | 'good' | 'fair' | 'poor';
  latency: number;
}

export interface VoiceChatRoom {
  id: string;
  name: string;
  participants: VoiceChatParticipant[];
  maxParticipants: number;
  isPrivate: boolean;
  requiresPushToTalk: boolean;
  createdAt: number;
}

class VoiceChatService extends EventEmitter {
  private static instance: VoiceChatService;
  private config: VoiceChatConfig;
  private isInitialized: boolean = false;
  private isRecording: boolean = false;
  private isPlaying: boolean = false;
  private currentRoom: VoiceChatRoom | null = null;
  private localParticipant: VoiceChatParticipant | null = null;
  private audioDevices: Map<string, AudioDevice> = new Map();
  private audioContext: any = null;
  private mediaStream: any = null;
  private peerConnections: Map<string, any> = new Map();

  private constructor() {
    super();
    this.config = {
      codec: 'opus',
      sampleRate: 48000,
      bitrate: 64000,
      channels: 1,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    };
  }

  static getInstance(): VoiceChatService {
    if (!VoiceChatService.instance) {
      VoiceChatService.instance = new VoiceChatService();
    }
    return VoiceChatService.instance;
  }

  // Initialization and Setup
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Voice Chat Service...');

      // Request audio permissions
      await this.requestAudioPermissions();

      // Initialize audio context
      await this.initializeAudioContext();

      // Discover audio devices
      await this.discoverAudioDevices();

      // Setup audio processing
      await this.setupAudioProcessing();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('Voice Chat Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Voice Chat Service:', error);
      throw error;
    }
  }

  private async requestAudioPermissions(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Web platform - request microphone permission
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach(track => track.stop()); // Stop immediately after permission
      } else {
        // React Native - would use react-native-permissions
        console.log('Audio permissions requested for mobile platform');
      }
    } catch (error) {
      console.error('Audio permission denied:', error);
      throw new Error('Microphone permission required for voice chat');
    }
  }

  private async initializeAudioContext(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Web Audio API
        const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
        this.audioContext = new AudioContext();
        
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
        }
      } else {
        // React Native - would use react-native-audio or similar
        console.log('Audio context initialized for mobile platform');
      }
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
      throw error;
    }
  }

  private async discoverAudioDevices(): Promise<void> {
    try {
      this.audioDevices.clear();

      if (Platform.OS === 'web') {
        const devices = await navigator.mediaDevices.enumerateDevices();
        
        devices.forEach((device, index) => {
          if (device.kind === 'audioinput' || device.kind === 'audiooutput') {
            const audioDevice: AudioDevice = {
              id: device.deviceId || `device_${index}`,
              name: device.label || `${device.kind} ${index + 1}`,
              type: this.detectDeviceType(device.label),
              isDefault: device.deviceId === 'default',
              isAvailable: true
            };
            
            this.audioDevices.set(audioDevice.id, audioDevice);
          }
        });
      } else {
        // Mock devices for mobile platforms
        const mockDevices: AudioDevice[] = [
          {
            id: 'builtin_mic',
            name: 'Built-in Microphone',
            type: 'microphone',
            isDefault: true,
            isAvailable: true
          },
          {
            id: 'builtin_speaker',
            name: 'Built-in Speaker',
            type: 'speaker',
            isDefault: true,
            isAvailable: true
          }
        ];

        mockDevices.forEach(device => {
          this.audioDevices.set(device.id, device);
        });
      }

      console.log(`Discovered ${this.audioDevices.size} audio devices`);
    } catch (error) {
      console.error('Failed to discover audio devices:', error);
    }
  }

  private detectDeviceType(label: string): AudioDevice['type'] {
    const lowerLabel = label.toLowerCase();
    if (lowerLabel.includes('bluetooth') || lowerLabel.includes('airpods')) {
      return 'bluetooth';
    }
    if (lowerLabel.includes('headset') || lowerLabel.includes('headphone')) {
      return 'headset';
    }
    if (lowerLabel.includes('microphone') || lowerLabel.includes('mic')) {
      return 'microphone';
    }
    return 'speaker';
  }

  private async setupAudioProcessing(): Promise<void> {
    try {
      // Setup audio processing pipeline
      console.log('Audio processing pipeline configured');
    } catch (error) {
      console.error('Failed to setup audio processing:', error);
    }
  }

  // Room Management
  async createVoiceChatRoom(
    name: string,
    options: {
      maxParticipants?: number;
      isPrivate?: boolean;
      requiresPushToTalk?: boolean;
    } = {}
  ): Promise<VoiceChatRoom> {
    try {
      if (!this.isInitialized) {
        throw new Error('Voice chat service not initialized');
      }

      const room: VoiceChatRoom = {
        id: this.generateRoomId(),
        name,
        participants: [],
        maxParticipants: options.maxParticipants || 8,
        isPrivate: options.isPrivate || false,
        requiresPushToTalk: options.requiresPushToTalk || false,
        createdAt: Date.now()
      };

      this.currentRoom = room;
      console.log(`Voice chat room created: ${room.name}`);
      
      return room;
    } catch (error) {
      console.error('Failed to create voice chat room:', error);
      throw error;
    }
  }

  async joinVoiceChatRoom(
    roomId: string,
    participantName: string
  ): Promise<VoiceChatParticipant> {
    try {
      if (!this.isInitialized) {
        throw new Error('Voice chat service not initialized');
      }

      // Create local participant
      this.localParticipant = {
        id: this.generateParticipantId(),
        name: participantName,
        isMuted: false,
        isDeafened: false,
        isSpeaking: false,
        audioLevel: 0,
        connectionQuality: 'good',
        latency: 0
      };

      // Start audio capture
      await this.startAudioCapture();

      // Setup peer connections for other participants
      await this.setupPeerConnections();

      this.emit('joinedRoom', { roomId, participant: this.localParticipant });
      console.log(`Joined voice chat room: ${roomId}`);
      
      return this.localParticipant;
    } catch (error) {
      console.error('Failed to join voice chat room:', error);
      throw error;
    }
  }

  async leaveVoiceChatRoom(): Promise<void> {
    try {
      if (this.currentRoom && this.localParticipant) {
        // Stop audio capture
        await this.stopAudioCapture();

        // Close peer connections
        this.closePeerConnections();

        // Clean up
        this.currentRoom = null;
        this.localParticipant = null;

        this.emit('leftRoom');
        console.log('Left voice chat room');
      }
    } catch (error) {
      console.error('Failed to leave voice chat room:', error);
      throw error;
    }
  }

  // Audio Control
  async startAudioCapture(): Promise<void> {
    try {
      if (this.isRecording) return;

      const constraints = {
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: this.config.echoCancellation,
          noiseSuppression: this.config.noiseSuppression,
          autoGainControl: this.config.autoGainControl
        }
      };

      if (Platform.OS === 'web') {
        this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
        this.setupAudioAnalysis();
      } else {
        // React Native implementation would go here
        console.log('Audio capture started on mobile platform');
      }

      this.isRecording = true;
      this.emit('audioStarted');
      console.log('Audio capture started');
    } catch (error) {
      console.error('Failed to start audio capture:', error);
      throw error;
    }
  }

  async stopAudioCapture(): Promise<void> {
    try {
      if (!this.isRecording) return;

      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach((track: any) => track.stop());
        this.mediaStream = null;
      }

      this.isRecording = false;
      this.emit('audioStopped');
      console.log('Audio capture stopped');
    } catch (error) {
      console.error('Failed to stop audio capture:', error);
    }
  }

  private setupAudioAnalysis(): void {
    if (!this.audioContext || !this.mediaStream) return;

    try {
      const source = this.audioContext.createMediaStreamSource(this.mediaStream);
      const analyser = this.audioContext.createAnalyser();
      
      analyser.fftSize = 256;
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      const updateAudioLevel = () => {
        if (!this.isRecording) return;

        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        const audioLevel = Math.round((average / 255) * 100);

        if (this.localParticipant) {
          this.localParticipant.audioLevel = audioLevel;
          this.localParticipant.isSpeaking = audioLevel > 10; // Threshold for speaking detection
          this.emit('audioLevelChanged', { participantId: this.localParticipant.id, level: audioLevel });
        }

        requestAnimationFrame(updateAudioLevel);
      };

      updateAudioLevel();
    } catch (error) {
      console.error('Failed to setup audio analysis:', error);
    }
  }

  // Participant Control
  async muteParticipant(participantId: string, muted: boolean): Promise<void> {
    try {
      if (this.localParticipant && this.localParticipant.id === participantId) {
        this.localParticipant.isMuted = muted;
        
        if (this.mediaStream) {
          this.mediaStream.getAudioTracks().forEach((track: any) => {
            track.enabled = !muted;
          });
        }

        this.emit('participantMuted', { participantId, muted });
        console.log(`Participant ${participantId} ${muted ? 'muted' : 'unmuted'}`);
      }
    } catch (error) {
      console.error('Failed to mute participant:', error);
    }
  }

  async deafenParticipant(participantId: string, deafened: boolean): Promise<void> {
    try {
      if (this.localParticipant && this.localParticipant.id === participantId) {
        this.localParticipant.isDeafened = deafened;
        this.emit('participantDeafened', { participantId, deafened });
        console.log(`Participant ${participantId} ${deafened ? 'deafened' : 'undeafened'}`);
      }
    } catch (error) {
      console.error('Failed to deafen participant:', error);
    }
  }

  // Utility Methods
  private setupPeerConnections(): Promise<void> {
    // WebRTC peer connection setup would go here
    return Promise.resolve();
  }

  private closePeerConnections(): void {
    this.peerConnections.forEach(connection => {
      connection.close();
    });
    this.peerConnections.clear();
  }

  private generateRoomId(): string {
    return `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateParticipantId(): string {
    return `participant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Getters
  getAudioDevices(): AudioDevice[] {
    return Array.from(this.audioDevices.values());
  }

  getCurrentRoom(): VoiceChatRoom | null {
    return this.currentRoom;
  }

  getLocalParticipant(): VoiceChatParticipant | null {
    return this.localParticipant;
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }

  isRecordingState(): boolean {
    return this.isRecording;
  }
}

export default VoiceChatService.getInstance();
