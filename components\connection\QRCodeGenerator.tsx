import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Share,
  Alert,
  Dimensions,
} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';

const { width } = Dimensions.get('window');

interface QRCodeGeneratorProps {
  deviceInfo: {
    id: string;
    name: string;
    type: 'ios' | 'android';
    connectionInfo: {
      ip?: string;
      port?: number;
      bluetoothId?: string;
    };
  };
  onClose?: () => void;
  title?: string;
  subtitle?: string;
  showControls?: boolean;
}

export default function QRCodeGenerator({
  deviceInfo,
  onClose,
  title = 'Share Connection',
  subtitle = 'Let others scan this QR code to connect',
  showControls = true,
}: QRCodeGeneratorProps) {
  const [qrData, setQrData] = useState<string>('');
  const [qrSize, setQrSize] = useState(200);

  useEffect(() => {
    generateQRData();
    calculateQRSize();
  }, [deviceInfo]);

  const generateQRData = () => {
    const connectionData = {
      type: 'logaco-connection',
      version: '1.0',
      deviceId: deviceInfo.id,
      deviceName: deviceInfo.name,
      deviceType: deviceInfo.type,
      connectionInfo: deviceInfo.connectionInfo,
      timestamp: Date.now(),
      // Add security token for validation
      token: generateSecurityToken(),
    };

    setQrData(JSON.stringify(connectionData));
  };

  const generateSecurityToken = () => {
    // Generate a simple security token (in production, use proper crypto)
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  };

  const calculateQRSize = () => {
    const maxSize = Math.min(width - 80, 250);
    setQrSize(maxSize);
  };

  const handleCopyToClipboard = async () => {
    try {
      await Clipboard.setStringAsync(qrData);
      Alert.alert('Copied!', 'Connection data copied to clipboard');
    } catch (error) {
      Alert.alert('Error', 'Failed to copy to clipboard');
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Join my LoGaCo session! Connection data: ${qrData}`,
        title: 'LoGaCo Connection',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share connection data');
    }
  };

  const handleRefresh = () => {
    generateQRData();
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />

      <BlurView intensity={15} style={styles.card}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.cardGradient}
        >
          {/* Header */}
          {onClose && (
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Ionicons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          )}

          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Ionicons name="qr-code" size={32} color="#00D4FF" />
            </View>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.subtitle}>{subtitle}</Text>
          </View>

          {/* QR Code */}
          <View style={styles.qrContainer}>
            <View style={styles.qrWrapper}>
              {qrData ? (
                <QRCode
                  value={qrData}
                  size={qrSize}
                  color="#1a1a2e"
                  backgroundColor="#FFFFFF"
                  logo={require('../../assets/icon.png')}
                  logoSize={qrSize * 0.15}
                  logoBackgroundColor="transparent"
                  logoMargin={2}
                  logoBorderRadius={8}
                />
              ) : (
                <View style={[styles.qrPlaceholder, { width: qrSize, height: qrSize }]}>
                  <Ionicons name="qr-code" size={qrSize * 0.3} color="#666" />
                  <Text style={styles.placeholderText}>Generating QR Code...</Text>
                </View>
              )}
            </View>
          </View>

          {/* Device Info */}
          <View style={styles.deviceInfo}>
            <View style={styles.deviceRow}>
              <Ionicons 
                name={deviceInfo.type === 'ios' ? 'phone-portrait' : 'phone-portrait'} 
                size={20} 
                color="#00D4FF" 
              />
              <Text style={styles.deviceText}>{deviceInfo.name}</Text>
            </View>
            <View style={styles.deviceRow}>
              <Ionicons name="finger-print" size={20} color="#00D4FF" />
              <Text style={styles.deviceText}>ID: {deviceInfo.id.substring(0, 8)}...</Text>
            </View>
            {deviceInfo.connectionInfo.ip && (
              <View style={styles.deviceRow}>
                <Ionicons name="globe" size={20} color="#00D4FF" />
                <Text style={styles.deviceText}>
                  {deviceInfo.connectionInfo.ip}:{deviceInfo.connectionInfo.port}
                </Text>
              </View>
            )}
          </View>

          {/* Controls */}
          {showControls && (
            <View style={styles.controls}>
              <TouchableOpacity style={styles.controlButton} onPress={handleCopyToClipboard}>
                <Ionicons name="copy" size={20} color="#00D4FF" />
                <Text style={styles.controlText}>Copy</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.controlButton} onPress={handleShare}>
                <Ionicons name="share" size={20} color="#00D4FF" />
                <Text style={styles.controlText}>Share</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.controlButton} onPress={handleRefresh}>
                <Ionicons name="refresh" size={20} color="#00D4FF" />
                <Text style={styles.controlText}>Refresh</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Instructions */}
          <View style={styles.instructions}>
            <Text style={styles.instructionTitle}>How to connect:</Text>
            <Text style={styles.instructionText}>
              1. Open LoGaCo on another device{'\n'}
              2. Tap "Scan QR Code" in the connection menu{'\n'}
              3. Point the camera at this QR code{'\n'}
              4. Wait for the connection to establish
            </Text>
          </View>
        </LinearGradient>
      </BlurView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  card: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 24,
    overflow: 'hidden',
  },
  cardGradient: {
    padding: 24,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 22,
  },
  qrContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  qrWrapper: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  qrPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  placeholderText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  deviceInfo: {
    marginBottom: 24,
    gap: 8,
  },
  deviceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  deviceText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    flex: 1,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  controlButton: {
    alignItems: 'center',
    gap: 4,
    padding: 8,
  },
  controlText: {
    fontSize: 12,
    color: '#00D4FF',
    fontWeight: '500',
  },
  instructions: {
    backgroundColor: 'rgba(0, 212, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 212, 255, 0.2)',
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#00D4FF',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: 20,
  },
});
