// Mock react-native-bluetooth-classic before importing
jest.mock(
  "react-native-bluetooth-classic",
  () => ({
    BluetoothModule: {
      isEnabled: jest.fn(),
      requestEnable: jest.fn(),
      getBondedDevices: jest.fn(),
      startDiscovery: jest.fn(),
      cancelDiscovery: jest.fn(),
      connectToDevice: jest.fn(),
      disconnectFromDevice: jest.fn(),
      writeToDevice: jest.fn(),
      onBluetoothEnabled: jest.fn(),
      onBluetoothDisabled: jest.fn(),
      onDeviceDiscovered: jest.fn(),
      onDeviceConnected: jest.fn(),
      onDeviceDisconnected: jest.fn(),
      onDataReceived: jest.fn(),
    },
  }),
  { virtual: true }
);

// Mock react-native PermissionsAndroid
jest.mock("react-native", () => ({
  Platform: {
    OS: "android",
    Version: 30,
  },
  PermissionsAndroid: {
    PERMISSIONS: {
      BLUETOOTH: "android.permission.BLUETOOTH",
      BLUETOOTH_ADMIN: "android.permission.BLUETOOTH_ADMIN",
      ACCESS_FINE_LOCATION: "android.permission.ACCESS_FINE_LOCATION",
      BLUETOOTH_SCAN: "android.permission.BLUETOOTH_SCAN",
      BLUETOOTH_CONNECT: "android.permission.BLUETOOTH_CONNECT",
      BLUETOOTH_ADVERTISE: "android.permission.BLUETOOTH_ADVERTISE",
    },
    RESULTS: {
      GRANTED: "granted",
      DENIED: "denied",
    },
    requestMultiple: jest.fn(),
  },
}));

import BluetoothService from "../../../services/networking/BluetoothService";

describe("BluetoothService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("isEnabled", () => {
    it("should return true when Bluetooth is enabled", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.isEnabled.mockResolvedValue(true);

      const result = await BluetoothService.isEnabled();
      expect(result).toBe(true);
      expect(BluetoothModule.isEnabled).toHaveBeenCalled();
    });

    it("should return false when Bluetooth is disabled", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.isEnabled.mockResolvedValue(false);

      const result = await BluetoothService.isEnabled();
      expect(result).toBe(false);
    });

    it("should handle errors gracefully", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.isEnabled.mockRejectedValue(new Error("Bluetooth error"));

      const result = await BluetoothService.isEnabled();
      expect(result).toBe(false);
    });
  });

  describe("requestEnable", () => {
    it("should request Bluetooth enable successfully", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.requestEnable.mockResolvedValue(true);

      const result = await BluetoothService.requestEnable();
      expect(result).toBe(true);
      expect(BluetoothModule.requestEnable).toHaveBeenCalled();
    });

    it("should handle enable request failure", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.requestEnable.mockResolvedValue(false);

      const result = await BluetoothService.requestEnable();
      expect(result).toBe(false);
    });
  });

  describe("requestPermissions", () => {
    it("should request all required permissions on Android", async () => {
      const { PermissionsAndroid } = require("react-native");
      PermissionsAndroid.requestMultiple.mockResolvedValue({
        "android.permission.BLUETOOTH": "granted",
        "android.permission.BLUETOOTH_ADMIN": "granted",
        "android.permission.ACCESS_FINE_LOCATION": "granted",
      });

      const result = await BluetoothService.requestPermissions();
      expect(result).toBe(true);
      expect(PermissionsAndroid.requestMultiple).toHaveBeenCalled();
    });

    it("should return false if any permission is denied", async () => {
      const { PermissionsAndroid } = require("react-native");
      PermissionsAndroid.requestMultiple.mockResolvedValue({
        "android.permission.BLUETOOTH": "granted",
        "android.permission.BLUETOOTH_ADMIN": "denied",
        "android.permission.ACCESS_FINE_LOCATION": "granted",
      });

      const result = await BluetoothService.requestPermissions();
      expect(result).toBe(false);
    });
  });

  describe("startDiscovery", () => {
    it("should start device discovery successfully", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.startDiscovery.mockResolvedValue(true);

      await BluetoothService.startDiscovery();
      expect(BluetoothModule.startDiscovery).toHaveBeenCalled();
    });

    it("should handle discovery start errors", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.startDiscovery.mockRejectedValue(
        new Error("Discovery failed")
      );

      await expect(BluetoothService.startDiscovery()).rejects.toThrow(
        "Discovery failed"
      );
    });
  });

  describe("stopDiscovery", () => {
    it("should stop device discovery successfully", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.cancelDiscovery.mockResolvedValue(true);

      // First start discovery to set the isDiscovering flag
      BluetoothModule.startDiscovery.mockResolvedValue(true);
      await BluetoothService.startDiscovery();

      // Now stop discovery
      await BluetoothService.stopDiscovery();
      expect(BluetoothModule.cancelDiscovery).toHaveBeenCalled();
    });
  });

  describe("getBondedDevices", () => {
    it("should return list of bonded devices", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      const mockDevices = [
        { id: "device1", name: "Device 1", address: "00:11:22:33:44:55" },
        { id: "device2", name: "Device 2", address: "00:11:22:33:44:66" },
      ];
      BluetoothModule.getBondedDevices.mockResolvedValue(mockDevices);

      const result = await BluetoothService.getBondedDevices();
      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: "device1",
        name: "Device 1",
        address: "00:11:22:33:44:55",
        paired: true,
      });
    });

    it("should handle errors when getting bonded devices", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.getBondedDevices.mockRejectedValue(
        new Error("Failed to get devices")
      );

      const result = await BluetoothService.getBondedDevices();
      expect(result).toEqual([]);
    });
  });

  describe("connectToDevice", () => {
    it("should connect to device successfully", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.connectToDevice.mockResolvedValue(true);

      const result = await BluetoothService.connectToDevice("device1");
      expect(result).toBe(true);
      expect(BluetoothModule.connectToDevice).toHaveBeenCalledWith("device1");
    });

    it("should handle connection failures", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.connectToDevice.mockResolvedValue(false);

      const result = await BluetoothService.connectToDevice("device1");
      expect(result).toBe(false);
    });
  });

  describe("sendData", () => {
    it("should send data to device successfully", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.writeToDevice.mockResolvedValue(true);

      const result = await BluetoothService.sendData("device1", "test data");
      expect(result).toBe(true);
      expect(BluetoothModule.writeToDevice).toHaveBeenCalledWith(
        "device1",
        "test data"
      );
    });

    it("should handle send data failures", async () => {
      const { BluetoothModule } = require("react-native-bluetooth-classic");
      BluetoothModule.writeToDevice.mockRejectedValue(new Error("Send failed"));

      const result = await BluetoothService.sendData("device1", "test data");
      expect(result).toBe(false);
    });
  });

  describe("event callbacks", () => {
    it("should register device discovered callback", () => {
      const callback = jest.fn();
      BluetoothService.onDeviceDiscovered(callback);

      // Simulate device discovery
      // Note: In a real test, we would trigger the actual event
      expect(callback).toBeDefined();
    });

    it("should register device connected callback", () => {
      const callback = jest.fn();
      BluetoothService.onDeviceConnected(callback);

      expect(callback).toBeDefined();
    });

    it("should register data received callback", () => {
      const callback = jest.fn();
      BluetoothService.onDataReceived(callback);

      expect(callback).toBeDefined();
    });
  });
});
