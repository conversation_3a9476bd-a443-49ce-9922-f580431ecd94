/**
 * Cross-platform EventEmitter implementation
 * Compatible with React Native, React Native Web, and Node.js environments
 */
export class EventEmitter {
  private events: { [key: string]: Function[] } = {};

  /**
   * Add an event listener
   */
  on(event: string, listener: Function): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  /**
   * Add a one-time event listener
   */
  once(event: string, listener: Function): void {
    const onceWrapper = (...args: any[]) => {
      listener(...args);
      this.removeListener(event, onceWrapper);
    };
    this.on(event, onceWrapper);
  }

  /**
   * Emit an event to all listeners
   */
  emit(event: string, ...args: any[]): boolean {
    if (this.events[event]) {
      this.events[event].forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
      return true;
    }
    return false;
  }

  /**
   * Remove all listeners for an event, or all events if no event specified
   */
  removeAllListeners(event?: string): void {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }

  /**
   * Remove a specific listener for an event
   */
  removeListener(event: string, listener: Function): void {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(l => l !== listener);
      if (this.events[event].length === 0) {
        delete this.events[event];
      }
    }
  }

  /**
   * Alias for removeListener
   */
  off(event: string, listener: Function): void {
    this.removeListener(event, listener);
  }

  /**
   * Get the number of listeners for an event
   */
  listenerCount(event: string): number {
    return this.events[event] ? this.events[event].length : 0;
  }

  /**
   * Get all event names that have listeners
   */
  eventNames(): string[] {
    return Object.keys(this.events);
  }

  /**
   * Get all listeners for an event
   */
  listeners(event: string): Function[] {
    return this.events[event] ? [...this.events[event]] : [];
  }
}

export default EventEmitter;
