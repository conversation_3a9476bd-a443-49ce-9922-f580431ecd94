# 🚀 LoGaCo CI/CD Setup Guide

This guide provides step-by-step instructions for setting up and configuring the CI/CD pipeline for the LoGaCo project.

## 📋 Prerequisites

Before setting up the CI/CD pipeline, ensure you have:

- [ ] GitHub repository with admin access
- [ ] Expo account with CLI access
- [ ] Apple Developer account (for iOS builds)
- [ ] Google Play Console account (for Android builds)
- [ ] Node.js 18+ installed locally

## 🔧 Initial Setup

### 1. Repository Configuration

1. **Initialize Git repository** (if not already done):
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/LoGaCo.git
   git push -u origin main
   ```

2. **Create development branch**:
   ```bash
   git checkout -b develop
   git push -u origin develop
   ```

3. **Set up branch protection rules** in GitHub:
   - Go to Settings → Branches
   - Add protection rule for `main` branch:
     - Require pull request reviews
     - Require status checks to pass
     - Require branches to be up to date
     - Include administrators

### 2. Environment Variables and Secrets

Configure the following secrets in GitHub repository settings (Settings → Secrets and variables → Actions):

#### Required Secrets:
```
EXPO_TOKEN=your_expo_access_token
APPLE_ID=<EMAIL>
APPLE_APP_SPECIFIC_PASSWORD=your_app_specific_password
APPLE_TEAM_ID=your_apple_team_id
GOOGLE_SERVICE_ACCOUNT_KEY=your_google_service_account_json
SNYK_TOKEN=your_snyk_token (optional)
```

#### Environment Variables:
```
NODE_VERSION=18
EXPO_CLI_VERSION=latest
APP_ENV=production
```

### 3. Expo Configuration

1. **Install Expo CLI**:
   ```bash
   npm install -g @expo/cli eas-cli
   ```

2. **Login to Expo**:
   ```bash
   npx expo login
   ```

3. **Configure EAS**:
   ```bash
   npx eas build:configure
   ```

4. **Update app.json** with your project details:
   ```json
   {
     "expo": {
       "name": "LoGaCo",
       "slug": "logaco",
       "owner": "your-expo-username",
       "projectId": "your-project-id"
     }
   }
   ```

## 🔄 Workflow Overview

### Continuous Integration (CI)

The CI pipeline runs on:
- Pull requests to `main` and `develop` branches
- Pushes to `main` and `develop` branches
- Manual workflow dispatch
- Scheduled nightly builds

**CI Jobs:**
1. **Code Quality & Linting** - ESLint, Prettier, TypeScript checks
2. **Automated Testing** - Jest unit tests with coverage
3. **Security Scanning** - npm audit, Snyk, secret detection
4. **Build Verification** - Multi-platform builds (Web, Android, iOS)
5. **Quality Gate** - Ensures all checks pass

### Continuous Deployment (CD)

The CD pipeline handles:
- **Development**: Auto-deploy on `develop` branch
- **Staging**: Auto-deploy on `main` branch with manual approval
- **Production**: Manual approval required

**CD Jobs:**
1. **Pre-deployment Checks** - Environment determination
2. **Development Deployment** - Expo development builds
3. **Staging Deployment** - Internal testing builds
4. **Production Deployment** - App store builds and submissions
5. **Post-deployment Monitoring** - Health checks and metrics

### Release Management

The release pipeline manages:
- Version tagging and GitHub releases
- App store submissions
- Release notes generation
- Artifact management

## 📱 Platform-Specific Setup

### iOS Setup

1. **Apple Developer Account**:
   - Enroll in Apple Developer Program
   - Create App ID for LoGaCo
   - Generate certificates and provisioning profiles

2. **App Store Connect**:
   - Create app listing
   - Configure app metadata
   - Set up TestFlight for beta testing

3. **EAS Configuration**:
   ```bash
   npx eas credentials:configure
   ```

### Android Setup

1. **Google Play Console**:
   - Create app listing
   - Generate upload key
   - Configure app signing

2. **Service Account**:
   - Create service account in Google Cloud Console
   - Download JSON key file
   - Add to GitHub secrets as `GOOGLE_SERVICE_ACCOUNT_KEY`

## 🔍 Monitoring and Maintenance

### Pipeline Monitoring

- **GitHub Actions**: Monitor workflow runs in the Actions tab
- **Expo Dashboard**: Track builds and deployments
- **App Store Connect**: Monitor app review status
- **Google Play Console**: Track release rollouts

### Automated Maintenance

The maintenance workflow runs:
- **Daily**: Security scans and health checks
- **Weekly**: Dependency updates and performance audits
- **Monthly**: Comprehensive system cleanup

### Manual Interventions

**Emergency Deployment**:
```bash
# Skip tests for emergency deployment
gh workflow run cd.yml -f environment=production -f skip_tests=true
```

**Rollback Deployment**:
```bash
# Trigger rollback workflow
gh workflow run cd.yml -f rollback=true
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Expo CLI version compatibility
   - Verify all dependencies are installed
   - Review build logs for specific errors

2. **Test Failures**:
   - Ensure all mocks are properly configured
   - Check test coverage thresholds
   - Verify Jest configuration

3. **Deployment Issues**:
   - Validate environment variables
   - Check app store credentials
   - Verify EAS configuration

### Debug Commands

```bash
# Run CI pipeline locally
npm run lint
npm run type-check
npm run test:ci

# Test builds locally
npx expo export --platform web
npx expo export --platform android
npx expo export --platform ios

# Check EAS configuration
npx eas build:list
npx eas update:list
```

## 📊 Performance Metrics

### Key Performance Indicators (KPIs)

- **Build Success Rate**: Target >95%
- **Test Coverage**: Target >80%
- **Deployment Time**: Target <15 minutes
- **Time to Recovery**: Target <30 minutes
- **Security Vulnerabilities**: Target 0 high/critical

### Monitoring Tools

- **GitHub Actions**: Built-in workflow monitoring
- **Codecov**: Test coverage reporting
- **Snyk**: Security vulnerability monitoring
- **Expo Analytics**: App performance metrics

## 🔄 Continuous Improvement

### Regular Reviews

- **Weekly**: Review failed builds and deployment issues
- **Monthly**: Analyze performance metrics and optimize workflows
- **Quarterly**: Update dependencies and security configurations

### Optimization Opportunities

- **Parallel Jobs**: Optimize job dependencies for faster execution
- **Caching**: Implement better caching strategies
- **Resource Allocation**: Adjust runner resources based on usage
- **Test Optimization**: Improve test execution time

## 📞 Support and Resources

### Documentation
- [Expo Documentation](https://docs.expo.dev/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)

### Community Support
- [Expo Discord](https://discord.gg/expo)
- [React Native Community](https://reactnative.dev/community/overview)
- [GitHub Actions Community](https://github.community/c/github-actions)

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintainer**: LoGaCo Development Team
