import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

interface InfoModalProps {
  visible: boolean;
  title: string;
  message?: string;
  content?: React.ReactNode;
  buttonText?: string;
  buttonColor?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  onClose: () => void;
  scrollable?: boolean;
}

const { width, height } = Dimensions.get('window');

export default function InfoModal({
  visible,
  title,
  message,
  content,
  buttonText = 'OK',
  buttonColor = '#00D4FF',
  icon = 'information-circle',
  iconColor = '#00D4FF',
  onClose,
  scrollable = false,
}: InfoModalProps) {
  const renderContent = () => {
    if (content) {
      return scrollable ? (
        <ScrollView
          style={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {content}
        </ScrollView>
      ) : (
        <View style={styles.contentContainer}>{content}</View>
      );
    }

    return (
      <Text style={styles.message}>{message}</Text>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <BlurView intensity={20} style={styles.blurOverlay}>
          <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={1}
            onPress={onClose}
          />
          
          <View style={[
            styles.modalContainer,
            scrollable && styles.modalContainerScrollable
          ]}>
            <BlurView intensity={40} style={styles.modalBlur}>
              <View style={styles.modalContent}>
                {/* Header */}
                <View style={styles.header}>
                  {icon && (
                    <View style={[styles.iconContainer, { backgroundColor: `${iconColor}20` }]}>
                      <Ionicons name={icon} size={28} color={iconColor} />
                    </View>
                  )}
                  
                  <Text style={styles.title}>{title}</Text>
                  
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={onClose}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <Ionicons name="close" size={24} color="rgba(255, 255, 255, 0.7)" />
                  </TouchableOpacity>
                </View>

                {/* Content */}
                <View style={styles.body}>
                  {renderContent()}
                </View>

                {/* Action */}
                <View style={styles.footer}>
                  <TouchableOpacity
                    style={[styles.button, { backgroundColor: buttonColor }]}
                    onPress={onClose}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.buttonText}>{buttonText}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </BlurView>
          </View>
        </BlurView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: width * 0.9,
    maxWidth: 450,
    maxHeight: height * 0.8,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
  },
  modalContainerScrollable: {
    maxHeight: height * 0.85,
  },
  modalBlur: {
    flex: 1,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  title: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  body: {
    flex: 1,
    marginBottom: 20,
  },
  message: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 24,
  },
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 10,
  },
  footer: {
    alignItems: 'center',
  },
  button: {
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 12,
    minWidth: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
