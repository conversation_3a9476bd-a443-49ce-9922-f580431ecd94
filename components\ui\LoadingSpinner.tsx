import React, { useEffect, useRef } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  ViewStyle,
  Easing,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  style?: ViewStyle;
  overlay?: boolean;
  variant?: 'default' | 'gradient' | 'pulse' | 'dots';
}

export default function LoadingSpinner({
  size = 'medium',
  color = '#00D4FF',
  style,
  overlay = false,
  variant = 'default',
}: LoadingSpinnerProps) {
  const spinValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(0)).current;
  const dotValues = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  const getSizeValue = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 48;
      default:
        return 32;
    }
  };

  useEffect(() => {
    if (variant === 'default' || variant === 'gradient') {
      // Spinning animation
      const spinAnimation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
      spinAnimation.start();

      return () => spinAnimation.stop();
    } else if (variant === 'pulse') {
      // Pulse animation
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 1,
            duration: 600,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 0,
            duration: 600,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => pulseAnimation.stop();
    } else if (variant === 'dots') {
      // Dots animation
      const createDotAnimation = (value: Animated.Value, delay: number) =>
        Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(value, {
              toValue: 1,
              duration: 400,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(value, {
              toValue: 0,
              duration: 400,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ])
        );

      const dotAnimations = dotValues.map((value, index) =>
        createDotAnimation(value, index * 200)
      );

      dotAnimations.forEach(animation => animation.start());

      return () => dotAnimations.forEach(animation => animation.stop());
    }
  }, [variant, spinValue, pulseValue, dotValues]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const renderSpinner = () => {
    const sizeValue = getSizeValue();

    if (variant === 'dots') {
      return (
        <View style={[styles.dotsContainer, { width: sizeValue * 2 }]}>
          {dotValues.map((value, index) => (
            <Animated.View
              key={index}
              style={[
                styles.dot,
                {
                  width: sizeValue / 4,
                  height: sizeValue / 4,
                  backgroundColor: color,
                  opacity: value.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.3, 1],
                  }),
                  transform: [
                    {
                      scale: value.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.8, 1.2],
                      }),
                    },
                  ],
                },
              ]}
            />
          ))}
        </View>
      );
    }

    if (variant === 'pulse') {
      return (
        <Animated.View
          style={[
            styles.pulseSpinner,
            {
              width: sizeValue,
              height: sizeValue,
              borderColor: color,
              opacity: pulseValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.3, 1],
              }),
              transform: [
                {
                  scale: pulseValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
      );
    }

    if (variant === 'gradient') {
      return (
        <Animated.View
          style={[
            styles.gradientSpinner,
            {
              width: sizeValue,
              height: sizeValue,
              transform: [{ rotate: spin }],
            },
          ]}
        >
          <LinearGradient
            colors={[color, 'transparent']}
            style={styles.gradientCircle}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>
      );
    }

    // Default spinner
    return (
      <Animated.View
        style={[
          styles.defaultSpinner,
          {
            width: sizeValue,
            height: sizeValue,
            borderColor: color,
            transform: [{ rotate: spin }],
          },
        ]}
      />
    );
  };

  const content = (
    <View style={[styles.container, style]}>
      {renderSpinner()}
    </View>
  );

  if (overlay) {
    return (
      <View style={styles.overlay}>
        <BlurView intensity={10} style={styles.overlayBlur}>
          {content}
        </BlurView>
      </View>
    );
  }

  return content;
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlayBlur: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  defaultSpinner: {
    borderWidth: 3,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderRadius: 50,
  },
  gradientSpinner: {
    borderRadius: 50,
    overflow: 'hidden',
  },
  gradientCircle: {
    flex: 1,
    borderRadius: 50,
  },
  pulseSpinner: {
    borderWidth: 3,
    borderRadius: 50,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dot: {
    borderRadius: 50,
  },
});
