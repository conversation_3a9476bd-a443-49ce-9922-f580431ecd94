import { Platform } from 'react-native';
import CryptoJS from 'crypto-js';

export interface EncryptionConfig {
  algorithm: 'AES-256-GCM' | 'ChaCha20-Poly1305';
  keyDerivation: 'PBKDF2' | 'Argon2';
  iterations: number;
  saltLength: number;
}

export interface EncryptedData {
  data: string;
  iv: string;
  salt: string;
  tag?: string;
  timestamp: number;
}

export interface KeyExchangeData {
  publicKey: string;
  signature: string;
  timestamp: number;
  deviceId: string;
}

class EncryptionService {
  private static instance: EncryptionService;
  private config: EncryptionConfig;
  private sessionKeys: Map<string, string> = new Map();
  private keyPairs: Map<string, { publicKey: string; privateKey: string }> = new Map();

  private constructor() {
    this.config = {
      algorithm: 'AES-256-GCM',
      keyDerivation: 'PBKDF2',
      iterations: 100000,
      saltLength: 32
    };
  }

  static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService();
    }
    return EncryptionService.instance;
  }

  // Key Generation and Management
  async generateSessionKey(deviceId: string, sharedSecret?: string): Promise<string> {
    try {
      let keyMaterial: string;
      
      if (sharedSecret) {
        // Use shared secret from key exchange
        keyMaterial = sharedSecret;
      } else {
        // Generate random key material
        keyMaterial = CryptoJS.lib.WordArray.random(256/8).toString();
      }

      // Derive session key using PBKDF2
      const salt = CryptoJS.lib.WordArray.random(this.config.saltLength);
      const sessionKey = CryptoJS.PBKDF2(keyMaterial, salt, {
        keySize: 256/32,
        iterations: this.config.iterations
      }).toString();

      this.sessionKeys.set(deviceId, sessionKey);
      console.log(`Session key generated for device: ${deviceId}`);
      
      return sessionKey;
    } catch (error) {
      console.error('Failed to generate session key:', error);
      throw new Error('Session key generation failed');
    }
  }

  async generateKeyPair(deviceId: string): Promise<{ publicKey: string; privateKey: string }> {
    try {
      // Simulate key pair generation (in real implementation, use native crypto)
      const privateKey = CryptoJS.lib.WordArray.random(256/8).toString();
      const publicKey = CryptoJS.SHA256(privateKey + deviceId).toString();

      const keyPair = { publicKey, privateKey };
      this.keyPairs.set(deviceId, keyPair);

      console.log(`Key pair generated for device: ${deviceId}`);
      return keyPair;
    } catch (error) {
      console.error('Failed to generate key pair:', error);
      throw new Error('Key pair generation failed');
    }
  }

  // Encryption and Decryption
  async encrypt(data: string, deviceId: string): Promise<EncryptedData> {
    try {
      const sessionKey = this.sessionKeys.get(deviceId);
      if (!sessionKey) {
        throw new Error('No session key found for device');
      }

      // Generate random IV
      const iv = CryptoJS.lib.WordArray.random(96/8); // 96-bit IV for GCM
      const salt = CryptoJS.lib.WordArray.random(this.config.saltLength);

      // Encrypt data using AES-GCM
      const encrypted = CryptoJS.AES.encrypt(data, sessionKey, {
        iv: iv,
        mode: CryptoJS.mode.GCM,
        padding: CryptoJS.pad.NoPadding
      });

      const encryptedData: EncryptedData = {
        data: encrypted.ciphertext.toString(),
        iv: iv.toString(),
        salt: salt.toString(),
        tag: encrypted.tag?.toString(),
        timestamp: Date.now()
      };

      console.log(`Data encrypted for device: ${deviceId}`);
      return encryptedData;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Data encryption failed');
    }
  }

  async decrypt(encryptedData: EncryptedData, deviceId: string): Promise<string> {
    try {
      const sessionKey = this.sessionKeys.get(deviceId);
      if (!sessionKey) {
        throw new Error('No session key found for device');
      }

      // Verify timestamp (prevent replay attacks)
      const maxAge = 5 * 60 * 1000; // 5 minutes
      if (Date.now() - encryptedData.timestamp > maxAge) {
        throw new Error('Encrypted data has expired');
      }

      // Decrypt data
      const decrypted = CryptoJS.AES.decrypt(
        {
          ciphertext: CryptoJS.enc.Hex.parse(encryptedData.data),
          tag: encryptedData.tag ? CryptoJS.enc.Hex.parse(encryptedData.tag) : undefined
        },
        sessionKey,
        {
          iv: CryptoJS.enc.Hex.parse(encryptedData.iv),
          mode: CryptoJS.mode.GCM,
          padding: CryptoJS.pad.NoPadding
        }
      );

      const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
      if (!decryptedText) {
        throw new Error('Decryption failed - invalid data or key');
      }

      console.log(`Data decrypted for device: ${deviceId}`);
      return decryptedText;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Data decryption failed');
    }
  }

  // Key Exchange Protocol
  async initiateKeyExchange(deviceId: string): Promise<KeyExchangeData> {
    try {
      const keyPair = await this.generateKeyPair(deviceId);
      
      // Create key exchange data
      const keyExchangeData: KeyExchangeData = {
        publicKey: keyPair.publicKey,
        signature: await this.signData(keyPair.publicKey, keyPair.privateKey),
        timestamp: Date.now(),
        deviceId
      };

      console.log(`Key exchange initiated for device: ${deviceId}`);
      return keyExchangeData;
    } catch (error) {
      console.error('Key exchange initiation failed:', error);
      throw new Error('Key exchange failed');
    }
  }

  async completeKeyExchange(
    remoteKeyExchange: KeyExchangeData,
    localDeviceId: string
  ): Promise<string> {
    try {
      // Verify signature
      const isValid = await this.verifySignature(
        remoteKeyExchange.publicKey,
        remoteKeyExchange.signature,
        remoteKeyExchange.publicKey
      );

      if (!isValid) {
        throw new Error('Invalid key exchange signature');
      }

      // Generate shared secret
      const localKeyPair = this.keyPairs.get(localDeviceId);
      if (!localKeyPair) {
        throw new Error('Local key pair not found');
      }

      const sharedSecret = await this.computeSharedSecret(
        localKeyPair.privateKey,
        remoteKeyExchange.publicKey
      );

      // Generate session key from shared secret
      const sessionKey = await this.generateSessionKey(
        remoteKeyExchange.deviceId,
        sharedSecret
      );

      console.log(`Key exchange completed with device: ${remoteKeyExchange.deviceId}`);
      return sessionKey;
    } catch (error) {
      console.error('Key exchange completion failed:', error);
      throw new Error('Key exchange failed');
    }
  }

  // Digital Signatures
  private async signData(data: string, privateKey: string): Promise<string> {
    try {
      // Simulate digital signature (in real implementation, use proper signing)
      const signature = CryptoJS.HmacSHA256(data, privateKey).toString();
      return signature;
    } catch (error) {
      console.error('Data signing failed:', error);
      throw new Error('Signing failed');
    }
  }

  private async verifySignature(
    data: string,
    signature: string,
    publicKey: string
  ): Promise<boolean> {
    try {
      // Simulate signature verification
      const expectedSignature = CryptoJS.HmacSHA256(data, publicKey).toString();
      return signature === expectedSignature;
    } catch (error) {
      console.error('Signature verification failed:', error);
      return false;
    }
  }

  private async computeSharedSecret(
    privateKey: string,
    remotePublicKey: string
  ): Promise<string> {
    try {
      // Simulate ECDH key agreement (in real implementation, use proper ECDH)
      const sharedSecret = CryptoJS.SHA256(privateKey + remotePublicKey).toString();
      return sharedSecret;
    } catch (error) {
      console.error('Shared secret computation failed:', error);
      throw new Error('Shared secret computation failed');
    }
  }

  // Utility Methods
  getSessionKey(deviceId: string): string | undefined {
    return this.sessionKeys.get(deviceId);
  }

  hasSessionKey(deviceId: string): boolean {
    return this.sessionKeys.has(deviceId);
  }

  revokeSessionKey(deviceId: string): void {
    this.sessionKeys.delete(deviceId);
    this.keyPairs.delete(deviceId);
    console.log(`Session key revoked for device: ${deviceId}`);
  }

  clearAllKeys(): void {
    this.sessionKeys.clear();
    this.keyPairs.clear();
    console.log('All encryption keys cleared');
  }

  // Security Utilities
  async hashPassword(password: string, salt?: string): Promise<{ hash: string; salt: string }> {
    const passwordSalt = salt || CryptoJS.lib.WordArray.random(this.config.saltLength).toString();
    const hash = CryptoJS.PBKDF2(password, passwordSalt, {
      keySize: 256/32,
      iterations: this.config.iterations
    }).toString();

    return { hash, salt: passwordSalt };
  }

  async verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
    const { hash: computedHash } = await this.hashPassword(password, salt);
    return computedHash === hash;
  }

  generateSecureToken(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString();
  }
}

export default EncryptionService.getInstance();
