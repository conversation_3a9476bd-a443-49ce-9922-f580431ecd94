const { getDefaultConfig } = require("expo/metro-config");
const path = require("path");

const config = getDefaultConfig(__dirname);

// Fix module resolution issues
config.serializer = {
  ...config.serializer,
  createModuleIdFactory: () => {
    let nextId = 0;
    const pathToId = new Map();

    return (modulePath) => {
      if (pathToId.has(modulePath)) {
        return pathToId.get(modulePath);
      }

      const id = nextId++;
      pathToId.set(modulePath, id);
      return id;
    };
  },
};

// Ensure proper path resolution
config.resolver = {
  ...config.resolver,
  platforms: ["ios", "android", "native", "web"],
  alias: {
    // Add any path aliases if needed
  },
  assetExts: [
    ...config.resolver.assetExts,
    'ttf',
    'otf',
    'woff',
    'woff2',
    'eot',
    'svg',
  ],
};

// Improve asset handling for Android builds
config.transformer = {
  ...config.transformer,
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
};

module.exports = config;
