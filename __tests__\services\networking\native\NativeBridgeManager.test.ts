import { Platform } from 'react-native';
import NativeBridgeManager from '../../../../services/networking/native/NativeBridgeManager';

// Mock the platform-specific modules
jest.mock('../../../../services/networking/native/iOSMultipeerConnectivity', () => ({
  isAvailable: jest.fn(() => Platform.OS === 'ios'),
  createSession: jest.fn(),
  joinSession: jest.fn(),
  sendGameData: jest.fn(),
  broadcastData: jest.fn(),
  getConnectedPeers: jest.fn(() => Promise.resolve({ peers: [], count: 0 })),
  getSessionInfo: jest.fn(() => Promise.resolve({ 
    localPeerID: 'test-peer',
    isAdvertising: false,
    isBrowsing: false 
  })),
  cleanup: jest.fn(),
  on: jest.fn(),
  onPeerConnected: jest.fn(),
  onPeerDisconnected: jest.fn(),
  onDataReceived: jest.fn()
}));

jest.mock('../../../../services/networking/native/AndroidVPNService', () => ({
  isAvailable: jest.fn(() => Platform.OS === 'android'),
  startTrafficInterception: jest.fn(),
  stopTrafficInterception: jest.fn(),
  getTrafficStats: jest.fn(() => Promise.resolve({
    bytesReceived: 0,
    bytesSent: 0,
    packetsProcessed: 0,
    isActive: false
  })),
  getVPNStatus: jest.fn(() => Promise.resolve({
    isRunning: false,
    isServiceBound: false,
    bytesReceived: 0,
    bytesSent: 0,
    packetsProcessed: 0
  })),
  getNetworkInterfaces: jest.fn(() => Promise.resolve({})),
  getSystemNetworkInfo: jest.fn(() => Promise.resolve({
    type: 'wifi',
    isConnected: true
  })),
  cleanup: jest.fn(),
  on: jest.fn(),
  onVPNStarted: jest.fn(),
  onVPNStopped: jest.fn(),
  onGamingPacketDetected: jest.fn()
}));

describe('NativeBridgeManager', () => {
  let nativeBridgeManager: typeof NativeBridgeManager;

  beforeEach(() => {
    jest.clearAllMocks();
    nativeBridgeManager = NativeBridgeManager;
  });

  afterEach(async () => {
    await nativeBridgeManager.cleanup();
  });

  describe('Initialization', () => {
    it('should initialize successfully with default config', async () => {
      await expect(nativeBridgeManager.initialize()).resolves.not.toThrow();
      expect(nativeBridgeManager.isInitializedState()).toBe(true);
    });

    it('should initialize with custom config', async () => {
      const config = {
        enableTrafficInterception: false,
        enablePeerToPeer: true,
        enableNetworkMonitoring: false,
        autoStartServices: true
      };

      await expect(nativeBridgeManager.initialize(config)).resolves.not.toThrow();
      expect(nativeBridgeManager.getConfig()).toMatchObject(config);
    });

    it('should detect platform capabilities correctly', () => {
      const capabilities = nativeBridgeManager.getCapabilities();
      
      expect(capabilities).toHaveProperty('hasMultipeerConnectivity');
      expect(capabilities).toHaveProperty('hasVPNService');
      expect(capabilities).toHaveProperty('hasNetworkInterfaces');
      expect(capabilities).toHaveProperty('hasTrafficInterception');
      expect(capabilities).toHaveProperty('hasDeepPacketInspection');

      if (Platform.OS === 'ios') {
        expect(capabilities.hasMultipeerConnectivity).toBe(true);
        expect(capabilities.hasVPNService).toBe(false);
      } else if (Platform.OS === 'android') {
        expect(capabilities.hasMultipeerConnectivity).toBe(false);
        expect(capabilities.hasVPNService).toBe(true);
      }
    });
  });

  describe('Service Management', () => {
    beforeEach(async () => {
      await nativeBridgeManager.initialize();
    });

    it('should start all services successfully', async () => {
      await expect(nativeBridgeManager.startAllServices()).resolves.not.toThrow();
    });

    it('should stop all services successfully', async () => {
      await nativeBridgeManager.startAllServices();
      await expect(nativeBridgeManager.stopAllServices()).resolves.not.toThrow();
    });

    it('should emit service events', async () => {
      const servicesStartedSpy = jest.fn();
      const servicesStoppedSpy = jest.fn();

      nativeBridgeManager.on('servicesStarted', servicesStartedSpy);
      nativeBridgeManager.on('servicesStopped', servicesStoppedSpy);

      await nativeBridgeManager.startAllServices();
      expect(servicesStartedSpy).toHaveBeenCalled();

      await nativeBridgeManager.stopAllServices();
      expect(servicesStoppedSpy).toHaveBeenCalled();
    });
  });

  describe('Data Transmission', () => {
    beforeEach(async () => {
      await nativeBridgeManager.initialize();
    });

    it('should send data to peer on iOS', async () => {
      if (Platform.OS === 'ios') {
        const testData = { message: 'test' };
        const peerId = 'test-peer';

        await expect(
          nativeBridgeManager.sendDataToPeer(testData, peerId)
        ).resolves.not.toThrow();
      }
    });

    it('should broadcast game data on iOS', async () => {
      if (Platform.OS === 'ios') {
        const gameData = { 
          type: 'game_state',
          players: ['player1', 'player2']
        };

        await expect(
          nativeBridgeManager.broadcastGameData(gameData)
        ).resolves.not.toThrow();
      }
    });

    it('should handle data transmission gracefully on unsupported platforms', async () => {
      if (Platform.OS !== 'ios') {
        const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
        
        await nativeBridgeManager.sendDataToPeer({ test: 'data' });
        expect(consoleSpy).toHaveBeenCalledWith(
          'Peer-to-peer data transmission not available on this platform'
        );

        consoleSpy.mockRestore();
      }
    });
  });

  describe('Network Statistics', () => {
    beforeEach(async () => {
      await nativeBridgeManager.initialize();
    });

    it('should get unified network stats', async () => {
      const stats = await nativeBridgeManager.getUnifiedNetworkStats();

      expect(stats).toHaveProperty('platform');
      expect(stats).toHaveProperty('bytesReceived');
      expect(stats).toHaveProperty('bytesSent');
      expect(stats).toHaveProperty('packetsProcessed');
      expect(stats).toHaveProperty('connectedPeers');
      expect(stats).toHaveProperty('isTrafficInterceptionActive');
      expect(stats).toHaveProperty('isPeerToPeerActive');
      expect(stats).toHaveProperty('lastUpdated');

      expect(stats.platform).toBe(Platform.OS);
      expect(typeof stats.bytesReceived).toBe('number');
      expect(typeof stats.bytesSent).toBe('number');
      expect(typeof stats.packetsProcessed).toBe('number');
      expect(typeof stats.connectedPeers).toBe('number');
      expect(typeof stats.isTrafficInterceptionActive).toBe('boolean');
      expect(typeof stats.isPeerToPeerActive).toBe('boolean');
      expect(typeof stats.lastUpdated).toBe('number');
    });

    it('should get detailed network info', async () => {
      const info = await nativeBridgeManager.getDetailedNetworkInfo();

      expect(info).toHaveProperty('platform');
      expect(info).toHaveProperty('capabilities');
      expect(info).toHaveProperty('timestamp');

      expect(info.platform).toBe(Platform.OS);
      expect(typeof info.timestamp).toBe('number');
    });

    it('should handle errors in network info gracefully', async () => {
      // Mock an error in the underlying service
      const mockError = new Error('Network error');
      
      if (Platform.OS === 'ios') {
        const iOSService = nativeBridgeManager.getiOSMultipeerConnectivity();
        jest.spyOn(iOSService, 'getSessionInfo').mockRejectedValue(mockError);
      }

      const info = await nativeBridgeManager.getDetailedNetworkInfo();
      
      if (Platform.OS === 'ios') {
        expect(info).toHaveProperty('error');
        expect(info.error).toBe(mockError.message);
      }
    });
  });

  describe('Platform-Specific Access', () => {
    beforeEach(async () => {
      await nativeBridgeManager.initialize();
    });

    it('should provide iOS Multipeer Connectivity on iOS', () => {
      if (Platform.OS === 'ios') {
        expect(() => nativeBridgeManager.getiOSMultipeerConnectivity()).not.toThrow();
      } else {
        expect(() => nativeBridgeManager.getiOSMultipeerConnectivity()).toThrow(
          'iOS Multipeer Connectivity not available on this platform'
        );
      }
    });

    it('should provide Android VPN Service on Android', () => {
      if (Platform.OS === 'android') {
        expect(() => nativeBridgeManager.getAndroidVPNService()).not.toThrow();
      } else {
        expect(() => nativeBridgeManager.getAndroidVPNService()).toThrow(
          'Android VPN Service not available on this platform'
        );
      }
    });
  });

  describe('Event Handling', () => {
    beforeEach(async () => {
      await nativeBridgeManager.initialize();
    });

    it('should emit stats update events during monitoring', (done) => {
      const statsUpdateSpy = jest.fn((stats) => {
        expect(stats).toHaveProperty('platform');
        expect(stats).toHaveProperty('lastUpdated');
        done();
      });

      nativeBridgeManager.on('statsUpdate', statsUpdateSpy);

      // Trigger a stats update manually
      nativeBridgeManager.emit('statsUpdate', {
        platform: Platform.OS,
        bytesReceived: 0,
        bytesSent: 0,
        packetsProcessed: 0,
        connectedPeers: 0,
        isTrafficInterceptionActive: false,
        isPeerToPeerActive: false,
        lastUpdated: Date.now()
      });
    });

    it('should handle peer connection events', () => {
      const peerConnectedSpy = jest.fn();
      nativeBridgeManager.on('peerConnected', peerConnectedSpy);

      // Simulate peer connection event
      nativeBridgeManager.emit('peerConnected', {
        platform: Platform.OS,
        peerID: 'test-peer',
        state: 'connected',
        timestamp: Date.now()
      });

      expect(peerConnectedSpy).toHaveBeenCalled();
    });

    it('should handle gaming packet detection events', () => {
      const packetDetectedSpy = jest.fn();
      nativeBridgeManager.on('gamingPacketDetected', packetDetectedSpy);

      // Simulate gaming packet detection
      nativeBridgeManager.emit('gamingPacketDetected', {
        platform: Platform.OS,
        sourceIP: '*************',
        destIP: '*************',
        protocol: 6,
        size: 1024,
        timestamp: Date.now()
      });

      expect(packetDetectedSpy).toHaveBeenCalled();
    });
  });

  describe('Cleanup', () => {
    it('should cleanup all resources', async () => {
      await nativeBridgeManager.initialize();
      await nativeBridgeManager.startAllServices();
      
      await expect(nativeBridgeManager.cleanup()).resolves.not.toThrow();
    });

    it('should remove all event listeners during cleanup', async () => {
      await nativeBridgeManager.initialize();
      
      const testListener = jest.fn();
      nativeBridgeManager.on('test', testListener);
      
      await nativeBridgeManager.cleanup();
      
      // Verify listeners are removed
      nativeBridgeManager.emit('test');
      expect(testListener).not.toHaveBeenCalled();
    });
  });
});
