import { Platform, PermissionsAndroid, NativeModules, Linking } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Application from 'expo-application';
import { GameInfo } from './GameDetector';

export interface EnhancedGameInfo extends GameInfo {
  installPath?: string;
  dataPath?: string;
  permissions?: string[];
  lastLaunched?: number;
  isRunning?: boolean;
  memoryUsage?: number;
  diskUsage?: number;
}

export interface GameDetectionResult {
  success: boolean;
  games: EnhancedGameInfo[];
  errors: string[];
  permissions: {
    granted: string[];
    denied: string[];
    required: string[];
  };
}

class GameDetectionEnhancer {
  private static instance: GameDetectionEnhancer;
  private permissionsGranted: Set<string> = new Set();
  private detectionMethods: Map<string, () => Promise<EnhancedGameInfo[]>> = new Map();

  static getInstance(): GameDetectionEnhancer {
    if (!GameDetectionEnhancer.instance) {
      GameDetectionEnhancer.instance = new GameDetectionEnhancer();
    }
    return GameDetectionEnhancer.instance;
  }

  constructor() {
    this.initializeDetectionMethods();
  }

  private initializeDetectionMethods() {
    this.detectionMethods.set('android_package_manager', this.detectAndroidPackages.bind(this));
    this.detectionMethods.set('android_file_system', this.detectAndroidFileSystem.bind(this));
    this.detectionMethods.set('ios_url_schemes', this.detectIOSUrlSchemes.bind(this));
    this.detectionMethods.set('ios_file_system', this.detectIOSFileSystem.bind(this));
    this.detectionMethods.set('cross_platform_registry', this.detectCrossPlatformRegistry.bind(this));
  }

  async enhancedGameDetection(): Promise<GameDetectionResult> {
    const result: GameDetectionResult = {
      success: false,
      games: [],
      errors: [],
      permissions: {
        granted: [],
        denied: [],
        required: this.getRequiredPermissions(),
      },
    };

    try {
      console.log('🔍 Starting enhanced game detection...');

      // Step 1: Request and verify permissions
      const permissionResult = await this.requestAllPermissions();
      result.permissions = permissionResult;

      if (permissionResult.denied.length > 0) {
        console.warn('⚠️ Some permissions denied:', permissionResult.denied);
        result.errors.push(`Permissions denied: ${permissionResult.denied.join(', ')}`);
      }

      // Step 2: Run all detection methods
      const detectionPromises = Array.from(this.detectionMethods.entries()).map(
        async ([method, detector]) => {
          try {
            console.log(`🔍 Running detection method: ${method}`);
            const games = await detector();
            console.log(`✅ ${method} found ${games.length} games`);
            return { method, games, error: null };
          } catch (error) {
            console.error(`❌ ${method} failed:`, error);
            return { 
              method, 
              games: [], 
              error: error instanceof Error ? error.message : 'Unknown error' 
            };
          }
        }
      );

      const detectionResults = await Promise.all(detectionPromises);

      // Step 3: Merge and deduplicate results
      const gameMap = new Map<string, EnhancedGameInfo>();
      
      detectionResults.forEach(({ method, games, error }) => {
        if (error) {
          result.errors.push(`${method}: ${error}`);
        }
        
        games.forEach(game => {
          const existing = gameMap.get(game.packageName);
          if (existing) {
            // Merge data from multiple sources
            gameMap.set(game.packageName, this.mergeGameInfo(existing, game));
          } else {
            gameMap.set(game.packageName, game);
          }
        });
      });

      result.games = Array.from(gameMap.values());
      result.success = result.games.length > 0;

      console.log(`🎯 Enhanced detection complete: ${result.games.length} games found`);
      
      return result;
    } catch (error) {
      console.error('💥 Enhanced game detection failed:', error);
      result.errors.push(error instanceof Error ? error.message : 'Detection failed');
      return result;
    }
  }

  private getRequiredPermissions(): string[] {
    if (Platform.OS === 'android') {
      return [
        'android.permission.QUERY_ALL_PACKAGES',
        'android.permission.GET_INSTALLED_APPS',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.PACKAGE_USAGE_STATS',
      ];
    } else if (Platform.OS === 'ios') {
      return [
        'NSDocumentDirectory',
        'NSApplicationSupportDirectory',
      ];
    }
    return [];
  }

  private async requestAllPermissions(): Promise<GameDetectionResult['permissions']> {
    const result = {
      granted: [],
      denied: [],
      required: this.getRequiredPermissions(),
    };

    if (Platform.OS === 'android') {
      for (const permission of result.required) {
        try {
          const granted = await PermissionsAndroid.request(permission as any);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            result.granted.push(permission);
            this.permissionsGranted.add(permission);
          } else {
            result.denied.push(permission);
          }
        } catch (error) {
          console.error(`Failed to request permission ${permission}:`, error);
          result.denied.push(permission);
        }
      }
    } else {
      // iOS permissions are handled differently
      result.granted = result.required;
    }

    return result;
  }

  private async detectAndroidPackages(): Promise<EnhancedGameInfo[]> {
    if (Platform.OS !== 'android') return [];

    try {
      const { InstalledApps } = NativeModules;
      if (!InstalledApps) {
        throw new Error('Native InstalledApps module not available');
      }

      const packages = await InstalledApps.getInstalledPackages();
      return packages
        .filter((pkg: any) => this.isLikelyGame(pkg))
        .map((pkg: any) => this.convertToEnhancedGameInfo(pkg));
    } catch (error) {
      console.warn('Android package detection failed:', error);
      return [];
    }
  }

  private async detectAndroidFileSystem(): Promise<EnhancedGameInfo[]> {
    if (Platform.OS !== 'android') return [];

    const games: EnhancedGameInfo[] = [];
    const commonGamePaths = [
      '/data/data/',
      '/Android/data/',
      '/Android/obb/',
    ];

    try {
      for (const basePath of commonGamePaths) {
        const fullPath = FileSystem.documentDirectory + basePath;
        const exists = await FileSystem.getInfoAsync(fullPath);
        
        if (exists.exists && exists.isDirectory) {
          const contents = await FileSystem.readDirectoryAsync(fullPath);
          
          for (const item of contents) {
            if (this.isGamePackageName(item)) {
              const gameInfo = await this.createGameInfoFromPath(fullPath + item, item);
              if (gameInfo) {
                games.push(gameInfo);
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn('Android filesystem detection failed:', error);
    }

    return games;
  }

  private async detectIOSUrlSchemes(): Promise<EnhancedGameInfo[]> {
    if (Platform.OS !== 'ios') return [];

    const games: EnhancedGameInfo[] = [];
    const commonGameSchemes = [
      'minecraft://',
      'candycrushsaga://',
      'amongus://',
      'chess://',
      'roblox://',
      'fortnite://',
    ];

    try {
      for (const scheme of commonGameSchemes) {
        const canOpen = await Linking.canOpenURL(scheme);
        if (canOpen) {
          const gameInfo = this.createGameInfoFromScheme(scheme);
          if (gameInfo) {
            games.push(gameInfo);
          }
        }
      }
    } catch (error) {
      console.warn('iOS URL scheme detection failed:', error);
    }

    return games;
  }

  private async detectIOSFileSystem(): Promise<EnhancedGameInfo[]> {
    if (Platform.OS !== 'ios') return [];

    // iOS has limited filesystem access, but we can check app-specific directories
    const games: EnhancedGameInfo[] = [];

    try {
      const documentsDir = FileSystem.documentDirectory;
      if (documentsDir) {
        const contents = await FileSystem.readDirectoryAsync(documentsDir);
        
        for (const item of contents) {
          if (this.isGameRelatedFile(item)) {
            const gameInfo = await this.createGameInfoFromFile(documentsDir + item, item);
            if (gameInfo) {
              games.push(gameInfo);
            }
          }
        }
      }
    } catch (error) {
      console.warn('iOS filesystem detection failed:', error);
    }

    return games;
  }

  private async detectCrossPlatformRegistry(): Promise<EnhancedGameInfo[]> {
    // This method checks for games using cross-platform indicators
    const games: EnhancedGameInfo[] = [];

    try {
      // Check for common game engines and frameworks
      const gameEngineIndicators = [
        'Unity',
        'Unreal',
        'Godot',
        'Cocos2d',
        'React Native',
      ];

      // This would be expanded with actual detection logic
      console.log('Cross-platform registry detection not fully implemented');
    } catch (error) {
      console.warn('Cross-platform detection failed:', error);
    }

    return games;
  }

  private isLikelyGame(packageInfo: any): boolean {
    const gameCategories = [
      'GAME',
      'ARCADE',
      'PUZZLE',
      'STRATEGY',
      'SIMULATION',
      'SPORTS',
      'RACING',
      'ACTION',
      'ADVENTURE',
      'ROLE_PLAYING',
    ];

    const gameKeywords = [
      'game', 'play', 'puzzle', 'adventure', 'action', 'strategy',
      'simulation', 'racing', 'sports', 'arcade', 'rpg', 'mmo',
    ];

    // Check category
    if (packageInfo.category && gameCategories.includes(packageInfo.category.toUpperCase())) {
      return true;
    }

    // Check package name and app name for game keywords
    const searchText = `${packageInfo.packageName} ${packageInfo.appName || ''}`.toLowerCase();
    return gameKeywords.some(keyword => searchText.includes(keyword));
  }

  private isGamePackageName(packageName: string): boolean {
    const gamePackagePatterns = [
      /\.game\./,
      /\.games\./,
      /\.play\./,
      /\.arcade\./,
      /\.puzzle\./,
      /\.strategy\./,
      /\.simulation\./,
      /\.sports\./,
      /\.racing\./,
      /\.action\./,
      /\.adventure\./,
      /\.rpg\./,
    ];

    return gamePackagePatterns.some(pattern => pattern.test(packageName.toLowerCase()));
  }

  private isGameRelatedFile(filename: string): boolean {
    const gameFileExtensions = ['.save', '.dat', '.game', '.level', '.world'];
    const gameFilePatterns = [/save/, /level/, /world/, /game/, /player/];

    const lowerFilename = filename.toLowerCase();
    
    return gameFileExtensions.some(ext => lowerFilename.endsWith(ext)) ||
           gameFilePatterns.some(pattern => pattern.test(lowerFilename));
  }

  private convertToEnhancedGameInfo(packageInfo: any): EnhancedGameInfo {
    return {
      id: packageInfo.packageName,
      name: packageInfo.appName || packageInfo.packageName,
      packageName: packageInfo.packageName,
      version: packageInfo.versionName,
      icon: packageInfo.icon,
      isInstalled: true,
      supportsMultiplayer: this.detectMultiplayerSupport(packageInfo),
      multiplayerType: this.detectMultiplayerType(packageInfo),
      category: packageInfo.category || 'Unknown',
      developer: packageInfo.developer || 'Unknown',
      lastPlayed: packageInfo.lastTimeUsed,
      playtime: packageInfo.totalTimeInForeground,
      installPath: packageInfo.sourceDir,
      dataPath: packageInfo.dataDir,
      permissions: packageInfo.requestedPermissions || [],
      lastLaunched: packageInfo.lastTimeUsed,
      isRunning: packageInfo.isRunning || false,
      memoryUsage: packageInfo.memoryUsage,
      diskUsage: packageInfo.diskUsage,
    };
  }

  private async createGameInfoFromPath(path: string, packageName: string): Promise<EnhancedGameInfo | null> {
    try {
      const info = await FileSystem.getInfoAsync(path);
      
      return {
        id: packageName,
        name: this.extractGameNameFromPackage(packageName),
        packageName,
        isInstalled: true,
        supportsMultiplayer: false, // Default, would need more detection
        multiplayerType: 'none',
        category: 'Unknown',
        developer: 'Unknown',
        installPath: path,
        diskUsage: info.size,
      };
    } catch (error) {
      return null;
    }
  }

  private createGameInfoFromScheme(scheme: string): EnhancedGameInfo | null {
    const packageName = scheme.replace('://', '');
    
    return {
      id: packageName,
      name: this.extractGameNameFromPackage(packageName),
      packageName,
      isInstalled: true,
      supportsMultiplayer: false,
      multiplayerType: 'none',
      category: 'Unknown',
      developer: 'Unknown',
    };
  }

  private async createGameInfoFromFile(path: string, filename: string): Promise<EnhancedGameInfo | null> {
    try {
      const info = await FileSystem.getInfoAsync(path);
      const packageName = filename.split('.')[0];
      
      return {
        id: packageName,
        name: this.extractGameNameFromPackage(packageName),
        packageName,
        isInstalled: true,
        supportsMultiplayer: false,
        multiplayerType: 'none',
        category: 'Unknown',
        developer: 'Unknown',
        dataPath: path,
        diskUsage: info.size,
      };
    } catch (error) {
      return null;
    }
  }

  private extractGameNameFromPackage(packageName: string): string {
    // Extract readable name from package name
    const parts = packageName.split('.');
    const lastPart = parts[parts.length - 1];
    
    // Capitalize first letter and replace common abbreviations
    return lastPart
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  private detectMultiplayerSupport(packageInfo: any): boolean {
    const multiplayerKeywords = [
      'multiplayer', 'online', 'network', 'bluetooth', 'wifi',
      'co-op', 'versus', 'pvp', 'mmo', 'social',
    ];

    const searchText = `${packageInfo.packageName} ${packageInfo.appName || ''} ${packageInfo.description || ''}`.toLowerCase();
    return multiplayerKeywords.some(keyword => searchText.includes(keyword));
  }

  private detectMultiplayerType(packageInfo: any): 'local' | 'online' | 'both' | 'none' {
    if (!this.detectMultiplayerSupport(packageInfo)) {
      return 'none';
    }

    const searchText = `${packageInfo.packageName} ${packageInfo.appName || ''} ${packageInfo.description || ''}`.toLowerCase();
    
    const hasLocal = ['bluetooth', 'wifi', 'local', 'nearby'].some(keyword => searchText.includes(keyword));
    const hasOnline = ['online', 'internet', 'server', 'cloud'].some(keyword => searchText.includes(keyword));

    if (hasLocal && hasOnline) return 'both';
    if (hasLocal) return 'local';
    if (hasOnline) return 'online';
    
    return 'online'; // Default for multiplayer games
  }

  private mergeGameInfo(existing: EnhancedGameInfo, newInfo: EnhancedGameInfo): EnhancedGameInfo {
    return {
      ...existing,
      ...newInfo,
      // Prefer non-null/undefined values
      name: newInfo.name || existing.name,
      version: newInfo.version || existing.version,
      icon: newInfo.icon || existing.icon,
      developer: newInfo.developer !== 'Unknown' ? newInfo.developer : existing.developer,
      category: newInfo.category !== 'Unknown' ? newInfo.category : existing.category,
      // Merge arrays
      permissions: [...(existing.permissions || []), ...(newInfo.permissions || [])],
      // Use latest timestamps
      lastPlayed: Math.max(existing.lastPlayed || 0, newInfo.lastPlayed || 0),
      lastLaunched: Math.max(existing.lastLaunched || 0, newInfo.lastLaunched || 0),
      // Combine paths
      installPath: newInfo.installPath || existing.installPath,
      dataPath: newInfo.dataPath || existing.dataPath,
    };
  }
}

export default GameDetectionEnhancer.getInstance();
