import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { GameSession, Player } from '../../services/game/SessionManager';
import StatusIndicator from '../ui/StatusIndicator';
import Button from '../ui/Button';

interface SessionCardProps {
  session: GameSession;
  onPress?: () => void;
  onJoin?: () => void;
  onLeave?: () => void;
  onInvite?: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
  currentPlayerId?: string;
}

export default function SessionCard({
  session,
  onPress,
  onJoin,
  onLeave,
  onInvite,
  style,
  variant = 'default',
  showActions = true,
  currentPlayerId,
}: SessionCardProps) {
  const isPlayerInSession = currentPlayerId && 
    session.players.some(player => player.id === currentPlayerId);
  
  const isHost = currentPlayerId === session.host.id;

  const getStatusColor = () => {
    switch (session.status) {
      case 'waiting':
        return '#FF9800';
      case 'starting':
        return '#00D4FF';
      case 'active':
        return '#4CAF50';
      case 'paused':
        return '#9E9E9E';
      case 'ended':
        return '#F44336';
      default:
        return '#666';
    }
  };

  const getStatusText = () => {
    switch (session.status) {
      case 'waiting':
        return 'Waiting for players';
      case 'starting':
        return 'Starting...';
      case 'active':
        return 'In progress';
      case 'paused':
        return 'Paused';
      case 'ended':
        return 'Ended';
      default:
        return 'Unknown';
    }
  };

  const getNetworkIcon = () => {
    switch (session.networkType) {
      case 'wifi':
        return 'wifi';
      case 'bluetooth':
        return 'bluetooth';
      case 'local':
        return 'home';
      default:
        return 'globe';
    }
  };

  const formatDuration = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) {
      return 'Just now';
    } else if (minutes < 60) {
      return `${minutes}m ago`;
    } else {
      const hours = Math.floor(minutes / 60);
      return `${hours}h ago`;
    }
  };

  const renderPlayerAvatars = () => {
    const maxVisible = variant === 'compact' ? 3 : 5;
    const visiblePlayers = session.players.slice(0, maxVisible);
    const remainingCount = session.players.length - maxVisible;

    return (
      <View style={styles.playerAvatars}>
        {visiblePlayers.map((player, index) => (
          <View
            key={player.id}
            style={[
              styles.playerAvatar,
              { marginLeft: index > 0 ? -8 : 0 },
              player.id === session.host.id && styles.hostAvatar,
            ]}
          >
            <Text style={styles.playerInitial}>
              {player.name.charAt(0).toUpperCase()}
            </Text>
            {player.id === session.host.id && (
              <View style={styles.hostBadge}>
                <Ionicons name="crown" size={8} color="#FFD700" />
              </View>
            )}
          </View>
        ))}
        
        {remainingCount > 0 && (
          <View style={[styles.playerAvatar, styles.remainingAvatar]}>
            <Text style={styles.remainingText}>+{remainingCount}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderCompactCard = () => (
    <TouchableOpacity
      style={[styles.compactCard, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={15} style={styles.compactBlur}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.compactGradient}
        >
          <View style={styles.compactContent}>
            <View style={styles.compactLeft}>
              <View style={styles.gameIcon}>
                <Text style={styles.gameEmoji}>{session.game.icon || '🎮'}</Text>
              </View>
              
              <View style={styles.compactInfo}>
                <Text style={styles.compactTitle} numberOfLines={1}>
                  {session.name}
                </Text>
                <Text style={styles.compactGame} numberOfLines={1}>
                  {session.game.name}
                </Text>
              </View>
            </View>

            <View style={styles.compactRight}>
              <StatusIndicator
                status={session.status as any}
                size="small"
                variant="dot"
              />
              <Text style={styles.playerCount}>
                {session.players.length}/{session.maxPlayers}
              </Text>
            </View>
          </View>
        </LinearGradient>
      </BlurView>
    </TouchableOpacity>
  );

  const renderDetailedCard = () => (
    <TouchableOpacity
      style={[styles.detailedCard, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={20} style={styles.detailedBlur}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.15)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.detailedGradient}
        >
          <View style={styles.detailedHeader}>
            <View style={styles.gameInfo}>
              <View style={styles.gameIcon}>
                <Text style={styles.gameEmoji}>{session.game.icon || '🎮'}</Text>
              </View>
              
              <View style={styles.sessionInfo}>
                <Text style={styles.sessionTitle}>{session.name}</Text>
                <Text style={styles.gameName}>{session.game.name}</Text>
                <Text style={styles.hostName}>Hosted by {session.host.name}</Text>
              </View>
            </View>

            <View style={styles.statusContainer}>
              <StatusIndicator
                status={session.status as any}
                label={getStatusText()}
                size="medium"
                variant="badge"
              />
            </View>
          </View>

          <View style={styles.detailedContent}>
            <View style={styles.sessionStats}>
              <View style={styles.statItem}>
                <Ionicons name="people" size={16} color="#00D4FF" />
                <Text style={styles.statText}>
                  {session.players.length}/{session.maxPlayers} players
                </Text>
              </View>
              
              <View style={styles.statItem}>
                <Ionicons name={getNetworkIcon() as any} size={16} color="#00D4FF" />
                <Text style={styles.statText}>{session.networkType}</Text>
              </View>
              
              <View style={styles.statItem}>
                <Ionicons name="time" size={16} color="#00D4FF" />
                <Text style={styles.statText}>
                  {session.startedAt 
                    ? formatDuration(session.startedAt)
                    : formatDuration(session.createdAt)
                  }
                </Text>
              </View>

              {session.settings.isPrivate && (
                <View style={styles.statItem}>
                  <Ionicons name="lock-closed" size={16} color="#FF9800" />
                  <Text style={styles.statText}>Private</Text>
                </View>
              )}
            </View>

            {renderPlayerAvatars()}
          </View>

          {showActions && (
            <View style={styles.actions}>
              {isPlayerInSession ? (
                <View style={styles.actionRow}>
                  {isHost && (
                    <Button
                      title="Invite"
                      onPress={onInvite}
                      variant="secondary"
                      size="small"
                      icon={<Ionicons name="share" size={16} color="#00D4FF" />}
                      style={styles.actionButton}
                    />
                  )}
                  <Button
                    title="Leave"
                    onPress={onLeave}
                    variant="ghost"
                    size="small"
                    icon={<Ionicons name="exit" size={16} color="#FF6B6B" />}
                    style={styles.actionButton}
                  />
                </View>
              ) : (
                session.status === 'waiting' && (
                  <Button
                    title="Join Session"
                    onPress={onJoin}
                    variant="primary"
                    size="small"
                    icon={<Ionicons name="enter" size={16} color="#1a1a2e" />}
                    style={styles.joinButton}
                  />
                )
              )}
            </View>
          )}
        </LinearGradient>
      </BlurView>
    </TouchableOpacity>
  );

  const renderDefaultCard = () => (
    <TouchableOpacity
      style={[styles.defaultCard, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={15} style={styles.defaultBlur}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.defaultGradient}
        >
          <View style={styles.defaultHeader}>
            <View style={styles.gameIcon}>
              <Text style={styles.gameEmoji}>{session.game.icon || '🎮'}</Text>
            </View>
            
            <View style={styles.sessionInfo}>
              <Text style={styles.sessionTitle} numberOfLines={1}>
                {session.name}
              </Text>
              <Text style={styles.gameName} numberOfLines={1}>
                {session.game.name}
              </Text>
            </View>

            <View style={styles.statusBadge}>
              <View
                style={[
                  styles.statusDot,
                  { backgroundColor: getStatusColor() },
                ]}
              />
              <Text style={styles.statusText}>{getStatusText()}</Text>
            </View>
          </View>

          <View style={styles.defaultContent}>
            <View style={styles.sessionMeta}>
              <Text style={styles.metaText}>
                {session.players.length}/{session.maxPlayers} players
              </Text>
              <Text style={styles.metaText}>•</Text>
              <Text style={styles.metaText}>{session.networkType}</Text>
              <Text style={styles.metaText}>•</Text>
              <Text style={styles.metaText}>
                {formatDuration(session.createdAt)}
              </Text>
            </View>

            {renderPlayerAvatars()}
          </View>
        </LinearGradient>
      </BlurView>
    </TouchableOpacity>
  );

  switch (variant) {
    case 'compact':
      return renderCompactCard();
    case 'detailed':
      return renderDetailedCard();
    default:
      return renderDefaultCard();
  }
}

const styles = StyleSheet.create({
  // Compact variant
  compactCard: {
    marginVertical: 4,
  },
  compactBlur: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  compactGradient: {
    padding: 12,
  },
  compactContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  compactLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  compactRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  compactInfo: {
    flex: 1,
  },
  compactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  compactGame: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 2,
  },

  // Detailed variant
  detailedCard: {
    marginVertical: 8,
  },
  detailedBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  detailedGradient: {
    padding: 20,
  },
  detailedHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  detailedContent: {
    gap: 16,
  },

  // Default variant
  defaultCard: {
    marginVertical: 6,
  },
  defaultBlur: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  defaultGradient: {
    padding: 16,
  },
  defaultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  defaultContent: {
    gap: 8,
  },

  // Shared styles
  gameIcon: {
    width: 48,
    height: 48,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gameEmoji: {
    fontSize: 24,
  },
  gameInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  gameName: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 2,
  },
  hostName: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.5)',
    marginTop: 2,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  sessionStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  sessionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  playerCount: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
  },
  playerAvatars: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  playerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(0, 212, 255, 0.3)',
    position: 'relative',
  },
  hostAvatar: {
    borderColor: '#FFD700',
  },
  playerInitial: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  hostBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FFD700',
    justifyContent: 'center',
    alignItems: 'center',
  },
  remainingAvatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  remainingText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  actions: {
    marginTop: 16,
  },
  actionRow: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  joinButton: {
    width: '100%',
  },
});
