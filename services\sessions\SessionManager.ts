import { EventEmitter } from '../../utils/EventEmitter';

export enum SessionStatus {
  WAITING = 'waiting',
  STARTING = 'starting',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum ConnectionStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
}

export interface SessionParticipant {
  userId: string;
  username: string;
  avatar?: string;
  connectionStatus: ConnectionStatus;
  joinedAt: number;
  isHost: boolean;
  deviceInfo?: {
    platform: string;
    model: string;
    version: string;
  };
}

export interface GameSession {
  id: string;
  gamePackageName: string;
  gameName: string;
  hostUserId: string;
  participants: Map<string, SessionParticipant>;
  status: SessionStatus;
  maxParticipants: number;
  isPrivate: boolean;
  inviteCode?: string;
  metadata: {
    gameMode?: string;
    difficulty?: string;
    customSettings?: Record<string, any>;
  };
  timestamps: {
    created: number;
    started?: number;
    paused?: number;
    resumed?: number;
    completed?: number;
  };
  duration: number; // in milliseconds
  networkConfig: {
    protocol: 'bluetooth' | 'wifi-direct' | 'local-network' | 'custom';
    port?: number;
    serverAddress?: string;
  };
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  completedSessions: number;
  totalPlaytime: number;
  averageSessionDuration: number;
  favoriteGames: Array<{ gamePackageName: string; sessionCount: number }>;
}

class SessionManager extends EventEmitter {
  private static instance: SessionManager;
  private activeSessions: Map<string, GameSession> = new Map();
  private userSessions: Map<string, string[]> = new Map(); // userId -> sessionIds
  private currentUserSession: string | null = null;
  private userId: string | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  constructor() {
    super();
    this.startCleanupTimer();
  }

  async initialize(userId: string): Promise<void> {
    this.userId = userId;
    await this.loadUserSessions();
    this.emit('initialized', { userId });
  }

  async createSession(config: {
    gamePackageName: string;
    gameName: string;
    maxParticipants: number;
    isPrivate?: boolean;
    gameMode?: string;
    difficulty?: string;
    networkConfig: GameSession['networkConfig'];
    customSettings?: Record<string, any>;
  }): Promise<GameSession> {
    if (!this.userId) {
      throw new Error('SessionManager not initialized');
    }

    const sessionId = this.generateSessionId();
    const inviteCode = config.isPrivate ? this.generateInviteCode() : undefined;

    const hostParticipant: SessionParticipant = {
      userId: this.userId,
      username: await this.getUserName(this.userId),
      connectionStatus: ConnectionStatus.CONNECTED,
      joinedAt: Date.now(),
      isHost: true,
      deviceInfo: await this.getDeviceInfo(),
    };

    const session: GameSession = {
      id: sessionId,
      gamePackageName: config.gamePackageName,
      gameName: config.gameName,
      hostUserId: this.userId,
      participants: new Map([[this.userId, hostParticipant]]),
      status: SessionStatus.WAITING,
      maxParticipants: config.maxParticipants,
      isPrivate: config.isPrivate || false,
      inviteCode,
      metadata: {
        gameMode: config.gameMode,
        difficulty: config.difficulty,
        customSettings: config.customSettings,
      },
      timestamps: {
        created: Date.now(),
      },
      duration: 0,
      networkConfig: config.networkConfig,
    };

    this.activeSessions.set(sessionId, session);
    this.addUserSession(this.userId, sessionId);
    this.currentUserSession = sessionId;

    this.emit('sessionCreated', session);
    this.emit('sessionUpdated', session);

    return session;
  }

  async joinSession(sessionId: string, inviteCode?: string): Promise<GameSession> {
    if (!this.userId) {
      throw new Error('SessionManager not initialized');
    }

    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.isPrivate && session.inviteCode !== inviteCode) {
      throw new Error('Invalid invite code');
    }

    if (session.participants.size >= session.maxParticipants) {
      throw new Error('Session is full');
    }

    if (session.status !== SessionStatus.WAITING) {
      throw new Error('Session is not accepting new participants');
    }

    const participant: SessionParticipant = {
      userId: this.userId,
      username: await this.getUserName(this.userId),
      connectionStatus: ConnectionStatus.CONNECTING,
      joinedAt: Date.now(),
      isHost: false,
      deviceInfo: await this.getDeviceInfo(),
    };

    session.participants.set(this.userId, participant);
    this.addUserSession(this.userId, sessionId);
    this.currentUserSession = sessionId;

    // Simulate connection process
    setTimeout(() => {
      participant.connectionStatus = ConnectionStatus.CONNECTED;
      this.emit('sessionUpdated', session);
      this.emit('participantJoined', { session, participant });
    }, 1000);

    this.emit('sessionUpdated', session);
    return session;
  }

  async leaveSession(sessionId: string): Promise<void> {
    if (!this.userId) {
      throw new Error('SessionManager not initialized');
    }

    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const participant = session.participants.get(this.userId);
    if (!participant) {
      throw new Error('User not in session');
    }

    session.participants.delete(this.userId);
    this.removeUserSession(this.userId, sessionId);

    if (this.currentUserSession === sessionId) {
      this.currentUserSession = null;
    }

    // If host leaves, transfer host or end session
    if (participant.isHost) {
      if (session.participants.size > 0) {
        const newHost = Array.from(session.participants.values())[0];
        newHost.isHost = true;
        session.hostUserId = newHost.userId;
        this.emit('hostTransferred', { session, newHost });
      } else {
        await this.endSession(sessionId, 'cancelled');
        return;
      }
    }

    this.emit('sessionUpdated', session);
    this.emit('participantLeft', { session, participant });
  }

  async startSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.hostUserId !== this.userId) {
      throw new Error('Only host can start session');
    }

    if (session.participants.size < 2) {
      throw new Error('Need at least 2 participants to start');
    }

    session.status = SessionStatus.STARTING;
    session.timestamps.started = Date.now();

    this.emit('sessionUpdated', session);
    this.emit('sessionStarting', session);

    // Simulate game launch delay
    setTimeout(() => {
      session.status = SessionStatus.ACTIVE;
      this.emit('sessionUpdated', session);
      this.emit('sessionStarted', session);
    }, 3000);
  }

  async pauseSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.hostUserId !== this.userId) {
      throw new Error('Only host can pause session');
    }

    session.status = SessionStatus.PAUSED;
    session.timestamps.paused = Date.now();

    this.emit('sessionUpdated', session);
    this.emit('sessionPaused', session);
  }

  async resumeSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.hostUserId !== this.userId) {
      throw new Error('Only host can resume session');
    }

    session.status = SessionStatus.ACTIVE;
    session.timestamps.resumed = Date.now();

    this.emit('sessionUpdated', session);
    this.emit('sessionResumed', session);
  }

  async endSession(sessionId: string, reason: 'completed' | 'cancelled'): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    session.status = reason === 'completed' ? SessionStatus.COMPLETED : SessionStatus.CANCELLED;
    session.timestamps.completed = Date.now();

    if (session.timestamps.started) {
      session.duration = session.timestamps.completed - session.timestamps.started;
    }

    // Remove from active sessions
    this.activeSessions.delete(sessionId);

    // Clear current session for all participants
    for (const participant of session.participants.values()) {
      this.removeUserSession(participant.userId, sessionId);
      if (this.currentUserSession === sessionId) {
        this.currentUserSession = null;
      }
    }

    this.emit('sessionEnded', { session, reason });
    await this.saveSessionHistory(session);
  }

  // Getters
  getCurrentUserSession(): GameSession | null {
    if (!this.currentUserSession) return null;
    return this.activeSessions.get(this.currentUserSession) || null;
  }

  getSession(sessionId: string): GameSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  getActiveSessions(): GameSession[] {
    return Array.from(this.activeSessions.values());
  }

  getUserActiveSessions(userId: string): GameSession[] {
    const sessionIds = this.userSessions.get(userId) || [];
    return sessionIds
      .map(id => this.activeSessions.get(id))
      .filter((session): session is GameSession => session !== null);
  }

  async getSessionStats(userId: string): Promise<SessionStats> {
    // This would typically query a database
    // For now, return mock data
    return {
      totalSessions: 42,
      activeSessions: this.getUserActiveSessions(userId).length,
      completedSessions: 38,
      totalPlaytime: 15600000, // 4.33 hours in ms
      averageSessionDuration: 410526, // ~6.8 minutes
      favoriteGames: [
        { gamePackageName: 'com.mojang.minecraftpe', sessionCount: 15 },
        { gamePackageName: 'com.innersloth.spacemafia', sessionCount: 12 },
        { gamePackageName: 'com.chess.com', sessionCount: 8 },
      ],
    };
  }

  // Private helper methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateInviteCode(): string {
    return Math.random().toString(36).substr(2, 6).toUpperCase();
  }

  private async getUserName(userId: string): Promise<string> {
    // This would typically fetch from user service
    return `User_${userId.substr(-4)}`;
  }

  private async getDeviceInfo(): Promise<SessionParticipant['deviceInfo']> {
    // This would use react-native-device-info or similar
    return {
      platform: 'android',
      model: 'Simulator',
      version: '1.0.0',
    };
  }

  private addUserSession(userId: string, sessionId: string): void {
    const sessions = this.userSessions.get(userId) || [];
    sessions.push(sessionId);
    this.userSessions.set(userId, sessions);
  }

  private removeUserSession(userId: string, sessionId: string): void {
    const sessions = this.userSessions.get(userId) || [];
    const index = sessions.indexOf(sessionId);
    if (index > -1) {
      sessions.splice(index, 1);
      this.userSessions.set(userId, sessions);
    }
  }

  private async loadUserSessions(): Promise<void> {
    // This would load from persistent storage
    console.log('Loading user sessions...');
  }

  private async saveSessionHistory(session: GameSession): Promise<void> {
    // This would save to persistent storage
    console.log('Saving session history:', session.id);
  }

  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleConnections();
    }, 30000); // Every 30 seconds
  }

  private cleanupStaleConnections(): void {
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    for (const [sessionId, session] of this.activeSessions.entries()) {
      let hasStaleConnections = false;

      for (const [userId, participant] of session.participants.entries()) {
        if (
          participant.connectionStatus === ConnectionStatus.DISCONNECTED &&
          now - participant.joinedAt > staleThreshold
        ) {
          session.participants.delete(userId);
          hasStaleConnections = true;
        }
      }

      if (hasStaleConnections) {
        if (session.participants.size === 0) {
          this.endSession(sessionId, 'cancelled');
        } else {
          this.emit('sessionUpdated', session);
        }
      }
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.activeSessions.clear();
    this.userSessions.clear();
    this.removeAllListeners();
  }
}

export default SessionManager.getInstance();
