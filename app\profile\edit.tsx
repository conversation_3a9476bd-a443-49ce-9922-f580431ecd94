import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';

import { RootState } from '../../store';
import { updateProfile } from '../../store/slices/userSlice';

export default function EditProfileScreen() {
  const dispatch = useDispatch();
  const userProfile = useSelector((state: RootState) => state.user.profile);
  
  const [name, setName] = useState(userProfile?.name || 'Player');
  const [deviceName, setDeviceName] = useState(userProfile?.deviceName || 'Unknown Device');
  const [status, setStatus] = useState(userProfile?.status || 'online');
  const [saving, setSaving] = useState(false);

  const statusOptions = [
    { value: 'online', label: 'Online', color: '#00FF88', icon: 'radio-button-on' },
    { value: 'away', label: 'Away', color: '#FFB800', icon: 'time' },
    { value: 'busy', label: 'Busy', color: '#FF4444', icon: 'remove-circle' },
    { value: 'offline', label: 'Offline', color: '#888888', icon: 'radio-button-off' },
  ];

  const handleSave = async () => {
    try {
      setSaving(true);
      
      dispatch(updateProfile({
        name: name.trim(),
        deviceName: deviceName.trim(),
        status: status as any,
      }));

      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const StatusOption = ({ option }: { option: typeof statusOptions[0] }) => (
    <TouchableOpacity
      style={[
        styles.statusOption,
        status === option.value && styles.statusOptionActive
      ]}
      onPress={() => setStatus(option.value)}
    >
      <BlurView intensity={15} style={styles.statusBlur}>
        <Ionicons 
          name={option.icon as any} 
          size={20} 
          color={status === option.value ? option.color : '#CCCCCC'} 
        />
        <Text style={[
          styles.statusLabel,
          status === option.value && { color: option.color }
        ]}>
          {option.label}
        </Text>
      </BlurView>
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Picture Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Picture</Text>
            <TouchableOpacity 
              style={styles.avatarContainer}
              onPress={() => router.push('/profile/avatar')}
            >
              <BlurView intensity={15} style={styles.avatarBlur}>
                <View style={styles.avatar}>
                  <Ionicons name="person" size={48} color="#00D4FF" />
                </View>
                <View style={styles.avatarOverlay}>
                  <Ionicons name="camera" size={24} color="#FFFFFF" />
                </View>
              </BlurView>
            </TouchableOpacity>
            <Text style={styles.avatarHint}>Tap to change avatar</Text>
          </View>

          {/* Basic Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Display Name</Text>
              <BlurView intensity={15} style={styles.inputBlur}>
                <TextInput
                  style={styles.textInput}
                  value={name}
                  onChangeText={setName}
                  placeholder="Enter your display name"
                  placeholderTextColor="#666"
                  maxLength={30}
                />
              </BlurView>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Device Name</Text>
              <BlurView intensity={15} style={styles.inputBlur}>
                <TextInput
                  style={styles.textInput}
                  value={deviceName}
                  onChangeText={setDeviceName}
                  placeholder="Enter device name"
                  placeholderTextColor="#666"
                  maxLength={50}
                />
              </BlurView>
            </View>
          </View>

          {/* Status Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Status</Text>
            <Text style={styles.sectionDescription}>
              Choose your online status to let others know your availability
            </Text>
            
            <View style={styles.statusGrid}>
              {statusOptions.map((option) => (
                <StatusOption key={option.value} option={option} />
              ))}
            </View>
          </View>

          {/* Profile Stats (Read-only) */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Stats</Text>
            <BlurView intensity={15} style={styles.statsCard}>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Level</Text>
                <Text style={styles.statValue}>{userProfile?.level || 1}</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Total Points</Text>
                <Text style={styles.statValue}>{userProfile?.totalPoints || 0}</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Achievements</Text>
                <Text style={styles.statValue}>{userProfile?.achievements?.length || 0}</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Friends</Text>
                <Text style={styles.statValue}>{userProfile?.friendCount || 0}</Text>
              </View>
            </BlurView>
          </View>

          {/* Save Button */}
          <TouchableOpacity
            style={[styles.saveButton, saving && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={saving}
          >
            <BlurView intensity={20} style={styles.saveBlur}>
              {saving ? (
                <Text style={styles.saveButtonText}>Saving...</Text>
              ) : (
                <>
                  <Ionicons name="checkmark" size={20} color="#000" />
                  <Text style={styles.saveButtonText}>Save Changes</Text>
                </>
              )}
            </BlurView>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 100,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 16,
    lineHeight: 20,
  },
  avatarContainer: {
    alignSelf: 'center',
    marginBottom: 8,
  },
  avatarBlur: {
    borderRadius: 60,
    overflow: 'hidden',
    position: 'relative',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#00D4FF',
  },
  avatarOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 212, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarHint: {
    fontSize: 12,
    color: '#CCCCCC',
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: '600',
  },
  inputBlur: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  textInput: {
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF',
    backgroundColor: 'transparent',
  },
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statusOption: {
    flex: 1,
    minWidth: '45%',
  },
  statusOptionActive: {
    transform: [{ scale: 1.02 }],
  },
  statusBlur: {
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  statusLabel: {
    fontSize: 14,
    color: '#CCCCCC',
    marginLeft: 8,
    fontWeight: '600',
  },
  statsCard: {
    borderRadius: 16,
    padding: 20,
    overflow: 'hidden',
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statLabel: {
    fontSize: 16,
    color: '#CCCCCC',
  },
  statValue: {
    fontSize: 16,
    color: '#00D4FF',
    fontWeight: 'bold',
  },
  saveButton: {
    marginTop: 20,
    marginBottom: 40,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveBlur: {
    borderRadius: 16,
    padding: 18,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00D4FF',
    overflow: 'hidden',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
