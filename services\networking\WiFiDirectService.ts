import { Platform, PermissionsAndroid } from 'react-native';
import {
  initialize,
  isSuccessfulInitialization,
  startPeerDiscovery,
  stopPeerDiscovery,
  getAvailablePeers,
  connect,
  disconnect,
  createGroup,
  removeGroup,
  getConnectionInfo,
  getGroupInfo,
  sendMessage,
  receiveMessage,
  getAvailablePeers as getCurrentPeers,
} from 'react-native-wifi-p2p';

export interface WiFiDirectPeer {
  deviceAddress: string;
  deviceName: string;
  primaryDeviceType?: string;
  secondaryDeviceType?: string;
  isGroupOwner?: boolean;
  status?: number;
}

export interface WiFiDirectGroup {
  interface: string;
  groupOwnerAddress: string;
  isGroupOwner: boolean;
  clients: WiFiDirectPeer[];
}

export interface WiFiDirectServiceInterface {
  initialize(): Promise<boolean>;
  isAvailable(): boolean;
  requestPermissions(): Promise<boolean>;
  startPeerDiscovery(): Promise<void>;
  stopPeerDiscovery(): Promise<void>;
  getAvailablePeers(): Promise<WiFiDirectPeer[]>;
  connectToPeer(peerAddress: string): Promise<boolean>;
  disconnect(): Promise<boolean>;
  createGroup(): Promise<boolean>;
  removeGroup(): Promise<boolean>;
  getConnectionInfo(): Promise<any>;
  getGroupInfo(): Promise<WiFiDirectGroup | null>;
  sendData(data: string): Promise<boolean>;
  onPeersChanged(callback: (peers: WiFiDirectPeer[]) => void): void;
  onConnectionChanged(callback: (connectionInfo: any) => void): void;
  onDataReceived(callback: (data: string) => void): void;
}

class WiFiDirectService implements WiFiDirectServiceInterface {
  private isInitialized = false;
  private isDiscovering = false;
  private availablePeers: WiFiDirectPeer[] = [];
  
  // Event callbacks
  private peersChangedCallback?: (peers: WiFiDirectPeer[]) => void;
  private connectionChangedCallback?: (connectionInfo: any) => void;
  private dataReceivedCallback?: (data: string) => void;

  constructor() {
    if (Platform.OS === 'android') {
      this.setupEventListeners();
    }
  }

  private setupEventListeners(): void {
    // Set up WiFi Direct event listeners
    // Note: react-native-wifi-p2p uses DeviceEventEmitter for events
    const { DeviceEventEmitter } = require('react-native');

    DeviceEventEmitter.addListener('PEERS_UPDATED', (event: any) => {
      this.availablePeers = event.devices || [];
      this.peersChangedCallback?.(this.availablePeers);
    });

    DeviceEventEmitter.addListener('CONNECTION_INFO_UPDATED', (event: any) => {
      this.connectionChangedCallback?.(event);
    });

    DeviceEventEmitter.addListener('MESSAGE_RECEIVED', (event: any) => {
      this.dataReceivedCallback?.(event.message);
    });
  }

  async initialize(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.warn('WiFi Direct is only available on Android');
      return false;
    }

    try {
      await initialize();
      this.isInitialized = await isSuccessfulInitialization();
      return this.isInitialized;
    } catch (error) {
      console.error('Error initializing WiFi Direct:', error);
      return false;
    }
  }

  isAvailable(): boolean {
    return Platform.OS === 'android' && this.isInitialized;
  }

  async requestPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_WIFI_STATE,
        PermissionsAndroid.PERMISSIONS.CHANGE_WIFI_STATE,
      ];

      // For Android 13+ (API 33+)
      if (Platform.Version >= 33) {
        permissions.push(PermissionsAndroid.PERMISSIONS.NEARBY_WIFI_DEVICES);
      }

      const granted = await PermissionsAndroid.requestMultiple(permissions);
      
      return Object.values(granted).every(
        permission => permission === PermissionsAndroid.RESULTS.GRANTED
      );
    } catch (error) {
      console.error('Error requesting WiFi Direct permissions:', error);
      return false;
    }
  }

  async startPeerDiscovery(): Promise<void> {
    if (!this.isAvailable()) {
      throw new Error('WiFi Direct is not available');
    }

    try {
      if (this.isDiscovering) {
        await this.stopPeerDiscovery();
      }
      
      await startPeerDiscovery();
      this.isDiscovering = true;
    } catch (error) {
      console.error('Error starting peer discovery:', error);
      throw error;
    }
  }

  async stopPeerDiscovery(): Promise<void> {
    if (!this.isAvailable()) {
      return;
    }

    try {
      if (this.isDiscovering) {
        await stopPeerDiscovery();
        this.isDiscovering = false;
      }
    } catch (error) {
      console.error('Error stopping peer discovery:', error);
      throw error;
    }
  }

  async getAvailablePeers(): Promise<WiFiDirectPeer[]> {
    if (!this.isAvailable()) {
      return [];
    }

    try {
      const peers = await getCurrentPeers();
      this.availablePeers = peers || [];
      return this.availablePeers;
    } catch (error) {
      console.error('Error getting available peers:', error);
      return [];
    }
  }

  async connectToPeer(peerAddress: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await connect(peerAddress);
      return true;
    } catch (error) {
      console.error('Error connecting to peer:', error);
      return false;
    }
  }

  async disconnect(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await disconnect();
      return true;
    } catch (error) {
      console.error('Error disconnecting:', error);
      return false;
    }
  }

  async createGroup(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await createGroup();
      return true;
    } catch (error) {
      console.error('Error creating group:', error);
      return false;
    }
  }

  async removeGroup(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await removeGroup();
      return true;
    } catch (error) {
      console.error('Error removing group:', error);
      return false;
    }
  }

  async getConnectionInfo(): Promise<any> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      return await getConnectionInfo();
    } catch (error) {
      console.error('Error getting connection info:', error);
      return null;
    }
  }

  async getGroupInfo(): Promise<WiFiDirectGroup | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      return await getGroupInfo();
    } catch (error) {
      console.error('Error getting group info:', error);
      return null;
    }
  }

  async sendData(data: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await sendMessage(data);
      return true;
    } catch (error) {
      console.error('Error sending data:', error);
      return false;
    }
  }

  onPeersChanged(callback: (peers: WiFiDirectPeer[]) => void): void {
    this.peersChangedCallback = callback;
  }

  onConnectionChanged(callback: (connectionInfo: any) => void): void {
    this.connectionChangedCallback = callback;
  }

  onDataReceived(callback: (data: string) => void): void {
    this.dataReceivedCallback = callback;
  }
}

export default new WiFiDirectService();
