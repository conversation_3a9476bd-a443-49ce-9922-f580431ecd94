import { Platform } from 'react-native';
import { GameInfo } from './GameDetector';

export interface CompatibilityCheck {
  isCompatible: boolean;
  score: number; // 0-100, higher is better
  issues: CompatibilityIssue[];
  recommendations: string[];
  requirements: GameRequirements;
}

export interface CompatibilityIssue {
  type: 'error' | 'warning' | 'info';
  category: 'network' | 'device' | 'version' | 'players' | 'permissions';
  message: string;
  solution?: string;
}

export interface GameRequirements {
  minPlayers: number;
  maxPlayers: number;
  networkTypes: ('wifi' | 'bluetooth' | 'cellular')[];
  platformSupport: ('ios' | 'android')[];
  minVersion?: string;
  permissions: string[];
  features: string[];
}

export interface SessionRequirements {
  playerCount: number;
  networkType: 'wifi' | 'bluetooth' | 'cellular' | 'local';
  devicePlatforms: string[];
  gameVersions: { [deviceId: string]: string };
}

class GameCompatibility {
  private static instance: GameCompatibility;
  private compatibilityRules: Map<string, GameRequirements> = new Map();

  static getInstance(): GameCompatibility {
    if (!GameCompatibility.instance) {
      GameCompatibility.instance = new GameCompatibility();
    }
    return GameCompatibility.instance;
  }

  constructor() {
    this.initializeCompatibilityRules();
  }

  private initializeCompatibilityRules() {
    // Define compatibility rules for known games
    this.compatibilityRules.set('com.mojang.minecraftpe', {
      minPlayers: 1,
      maxPlayers: 8,
      networkTypes: ['wifi', 'bluetooth'],
      platformSupport: ['ios', 'android'],
      minVersion: '1.19.0',
      permissions: ['INTERNET', 'ACCESS_NETWORK_STATE', 'BLUETOOTH'],
      features: ['cross-platform', 'world-sync', 'voice-chat'],
    });

    this.compatibilityRules.set('com.innersloth.spacemafia', {
      minPlayers: 4,
      maxPlayers: 15,
      networkTypes: ['wifi', 'bluetooth'],
      platformSupport: ['ios', 'android'],
      minVersion: '2023.3.28',
      permissions: ['INTERNET', 'ACCESS_NETWORK_STATE'],
      features: ['cross-platform', 'text-chat'],
    });

    this.compatibilityRules.set('com.chess.com', {
      minPlayers: 2,
      maxPlayers: 2,
      networkTypes: ['wifi', 'bluetooth', 'cellular'],
      platformSupport: ['ios', 'android'],
      permissions: ['INTERNET'],
      features: ['cross-platform', 'turn-based'],
    });

    this.compatibilityRules.set('com.ea.game.pvzfree_row', {
      minPlayers: 1,
      maxPlayers: 2,
      networkTypes: ['bluetooth'],
      platformSupport: ['ios', 'android'],
      permissions: ['BLUETOOTH', 'BLUETOOTH_ADMIN'],
      features: ['local-only', 'real-time'],
    });

    this.compatibilityRules.set('com.scopely.monopolygo', {
      minPlayers: 2,
      maxPlayers: 6,
      networkTypes: ['wifi', 'bluetooth'],
      platformSupport: ['ios', 'android'],
      permissions: ['BLUETOOTH', 'ACCESS_NETWORK_STATE'],
      features: ['turn-based', 'save-state'],
    });
  }

  async checkGameCompatibility(
    game: GameInfo,
    sessionRequirements: SessionRequirements
  ): Promise<CompatibilityCheck> {
    const rules = this.compatibilityRules.get(game.packageName);

    if (!rules) {
      return this.createUnknownGameCompatibility(game, sessionRequirements);
    }

    const issues: CompatibilityIssue[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check player count compatibility
    const playerCheck = this.checkPlayerCount(rules, sessionRequirements);
    if (!playerCheck.isValid) {
      issues.push(...playerCheck.issues);
      score -= playerCheck.penalty;
    }

    // Check network compatibility
    const networkCheck = this.checkNetworkCompatibility(rules, sessionRequirements);
    if (!networkCheck.isValid) {
      issues.push(...networkCheck.issues);
      score -= networkCheck.penalty;
    }

    // Check platform compatibility
    const platformCheck = this.checkPlatformCompatibility(rules, sessionRequirements);
    if (!platformCheck.isValid) {
      issues.push(...platformCheck.issues);
      score -= platformCheck.penalty;
    }

    // Check version compatibility
    const versionCheck = this.checkVersionCompatibility(rules, sessionRequirements);
    if (!versionCheck.isValid) {
      issues.push(...versionCheck.issues);
      score -= versionCheck.penalty;
    }

    // Generate recommendations
    recommendations.push(...this.generateRecommendations(game, rules, issues));

    const isCompatible = issues.filter(issue => issue.type === 'error').length === 0;

    return {
      isCompatible,
      score: Math.max(0, score),
      issues,
      recommendations,
      requirements: rules,
    };
  }

  private checkPlayerCount(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    if (session.playerCount < rules.minPlayers) {
      issues.push({
        type: 'error',
        category: 'players',
        message: `Not enough players. Need at least ${rules.minPlayers}, have ${session.playerCount}`,
        solution: `Invite ${rules.minPlayers - session.playerCount} more player(s)`,
      });
      penalty = 50;
    } else if (session.playerCount > rules.maxPlayers) {
      issues.push({
        type: 'error',
        category: 'players',
        message: `Too many players. Maximum ${rules.maxPlayers}, have ${session.playerCount}`,
        solution: `Remove ${session.playerCount - rules.maxPlayers} player(s)`,
      });
      penalty = 30;
    }

    return { isValid: issues.length === 0, issues, penalty };
  }

  private checkNetworkCompatibility(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    if (!rules.networkTypes.includes(session.networkType as any)) {
      issues.push({
        type: 'error',
        category: 'network',
        message: `Network type '${session.networkType}' not supported`,
        solution: `Use one of: ${rules.networkTypes.join(', ')}`,
      });
      penalty = 40;
    }

    // Check for optimal network type
    if (session.networkType === 'cellular' && rules.networkTypes.includes('wifi')) {
      issues.push({
        type: 'warning',
        category: 'network',
        message: 'WiFi recommended for better performance',
        solution: 'Connect to WiFi for optimal experience',
      });
      penalty = 10;
    }

    return { isValid: issues.filter(i => i.type === 'error').length === 0, issues, penalty };
  }

  private checkPlatformCompatibility(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    const unsupportedPlatforms = session.devicePlatforms.filter(
      platform => !rules.platformSupport.includes(platform as any)
    );

    if (unsupportedPlatforms.length > 0) {
      issues.push({
        type: 'error',
        category: 'device',
        message: `Unsupported platforms: ${unsupportedPlatforms.join(', ')}`,
        solution: 'All players need compatible devices',
      });
      penalty = 30;
    }

    // Check for mixed platforms (might have compatibility issues)
    const uniquePlatforms = new Set(session.devicePlatforms);
    if (uniquePlatforms.size > 1) {
      issues.push({
        type: 'info',
        category: 'device',
        message: 'Mixed platforms detected (iOS/Android)',
        solution: 'Cross-platform play may have limitations',
      });
      penalty = 5;
    }

    return { isValid: issues.filter(i => i.type === 'error').length === 0, issues, penalty };
  }

  private checkVersionCompatibility(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    if (rules.minVersion) {
      const versions = Object.values(session.gameVersions);
      const outdatedVersions = versions.filter(version =>
        this.compareVersions(version, rules.minVersion!) < 0
      );

      if (outdatedVersions.length > 0) {
        issues.push({
          type: 'warning',
          category: 'version',
          message: `Some players have outdated game versions`,
          solution: 'Update game to latest version for best compatibility',
        });
        penalty = 15;
      }
    }

    // Check for version mismatches
    const versions = Object.values(session.gameVersions);
    const uniqueVersions = new Set(versions);
    if (uniqueVersions.size > 1) {
      issues.push({
        type: 'warning',
        category: 'version',
        message: 'Players have different game versions',
        solution: 'Ensure all players have the same game version',
      });
      penalty = 10;
    }

    return { isValid: issues.filter(i => i.type === 'error').length === 0, issues, penalty };
  }

  private generateRecommendations(
    game: GameInfo,
    rules: GameRequirements,
    issues: CompatibilityIssue[]
  ): string[] {
    const recommendations: string[] = [];

    // Add game-specific recommendations
    if (rules.features.includes('voice-chat')) {
      recommendations.push('Enable microphone permissions for voice chat');
    }

    if (rules.features.includes('cross-platform')) {
      recommendations.push('Cross-platform play supported - invite friends on any device');
    }

    if (rules.networkTypes.includes('bluetooth') && rules.networkTypes.length === 1) {
      recommendations.push('Ensure Bluetooth is enabled and devices are close together');
    }

    if (rules.features.includes('turn-based')) {
      recommendations.push('Turn-based game - connection stability less critical');
    }

    // Add issue-specific recommendations
    const hasNetworkIssues = issues.some(i => i.category === 'network');
    if (hasNetworkIssues) {
      recommendations.push('Check network settings and try different connection methods');
    }

    return recommendations;
  }

  private createUnknownGameCompatibility(
    game: GameInfo,
    session: SessionRequirements
  ): CompatibilityCheck {
    const issues: CompatibilityIssue[] = [{
      type: 'warning',
      category: 'version',
      message: 'Game compatibility unknown',
      solution: 'Test connection manually to verify compatibility',
    }];

    return {
      isCompatible: true,
      score: 70, // Neutral score for unknown games
      issues,
      recommendations: [
        'This game is not in our compatibility database',
        'Try connecting and report any issues to help improve compatibility',
      ],
      requirements: {
        minPlayers: 1,
        maxPlayers: 10,
        networkTypes: ['wifi', 'bluetooth'],
        platformSupport: ['ios', 'android'],
        permissions: [],
        features: [],
      },
    };
  }

  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    const maxLength = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }

    return 0;
  }

  async getOptimalNetworkType(
    game: GameInfo,
    availableNetworks: string[]
  ): Promise<string | null> {
    const rules = this.compatibilityRules.get(game.packageName);
    if (!rules) return availableNetworks[0] || null;

    // Find the best available network type
    const preferredOrder = ['wifi', 'bluetooth', 'cellular'];

    for (const networkType of preferredOrder) {
      if (rules.networkTypes.includes(networkType as any) &&
          availableNetworks.includes(networkType)) {
        return networkType;
      }
    }

    return null;
  }

  async getCompatibilityScore(
    game: GameInfo,
    session: SessionRequirements
  ): Promise<number> {
    const check = await this.checkGameCompatibility(game, session);
    return check.score;
  }

  async isGameSupported(packageName: string): Promise<boolean> {
    return this.compatibilityRules.has(packageName);
  }

  async getSupportedGames(): Promise<string[]> {
    return Array.from(this.compatibilityRules.keys());
  }

  // Real-time compatibility verification
  async verifyRealTimeCompatibility(
    localGame: GameInfo,
    remoteDevice: { deviceId: string; games: GameInfo[]; deviceInfo?: any }
  ): Promise<{
    compatible: boolean;
    latency: number;
    bandwidth: number;
    versionMatch: boolean;
    networkQuality: 'excellent' | 'good' | 'fair' | 'poor';
    issues: string[];
  }> {
    try {
      // Perform real-time network tests
      const networkTest = await this.performNetworkTest(remoteDevice.deviceId);

      // Check version compatibility
      const versionCheck = await this.checkVersionCompatibilityRealTime(localGame, remoteDevice);

      // Assess overall network quality
      const networkQuality = this.assessNetworkQuality(networkTest.latency, networkTest.bandwidth);

      const issues: string[] = [];

      if (networkTest.latency > 100) {
        issues.push('High latency detected - may cause lag');
      }

      if (networkTest.bandwidth < 1000) { // Less than 1 Mbps
        issues.push('Low bandwidth - may affect game performance');
      }

      if (!versionCheck.compatible) {
        issues.push(`Version mismatch: Local ${versionCheck.localVersion} vs Remote ${versionCheck.remoteVersion}`);
      }

      const compatible = networkTest.latency < 150 &&
                        networkTest.bandwidth > 500 &&
                        versionCheck.compatible;

      return {
        compatible,
        latency: networkTest.latency,
        bandwidth: networkTest.bandwidth,
        versionMatch: versionCheck.compatible,
        networkQuality,
        issues
      };
    } catch (error) {
      console.error('Real-time compatibility check failed:', error);
      return {
        compatible: false,
        latency: -1,
        bandwidth: -1,
        versionMatch: false,
        networkQuality: 'poor',
        issues: ['Failed to perform compatibility check']
      };
    }
  }

  private async performNetworkTest(deviceId: string): Promise<{ latency: number; bandwidth: number }> {
    try {
      // Simulate network testing - in real implementation this would:
      // - Send ping packets to measure latency
      // - Transfer test data to measure bandwidth
      // - Use WebRTC or similar for peer-to-peer testing

      const startTime = Date.now();

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));

      const latency = Date.now() - startTime + Math.random() * 80; // 10-130ms
      const bandwidth = Math.random() * 10000 + 1000; // 1-11 Mbps

      return { latency, bandwidth };
    } catch (error) {
      console.error('Network test failed:', error);
      return { latency: 999, bandwidth: 0 };
    }
  }

  private async checkVersionCompatibilityRealTime(
    localGame: GameInfo,
    remoteDevice: { games: GameInfo[] }
  ): Promise<{ compatible: boolean; localVersion: string; remoteVersion: string }> {
    const remoteGame = remoteDevice.games.find(g => g.packageName === localGame.packageName);

    if (!remoteGame) {
      return {
        compatible: false,
        localVersion: localGame.version || 'unknown',
        remoteVersion: 'not installed'
      };
    }

    const localVersion = localGame.version || '1.0.0';
    const remoteVersion = remoteGame.version || '1.0.0';

    // Enhanced version compatibility check
    const localMajor = parseInt(localVersion.split('.')[0]);
    const remoteMajor = parseInt(remoteVersion.split('.')[0]);
    const localMinor = parseInt(localVersion.split('.')[1] || '0');
    const remoteMinor = parseInt(remoteVersion.split('.')[1] || '0');

    // Compatible if major versions match and minor versions are within 1
    const compatible = localMajor === remoteMajor && Math.abs(localMinor - remoteMinor) <= 1;

    return {
      compatible,
      localVersion,
      remoteVersion
    };
  }

  private assessNetworkQuality(latency: number, bandwidth: number): 'excellent' | 'good' | 'fair' | 'poor' {
    if (latency < 30 && bandwidth > 5000) return 'excellent';
    if (latency < 60 && bandwidth > 2000) return 'good';
    if (latency < 100 && bandwidth > 1000) return 'fair';
    return 'poor';
  }

  async monitorConnectionQuality(
    deviceId: string,
    duration: number = 30000
  ): Promise<{
    averageLatency: number;
    averageBandwidth: number;
    packetLoss: number;
    stability: 'stable' | 'unstable' | 'poor';
  }> {
    const measurements: Array<{ latency: number; bandwidth: number }> = [];
    const interval = 2000; // Test every 2 seconds
    const testCount = Math.floor(duration / interval);

    for (let i = 0; i < testCount; i++) {
      try {
        const measurement = await this.performNetworkTest(deviceId);
        measurements.push(measurement);

        if (i < testCount - 1) {
          await new Promise(resolve => setTimeout(resolve, interval));
        }
      } catch (error) {
        console.error('Network measurement failed:', error);
      }
    }

    if (measurements.length === 0) {
      return {
        averageLatency: -1,
        averageBandwidth: -1,
        packetLoss: 100,
        stability: 'poor'
      };
    }

    const averageLatency = measurements.reduce((sum, m) => sum + m.latency, 0) / measurements.length;
    const averageBandwidth = measurements.reduce((sum, m) => sum + m.bandwidth, 0) / measurements.length;

    // Calculate stability based on variance
    const latencyVariance = measurements.reduce((sum, m) => sum + Math.pow(m.latency - averageLatency, 2), 0) / measurements.length;
    const latencyStdDev = Math.sqrt(latencyVariance);

    let stability: 'stable' | 'unstable' | 'poor';
    if (latencyStdDev < 10) stability = 'stable';
    else if (latencyStdDev < 30) stability = 'unstable';
    else stability = 'poor';

    // Simulate packet loss (in real implementation, this would be measured)
    const packetLoss = Math.random() * 5; // 0-5% packet loss

    return {
      averageLatency,
      averageBandwidth,
      packetLoss,
      stability
    };
  }
}

export default GameCompatibility.getInstance();
