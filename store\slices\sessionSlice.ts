import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import SessionManager, { GameSession, SessionStatus, SessionStats } from '../../services/sessions/SessionManager';

export interface SessionState {
  currentSession: GameSession | null;
  activeSessions: GameSession[];
  userSessions: GameSession[];
  sessionStats: SessionStats | null;
  loading: boolean;
  error: string | null;
  isInitialized: boolean;
}

const initialState: SessionState = {
  currentSession: null,
  activeSessions: [],
  userSessions: [],
  sessionStats: null,
  loading: false,
  error: null,
  isInitialized: false,
};

// Async thunks
export const initializeSessionManager = createAsyncThunk(
  'session/initialize',
  async (userId: string, { rejectWithValue }) => {
    try {
      await SessionManager.initialize(userId);
      const stats = await SessionManager.getSessionStats(userId);
      return { userId, stats };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to initialize session manager');
    }
  }
);

export const createSession = createAsyncThunk(
  'session/create',
  async (config: {
    gamePackageName: string;
    gameName: string;
    maxParticipants: number;
    isPrivate?: boolean;
    gameMode?: string;
    difficulty?: string;
    networkConfig: GameSession['networkConfig'];
    customSettings?: Record<string, any>;
  }, { rejectWithValue }) => {
    try {
      const session = await SessionManager.createSession(config);
      return session;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create session');
    }
  }
);

export const joinSession = createAsyncThunk(
  'session/join',
  async ({ sessionId, inviteCode }: { sessionId: string; inviteCode?: string }, { rejectWithValue }) => {
    try {
      const session = await SessionManager.joinSession(sessionId, inviteCode);
      return session;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to join session');
    }
  }
);

export const leaveSession = createAsyncThunk(
  'session/leave',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      await SessionManager.leaveSession(sessionId);
      return sessionId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to leave session');
    }
  }
);

export const startSession = createAsyncThunk(
  'session/start',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      await SessionManager.startSession(sessionId);
      return sessionId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to start session');
    }
  }
);

export const pauseSession = createAsyncThunk(
  'session/pause',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      await SessionManager.pauseSession(sessionId);
      return sessionId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to pause session');
    }
  }
);

export const resumeSession = createAsyncThunk(
  'session/resume',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      await SessionManager.resumeSession(sessionId);
      return sessionId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to resume session');
    }
  }
);

export const endSession = createAsyncThunk(
  'session/end',
  async ({ sessionId, reason }: { sessionId: string; reason: 'completed' | 'cancelled' }, { rejectWithValue }) => {
    try {
      await SessionManager.endSession(sessionId, reason);
      return { sessionId, reason };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to end session');
    }
  }
);

export const refreshActiveSessions = createAsyncThunk(
  'session/refreshActive',
  async (_, { rejectWithValue }) => {
    try {
      const activeSessions = SessionManager.getActiveSessions();
      return activeSessions;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh sessions');
    }
  }
);

export const refreshUserSessions = createAsyncThunk(
  'session/refreshUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const userSessions = SessionManager.getUserActiveSessions(userId);
      return userSessions;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh user sessions');
    }
  }
);

const sessionSlice = createSlice({
  name: 'session',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateSession: (state, action: PayloadAction<GameSession>) => {
      const updatedSession = action.payload;
      
      // Update current session if it matches
      if (state.currentSession?.id === updatedSession.id) {
        state.currentSession = updatedSession;
      }
      
      // Update in active sessions
      const activeIndex = state.activeSessions.findIndex(s => s.id === updatedSession.id);
      if (activeIndex !== -1) {
        state.activeSessions[activeIndex] = updatedSession;
      }
      
      // Update in user sessions
      const userIndex = state.userSessions.findIndex(s => s.id === updatedSession.id);
      if (userIndex !== -1) {
        state.userSessions[userIndex] = updatedSession;
      }
    },
    removeSession: (state, action: PayloadAction<string>) => {
      const sessionId = action.payload;
      
      // Clear current session if it matches
      if (state.currentSession?.id === sessionId) {
        state.currentSession = null;
      }
      
      // Remove from active sessions
      state.activeSessions = state.activeSessions.filter(s => s.id !== sessionId);
      
      // Remove from user sessions
      state.userSessions = state.userSessions.filter(s => s.id !== sessionId);
    },
    setCurrentSession: (state, action: PayloadAction<GameSession | null>) => {
      state.currentSession = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize
      .addCase(initializeSessionManager.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(initializeSessionManager.fulfilled, (state, action) => {
        state.loading = false;
        state.isInitialized = true;
        state.sessionStats = action.payload.stats;
      })
      .addCase(initializeSessionManager.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create session
      .addCase(createSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSession.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSession = action.payload;
        state.activeSessions.push(action.payload);
        state.userSessions.push(action.payload);
      })
      .addCase(createSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Join session
      .addCase(joinSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(joinSession.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSession = action.payload;
        
        // Update or add to user sessions
        const existingIndex = state.userSessions.findIndex(s => s.id === action.payload.id);
        if (existingIndex !== -1) {
          state.userSessions[existingIndex] = action.payload;
        } else {
          state.userSessions.push(action.payload);
        }
      })
      .addCase(joinSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Leave session
      .addCase(leaveSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(leaveSession.fulfilled, (state, action) => {
        state.loading = false;
        const sessionId = action.payload;
        
        if (state.currentSession?.id === sessionId) {
          state.currentSession = null;
        }
        
        state.userSessions = state.userSessions.filter(s => s.id !== sessionId);
      })
      .addCase(leaveSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Start session
      .addCase(startSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startSession.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(startSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // End session
      .addCase(endSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(endSession.fulfilled, (state, action) => {
        state.loading = false;
        const { sessionId } = action.payload;
        
        if (state.currentSession?.id === sessionId) {
          state.currentSession = null;
        }
        
        state.activeSessions = state.activeSessions.filter(s => s.id !== sessionId);
        state.userSessions = state.userSessions.filter(s => s.id !== sessionId);
      })
      .addCase(endSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Refresh active sessions
      .addCase(refreshActiveSessions.fulfilled, (state, action) => {
        state.activeSessions = action.payload;
      })
      
      // Refresh user sessions
      .addCase(refreshUserSessions.fulfilled, (state, action) => {
        state.userSessions = action.payload;
      });
  },
});

export const { clearError, updateSession, removeSession, setCurrentSession } = sessionSlice.actions;
export default sessionSlice.reducer;
