import { Platform } from 'react-native';
import NetworkService from '../NetworkService';

export interface TroubleshootingResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  details?: string;
  suggestion?: string;
}

export interface TroubleshootingReport {
  timestamp: number;
  deviceInfo: {
    platform: string;
    version: string;
    model?: string;
  };
  results: TroubleshootingResult[];
  overallStatus: 'healthy' | 'issues' | 'critical';
  recommendations: string[];
}

class TroubleshootingService {
  private static instance: TroubleshootingService;

  static getInstance(): TroubleshootingService {
    if (!TroubleshootingService.instance) {
      TroubleshootingService.instance = new TroubleshootingService();
    }
    return TroubleshootingService.instance;
  }

  async runDiagnostics(): Promise<TroubleshootingReport> {
    const results: TroubleshootingResult[] = [];
    
    // Run all diagnostic tests
    results.push(await this.testNetworkStatus());
    results.push(await this.testBluetoothStatus());
    results.push(await this.testWiFiStatus());
    results.push(await this.testPermissions());
    results.push(await this.testDeviceCompatibility());
    results.push(await this.testNetworkConnectivity());
    results.push(await this.testPortAvailability());

    // Determine overall status
    const overallStatus = this.determineOverallStatus(results);
    const recommendations = this.generateRecommendations(results);

    return {
      timestamp: Date.now(),
      deviceInfo: {
        platform: Platform.OS,
        version: Platform.Version.toString(),
      },
      results,
      overallStatus,
      recommendations,
    };
  }

  private async testNetworkStatus(): Promise<TroubleshootingResult> {
    try {
      const status = await NetworkService.getNetworkStatus();
      
      if (status.bluetooth && status.wifi) {
        return {
          test: 'Network Status',
          status: 'pass',
          message: 'All network features are available',
          details: 'Bluetooth and WiFi are both enabled',
        };
      } else if (status.bluetooth || status.wifi) {
        return {
          test: 'Network Status',
          status: 'warning',
          message: 'Some network features are unavailable',
          details: `Bluetooth: ${status.bluetooth ? 'Enabled' : 'Disabled'}, WiFi: ${status.wifi ? 'Enabled' : 'Disabled'}`,
          suggestion: 'Enable both Bluetooth and WiFi for best connectivity options',
        };
      } else {
        return {
          test: 'Network Status',
          status: 'fail',
          message: 'No network features available',
          details: 'Both Bluetooth and WiFi are disabled',
          suggestion: 'Enable Bluetooth or WiFi to use LoGaCo',
        };
      }
    } catch (error) {
      return {
        test: 'Network Status',
        status: 'fail',
        message: 'Failed to check network status',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Restart the app and try again',
      };
    }
  }

  private async testBluetoothStatus(): Promise<TroubleshootingResult> {
    try {
      const isEnabled = await NetworkService.isBluetoothEnabled();
      
      if (isEnabled) {
        return {
          test: 'Bluetooth',
          status: 'pass',
          message: 'Bluetooth is enabled and ready',
        };
      } else {
        return {
          test: 'Bluetooth',
          status: 'warning',
          message: 'Bluetooth is disabled',
          suggestion: 'Enable Bluetooth in device settings for local connections',
        };
      }
    } catch (error) {
      return {
        test: 'Bluetooth',
        status: 'fail',
        message: 'Cannot access Bluetooth',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Check if Bluetooth is supported on this device',
      };
    }
  }

  private async testWiFiStatus(): Promise<TroubleshootingResult> {
    try {
      const isEnabled = await NetworkService.isWiFiEnabled();
      
      if (isEnabled) {
        return {
          test: 'WiFi',
          status: 'pass',
          message: 'WiFi is enabled and ready',
        };
      } else {
        return {
          test: 'WiFi',
          status: 'warning',
          message: 'WiFi is disabled',
          suggestion: 'Enable WiFi for network-based connections',
        };
      }
    } catch (error) {
      return {
        test: 'WiFi',
        status: 'fail',
        message: 'Cannot access WiFi',
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Check WiFi settings and try again',
      };
    }
  }

  private async testPermissions(): Promise<TroubleshootingResult> {
    try {
      // This would check actual permissions in a real implementation
      const hasLocationPermission = true; // Mock
      const hasCameraPermission = true; // Mock
      
      const missingPermissions = [];
      if (!hasLocationPermission) missingPermissions.push('Location');
      if (!hasCameraPermission) missingPermissions.push('Camera');
      
      if (missingPermissions.length === 0) {
        return {
          test: 'Permissions',
          status: 'pass',
          message: 'All required permissions granted',
        };
      } else {
        return {
          test: 'Permissions',
          status: 'warning',
          message: `Missing permissions: ${missingPermissions.join(', ')}`,
          suggestion: 'Grant missing permissions in app settings',
        };
      }
    } catch (error) {
      return {
        test: 'Permissions',
        status: 'fail',
        message: 'Failed to check permissions',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async testDeviceCompatibility(): Promise<TroubleshootingResult> {
    try {
      const isCompatible = Platform.OS === 'ios' || Platform.OS === 'android';
      
      if (isCompatible) {
        return {
          test: 'Device Compatibility',
          status: 'pass',
          message: 'Device is compatible with LoGaCo',
          details: `Platform: ${Platform.OS} ${Platform.Version}`,
        };
      } else {
        return {
          test: 'Device Compatibility',
          status: 'fail',
          message: 'Device platform not supported',
          details: `Platform: ${Platform.OS}`,
          suggestion: 'LoGaCo requires iOS or Android',
        };
      }
    } catch (error) {
      return {
        test: 'Device Compatibility',
        status: 'fail',
        message: 'Failed to check device compatibility',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async testNetworkConnectivity(): Promise<TroubleshootingResult> {
    try {
      // Mock network connectivity test
      const isConnected = Math.random() > 0.1; // 90% success rate
      
      if (isConnected) {
        return {
          test: 'Network Connectivity',
          status: 'pass',
          message: 'Network connectivity is working',
        };
      } else {
        return {
          test: 'Network Connectivity',
          status: 'fail',
          message: 'Network connectivity issues detected',
          suggestion: 'Check your internet connection and try again',
        };
      }
    } catch (error) {
      return {
        test: 'Network Connectivity',
        status: 'fail',
        message: 'Failed to test network connectivity',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async testPortAvailability(): Promise<TroubleshootingResult> {
    try {
      // Mock port availability test
      const portsAvailable = Math.random() > 0.2; // 80% success rate
      
      if (portsAvailable) {
        return {
          test: 'Port Availability',
          status: 'pass',
          message: 'Required network ports are available',
          details: 'Ports 8080-8090 are accessible',
        };
      } else {
        return {
          test: 'Port Availability',
          status: 'warning',
          message: 'Some network ports may be blocked',
          details: 'Firewall or network restrictions detected',
          suggestion: 'Check firewall settings or try a different network',
        };
      }
    } catch (error) {
      return {
        test: 'Port Availability',
        status: 'fail',
        message: 'Failed to test port availability',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private determineOverallStatus(results: TroubleshootingResult[]): 'healthy' | 'issues' | 'critical' {
    const failCount = results.filter(r => r.status === 'fail').length;
    const warningCount = results.filter(r => r.status === 'warning').length;
    
    if (failCount > 0) {
      return 'critical';
    } else if (warningCount > 2) {
      return 'issues';
    } else {
      return 'healthy';
    }
  }

  private generateRecommendations(results: TroubleshootingResult[]): string[] {
    const recommendations: string[] = [];
    
    results.forEach(result => {
      if (result.suggestion) {
        recommendations.push(result.suggestion);
      }
    });
    
    // Add general recommendations
    if (recommendations.length === 0) {
      recommendations.push('Your device is ready for LoGaCo connections!');
    } else {
      recommendations.unshift('Follow these steps to improve connectivity:');
    }
    
    return recommendations;
  }

  async testSpecificConnection(deviceId: string): Promise<TroubleshootingResult> {
    try {
      // Mock specific connection test
      const connectionSuccessful = Math.random() > 0.3; // 70% success rate
      
      if (connectionSuccessful) {
        return {
          test: `Connection to ${deviceId}`,
          status: 'pass',
          message: 'Connection test successful',
          details: 'Device is reachable and responsive',
        };
      } else {
        return {
          test: `Connection to ${deviceId}`,
          status: 'fail',
          message: 'Connection test failed',
          details: 'Device is not reachable or not responding',
          suggestion: 'Make sure both devices are on the same network and try again',
        };
      }
    } catch (error) {
      return {
        test: `Connection to ${deviceId}`,
        status: 'fail',
        message: 'Connection test error',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getNetworkDiagnostics(): Promise<{
    localIP: string;
    networkType: string;
    signalStrength: number;
    latency: number;
  }> {
    // Mock network diagnostics
    return {
      localIP: '192.168.1.' + Math.floor(Math.random() * 254 + 1),
      networkType: Math.random() > 0.5 ? 'WiFi' : 'Cellular',
      signalStrength: Math.floor(Math.random() * 100),
      latency: Math.floor(Math.random() * 100 + 10),
    };
  }
}

export default TroubleshootingService.getInstance();
