import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import { Modal } from '../../components/ui';
import SocialManager, { SocialGroup } from '../../services/social/SocialManager';

export default function GroupsScreen() {
  const userProfile = useSelector((state: RootState) => state.user.profile);
  
  const [groups, setGroups] = useState<SocialGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateGroup, setShowCreateGroup] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [newGroupPrivate, setNewGroupPrivate] = useState(false);

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    try {
      setLoading(true);

      if (userProfile?.id) {
        await SocialManager.initialize(userProfile.id);
        const userGroups = SocialManager.getGroups();
        setGroups(userGroups);
      }
    } catch (error) {
      console.error('Failed to load groups:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async () => {
    if (!newGroupName.trim()) {
      Alert.alert('Error', 'Please enter a group name');
      return;
    }

    try {
      await SocialManager.createGroup(
        newGroupName.trim(),
        newGroupDescription.trim() || undefined,
        newGroupPrivate
      );
      
      setNewGroupName('');
      setNewGroupDescription('');
      setNewGroupPrivate(false);
      setShowCreateGroup(false);
      
      await loadGroups();
      Alert.alert('Success', 'Group created successfully!');
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to create group');
    }
  };

  const handleLeaveGroup = (group: SocialGroup) => {
    Alert.alert(
      'Leave Group',
      `Are you sure you want to leave "${group.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              await SocialManager.leaveGroup(group.id);
              await loadGroups();
            } catch (error) {
              Alert.alert('Error', 'Failed to leave group');
            }
          }
        }
      ]
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const GroupCard = ({ group }: { group: SocialGroup }) => {
    const isOwner = group.ownerId === userProfile?.id;
    const memberCount = group.members.length;
    const onlineCount = group.members.filter(m => m.isOnline).length;

    return (
      <View style={styles.groupCard}>
        <BlurView intensity={15} style={styles.groupBlur}>
          <View style={styles.groupHeader}>
            <View style={styles.groupInfo}>
              <View style={styles.groupTitleRow}>
                <Text style={styles.groupName}>{group.name}</Text>
                {group.isPrivate && (
                  <Ionicons name="lock-closed" size={16} color="#FFB800" />
                )}
                {isOwner && (
                  <View style={styles.ownerBadge}>
                    <Text style={styles.ownerText}>Owner</Text>
                  </View>
                )}
              </View>
              {group.description && (
                <Text style={styles.groupDescription}>{group.description}</Text>
              )}
              <View style={styles.groupMeta}>
                <Text style={styles.groupMetaText}>
                  {memberCount} members • {onlineCount} online
                </Text>
                <Text style={styles.groupMetaText}>
                  Created {formatDate(group.createdAt)}
                </Text>
              </View>
            </View>

            <TouchableOpacity
              style={styles.groupAction}
              onPress={() => handleLeaveGroup(group)}
            >
              <Ionicons name="ellipsis-vertical" size={20} color="#CCCCCC" />
            </TouchableOpacity>
          </View>

          {/* Group Stats */}
          <View style={styles.groupStats}>
            <View style={styles.groupStat}>
              <Text style={styles.groupStatValue}>{group.stats.totalSessions}</Text>
              <Text style={styles.groupStatLabel}>Sessions</Text>
            </View>
            <View style={styles.groupStat}>
              <Text style={styles.groupStatValue}>
                {Math.round(group.stats.totalPlaytime / (1000 * 60 * 60))}h
              </Text>
              <Text style={styles.groupStatLabel}>Playtime</Text>
            </View>
            <View style={styles.groupStat}>
              <Text style={styles.groupStatValue}>{group.stats.memberCount}</Text>
              <Text style={styles.groupStatLabel}>Members</Text>
            </View>
          </View>

          {/* Recent Members */}
          <View style={styles.membersSection}>
            <Text style={styles.membersTitle}>Recent Members</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {group.members.slice(0, 5).map((member, index) => (
                <View key={member.userId} style={styles.memberAvatar}>
                  <View style={[
                    styles.memberIcon,
                    { backgroundColor: member.isOnline ? '#00FF88' : '#666666' }
                  ]}>
                    <Ionicons name="person" size={16} color="#FFF" />
                  </View>
                  <Text style={styles.memberName}>{member.userName}</Text>
                </View>
              ))}
              {group.members.length > 5 && (
                <View style={styles.memberAvatar}>
                  <View style={styles.memberIcon}>
                    <Text style={styles.moreMembers}>+{group.members.length - 5}</Text>
                  </View>
                </View>
              )}
            </ScrollView>
          </View>
        </BlurView>
      </View>
    );
  };

  const renderGroup = ({ item }: { item: SocialGroup }) => <GroupCard group={item} />;

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Groups</Text>
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => setShowCreateGroup(true)}
          >
            <Ionicons name="add" size={24} color="#00D4FF" />
          </TouchableOpacity>
        </View>

        {/* Groups List */}
        <FlatList
          data={groups}
          renderItem={renderGroup}
          keyExtractor={(item) => item.id}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <BlurView intensity={15} style={styles.emptyCard}>
                <Ionicons name="people-circle-outline" size={48} color="#666" />
                <Text style={styles.emptyText}>No groups yet</Text>
                <Text style={styles.emptySubtext}>
                  Create or join groups to connect with other players!
                </Text>
              </BlurView>
            </View>
          }
        />

        {/* Create Group Modal */}
        <Modal
          visible={showCreateGroup}
          onClose={() => setShowCreateGroup(false)}
          variant="centered"
        >
          <BlurView intensity={20} style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create Group</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Group Name</Text>
              <TextInput
                style={styles.textInput}
                value={newGroupName}
                onChangeText={setNewGroupName}
                placeholder="Enter group name"
                placeholderTextColor="#666"
                maxLength={50}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Description (Optional)</Text>
              <TextInput
                style={[styles.textInput, styles.descriptionInput]}
                value={newGroupDescription}
                onChangeText={setNewGroupDescription}
                placeholder="Describe your group..."
                placeholderTextColor="#666"
                multiline
                numberOfLines={3}
                maxLength={200}
              />
            </View>

            <TouchableOpacity
              style={styles.privacyOption}
              onPress={() => setNewGroupPrivate(!newGroupPrivate)}
            >
              <View style={styles.privacyLeft}>
                <Ionicons 
                  name={newGroupPrivate ? "lock-closed" : "lock-open"} 
                  size={20} 
                  color={newGroupPrivate ? "#FFB800" : "#00D4FF"} 
                />
                <Text style={styles.privacyText}>Private Group</Text>
              </View>
              <Ionicons 
                name={newGroupPrivate ? "checkmark-circle" : "ellipse-outline"} 
                size={24} 
                color={newGroupPrivate ? "#00FF88" : "#666"} 
              />
            </TouchableOpacity>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowCreateGroup(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.createButtonModal]}
                onPress={handleCreateGroup}
              >
                <Text style={styles.createButtonText}>Create Group</Text>
              </TouchableOpacity>
            </View>
          </BlurView>
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 100,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  createButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#00D4FF',
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  groupCard: {
    marginBottom: 16,
  },
  groupBlur: {
    borderRadius: 16,
    padding: 16,
    overflow: 'hidden',
  },
  groupHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  groupInfo: {
    flex: 1,
  },
  groupTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  groupName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 8,
  },
  ownerBadge: {
    backgroundColor: '#00D4FF',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  ownerText: {
    fontSize: 10,
    color: '#000',
    fontWeight: 'bold',
  },
  groupDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 8,
    lineHeight: 20,
  },
  groupMeta: {
    gap: 2,
  },
  groupMetaText: {
    fontSize: 12,
    color: '#888888',
  },
  groupAction: {
    padding: 8,
  },
  groupStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  groupStat: {
    alignItems: 'center',
  },
  groupStatValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  groupStatLabel: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 2,
  },
  membersSection: {
    marginTop: 8,
  },
  membersTitle: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
    marginBottom: 8,
  },
  memberAvatar: {
    alignItems: 'center',
    marginRight: 12,
  },
  memberIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#666666',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  memberName: {
    fontSize: 10,
    color: '#CCCCCC',
    textAlign: 'center',
  },
  moreMembers: {
    fontSize: 10,
    color: '#FFF',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyCard: {
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    overflow: 'hidden',
  },
  emptyText: {
    fontSize: 18,
    color: '#FFFFFF',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 8,
    textAlign: 'center',
  },
  modalContent: {
    borderRadius: 20,
    padding: 24,
    margin: 20,
    overflow: 'hidden',
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  descriptionInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  privacyOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    marginBottom: 24,
  },
  privacyLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  privacyText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 12,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  createButtonModal: {
    backgroundColor: '#00D4FF',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  createButtonText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '600',
  },
});
