import { Platform, PermissionsAndroid, NativeModules } from 'react-native';
import * as Device from 'expo-device';
import * as FileSystem from 'expo-file-system';
import GameDetector from './GameDetector';
import GameDetectionEnhancer from './GameDetectionEnhancer';

export interface TroubleshootingStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'warning' | 'error';
  result?: any;
  error?: string;
  recommendation?: string;
  autoFix?: () => Promise<boolean>;
}

export interface TroubleshootingReport {
  deviceInfo: {
    platform: string;
    version: string;
    model: string;
    brand: string;
  };
  permissions: {
    required: string[];
    granted: string[];
    denied: string[];
  };
  steps: TroubleshootingStep[];
  overallStatus: 'healthy' | 'issues' | 'critical';
  recommendations: string[];
  autoFixAvailable: boolean;
}

class GameDetectionTroubleshooter {
  private static instance: GameDetectionTroubleshooter;

  static getInstance(): GameDetectionTroubleshooter {
    if (!GameDetectionTroubleshooter.instance) {
      GameDetectionTroubleshooter.instance = new GameDetectionTroubleshooter();
    }
    return GameDetectionTroubleshooter.instance;
  }

  async runFullDiagnostics(): Promise<TroubleshootingReport> {
    console.log('🔧 Starting game detection troubleshooting...');

    const report: TroubleshootingReport = {
      deviceInfo: await this.getDeviceInfo(),
      permissions: { required: [], granted: [], denied: [] },
      steps: [],
      overallStatus: 'healthy',
      recommendations: [],
      autoFixAvailable: false,
    };

    // Define troubleshooting steps
    const steps: Omit<TroubleshootingStep, 'status' | 'result'>[] = [
      {
        id: 'device_compatibility',
        name: 'Device Compatibility Check',
        description: 'Verify device supports game detection features',
      },
      {
        id: 'permissions_check',
        name: 'Permissions Verification',
        description: 'Check if all required permissions are granted',
        autoFix: this.fixPermissions.bind(this),
      },
      {
        id: 'native_modules',
        name: 'Native Modules Check',
        description: 'Verify native modules are available and working',
      },
      {
        id: 'file_system_access',
        name: 'File System Access',
        description: 'Test file system read/write capabilities',
      },
      {
        id: 'game_database',
        name: 'Game Database Integrity',
        description: 'Verify game database is loaded and accessible',
      },
      {
        id: 'detection_methods',
        name: 'Detection Methods Test',
        description: 'Test all available game detection methods',
      },
      {
        id: 'mock_data_fallback',
        name: 'Mock Data Fallback',
        description: 'Verify mock data system is working',
      },
    ];

    // Initialize steps
    report.steps = steps.map(step => ({
      ...step,
      status: 'pending',
    }));

    // Run each step
    for (let i = 0; i < report.steps.length; i++) {
      const step = report.steps[i];
      step.status = 'running';

      try {
        await this.runTroubleshootingStep(step);
      } catch (error) {
        step.status = 'error';
        step.error = error instanceof Error ? error.message : 'Unknown error';
      }
    }

    // Analyze results and generate recommendations
    this.analyzeResults(report);

    console.log('🔧 Troubleshooting complete:', report.overallStatus);
    return report;
  }

  private async getDeviceInfo() {
    return {
      platform: Platform.OS,
      version: Platform.Version.toString(),
      model: Device.modelName || 'Unknown',
      brand: Device.brand || 'Unknown',
    };
  }

  private async runTroubleshootingStep(step: TroubleshootingStep): Promise<void> {
    switch (step.id) {
      case 'device_compatibility':
        await this.checkDeviceCompatibility(step);
        break;
      case 'permissions_check':
        await this.checkPermissions(step);
        break;
      case 'native_modules':
        await this.checkNativeModules(step);
        break;
      case 'file_system_access':
        await this.checkFileSystemAccess(step);
        break;
      case 'game_database':
        await this.checkGameDatabase(step);
        break;
      case 'detection_methods':
        await this.testDetectionMethods(step);
        break;
      case 'mock_data_fallback':
        await this.testMockDataFallback(step);
        break;
      default:
        throw new Error(`Unknown troubleshooting step: ${step.id}`);
    }
  }

  private async checkDeviceCompatibility(step: TroubleshootingStep): Promise<void> {
    const compatibility = {
      platform: Platform.OS,
      version: Platform.Version,
      isPhysicalDevice: Device.isDevice,
      supportedFeatures: [],
    };

    // Check platform-specific features
    if (Platform.OS === 'android') {
      compatibility.supportedFeatures.push('Package Manager', 'File System Access');
      if (Platform.Version >= 23) {
        compatibility.supportedFeatures.push('Runtime Permissions');
      }
    } else if (Platform.OS === 'ios') {
      compatibility.supportedFeatures.push('URL Schemes', 'App Groups');
    }

    step.result = compatibility;
    step.status = 'success';

    if (!Device.isDevice) {
      step.status = 'warning';
      step.recommendation = 'Some features may not work properly on simulators/emulators';
    }
  }

  private async checkPermissions(step: TroubleshootingStep): Promise<void> {
    const permissions = {
      required: [],
      granted: [],
      denied: [],
    };

    if (Platform.OS === 'android') {
      const requiredPermissions = [
        'android.permission.QUERY_ALL_PACKAGES',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.PACKAGE_USAGE_STATS',
      ];

      permissions.required = requiredPermissions;

      for (const permission of requiredPermissions) {
        try {
          const result = await PermissionsAndroid.check(permission as any);
          if (result) {
            permissions.granted.push(permission);
          } else {
            permissions.denied.push(permission);
          }
        } catch (error) {
          permissions.denied.push(permission);
        }
      }
    } else {
      // iOS permissions are handled differently
      permissions.required = ['File System Access'];
      permissions.granted = ['File System Access'];
    }

    step.result = permissions;
    
    if (permissions.denied.length === 0) {
      step.status = 'success';
    } else if (permissions.granted.length > 0) {
      step.status = 'warning';
      step.recommendation = `Grant missing permissions: ${permissions.denied.join(', ')}`;
    } else {
      step.status = 'error';
      step.recommendation = 'Critical permissions missing. Game detection will not work properly.';
    }
  }

  private async checkNativeModules(step: TroubleshootingStep): Promise<void> {
    const modules = {
      available: [],
      missing: [],
    };

    const expectedModules = ['InstalledApps', 'GameLauncher', 'FileManager'];

    for (const moduleName of expectedModules) {
      if (NativeModules[moduleName]) {
        modules.available.push(moduleName);
      } else {
        modules.missing.push(moduleName);
      }
    }

    step.result = modules;

    if (modules.missing.length === 0) {
      step.status = 'success';
    } else if (modules.available.length > 0) {
      step.status = 'warning';
      step.recommendation = `Some native modules are missing: ${modules.missing.join(', ')}. Fallback methods will be used.`;
    } else {
      step.status = 'warning';
      step.recommendation = 'No native modules available. Using JavaScript-only detection methods.';
    }
  }

  private async checkFileSystemAccess(step: TroubleshootingStep): Promise<void> {
    const access = {
      documentsDirectory: false,
      canRead: false,
      canWrite: false,
      testFilePath: '',
    };

    try {
      // Test documents directory access
      if (FileSystem.documentDirectory) {
        access.documentsDirectory = true;
        access.testFilePath = FileSystem.documentDirectory + 'test_game_detection.txt';

        // Test write access
        await FileSystem.writeAsStringAsync(access.testFilePath, 'test');
        access.canWrite = true;

        // Test read access
        const content = await FileSystem.readAsStringAsync(access.testFilePath);
        access.canRead = content === 'test';

        // Clean up
        await FileSystem.deleteAsync(access.testFilePath, { idempotent: true });
      }

      step.result = access;

      if (access.documentsDirectory && access.canRead && access.canWrite) {
        step.status = 'success';
      } else {
        step.status = 'warning';
        step.recommendation = 'Limited file system access. Some detection methods may not work.';
      }
    } catch (error) {
      step.status = 'error';
      step.error = error instanceof Error ? error.message : 'File system access failed';
      step.recommendation = 'File system access is blocked. Check app permissions.';
    }
  }

  private async checkGameDatabase(step: TroubleshootingStep): Promise<void> {
    try {
      const gameDetector = GameDetector;
      const gameCount = Object.keys((gameDetector as any).gameDatabase || {}).length;

      step.result = {
        databaseLoaded: gameCount > 0,
        gameCount,
        sampleGames: gameCount > 0 ? Object.keys((gameDetector as any).gameDatabase).slice(0, 3) : [],
      };

      if (gameCount > 0) {
        step.status = 'success';
      } else {
        step.status = 'error';
        step.recommendation = 'Game database is empty or not loaded properly.';
      }
    } catch (error) {
      step.status = 'error';
      step.error = error instanceof Error ? error.message : 'Database check failed';
    }
  }

  private async testDetectionMethods(step: TroubleshootingStep): Promise<void> {
    const methods = {
      enhanced: { available: false, gameCount: 0, errors: [] },
      standard: { available: false, gameCount: 0, errors: [] },
    };

    try {
      // Test enhanced detection
      const enhancedResult = await GameDetectionEnhancer.enhancedGameDetection();
      methods.enhanced.available = true;
      methods.enhanced.gameCount = enhancedResult.games.length;
      methods.enhanced.errors = enhancedResult.errors;
    } catch (error) {
      methods.enhanced.errors.push(error instanceof Error ? error.message : 'Enhanced detection failed');
    }

    try {
      // Test standard detection
      const standardGames = await GameDetector.scanForGames();
      methods.standard.available = true;
      methods.standard.gameCount = standardGames.length;
    } catch (error) {
      methods.standard.errors.push(error instanceof Error ? error.message : 'Standard detection failed');
    }

    step.result = methods;

    if (methods.enhanced.available || methods.standard.available) {
      step.status = 'success';
    } else {
      step.status = 'error';
      step.recommendation = 'All detection methods failed. Check permissions and native modules.';
    }
  }

  private async testMockDataFallback(step: TroubleshootingStep): Promise<void> {
    try {
      // This would test the mock data system
      const mockGames = ['com.mojang.minecraftpe', 'com.king.candycrushsaga'];
      
      step.result = {
        mockDataAvailable: true,
        mockGameCount: mockGames.length,
        sampleMockGames: mockGames,
      };

      step.status = 'success';
    } catch (error) {
      step.status = 'error';
      step.error = error instanceof Error ? error.message : 'Mock data test failed';
    }
  }

  private async fixPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true; // iOS permissions are handled differently
    }

    try {
      const permissions = [
        'android.permission.QUERY_ALL_PACKAGES',
        'android.permission.READ_EXTERNAL_STORAGE',
      ];

      let allGranted = true;

      for (const permission of permissions) {
        const result = await PermissionsAndroid.request(permission as any);
        if (result !== PermissionsAndroid.RESULTS.GRANTED) {
          allGranted = false;
        }
      }

      return allGranted;
    } catch (error) {
      console.error('Failed to fix permissions:', error);
      return false;
    }
  }

  private analyzeResults(report: TroubleshootingReport): void {
    const errorSteps = report.steps.filter(step => step.status === 'error');
    const warningSteps = report.steps.filter(step => step.status === 'warning');

    // Determine overall status
    if (errorSteps.length > 2) {
      report.overallStatus = 'critical';
    } else if (errorSteps.length > 0 || warningSteps.length > 2) {
      report.overallStatus = 'issues';
    } else {
      report.overallStatus = 'healthy';
    }

    // Generate recommendations
    report.recommendations = [
      ...errorSteps.map(step => step.recommendation).filter(Boolean),
      ...warningSteps.map(step => step.recommendation).filter(Boolean),
    ];

    // Check if auto-fix is available
    report.autoFixAvailable = report.steps.some(step => step.autoFix && step.status !== 'success');

    // Add general recommendations based on platform
    if (Platform.OS === 'android') {
      report.recommendations.push(
        'Ensure the app has permission to query installed packages',
        'Try running on a physical device for better detection accuracy'
      );
    } else if (Platform.OS === 'ios') {
      report.recommendations.push(
        'Game detection on iOS is limited due to platform restrictions',
        'URL scheme detection works best for supported games'
      );
    }
  }

  async runAutoFix(): Promise<{ success: boolean; fixedSteps: string[] }> {
    const result = { success: true, fixedSteps: [] };

    const report = await this.runFullDiagnostics();
    
    for (const step of report.steps) {
      if (step.autoFix && step.status !== 'success') {
        try {
          const fixed = await step.autoFix();
          if (fixed) {
            result.fixedSteps.push(step.name);
          } else {
            result.success = false;
          }
        } catch (error) {
          console.error(`Auto-fix failed for ${step.name}:`, error);
          result.success = false;
        }
      }
    }

    return result;
  }
}

export default GameDetectionTroubleshooter.getInstance();
