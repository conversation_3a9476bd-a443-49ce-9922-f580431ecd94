name: 📦 Release Management

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string
      release_type:
        description: 'Type of release'
        required: true
        default: 'minor'
        type: choice
        options:
          - major
          - minor
          - patch
          - prerelease
      create_github_release:
        description: 'Create GitHub release'
        required: false
        default: true
        type: boolean

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  # Job 1: Validate release
  validate-release:
    name: ✅ Validate Release
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      version: ${{ steps.version.outputs.version }}
      changelog: ${{ steps.changelog.outputs.changelog }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏷️ Extract version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "push" ]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            VERSION=${{ github.event.inputs.version }}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "📦 Release version: $VERSION"

      - name: 📋 Generate changelog
        id: changelog
        run: |
          echo "📋 Generating changelog..."
          # Generate changelog from git commits
          CHANGELOG=$(git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 HEAD^)..HEAD)
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: 🔍 Validate version format
        run: |
          VERSION=${{ steps.version.outputs.version }}
          if [[ ! $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$ ]]; then
            echo "❌ Invalid version format: $VERSION"
            echo "Expected format: v1.0.0 or v1.0.0-beta"
            exit 1
          fi
          echo "✅ Version format is valid: $VERSION"

  # Job 2: Run full CI pipeline
  run-ci:
    name: 🔄 Run CI Pipeline
    uses: ./.github/workflows/ci.yml
    needs: validate-release

  # Job 3: Build release artifacts
  build-release:
    name: 🏗️ Build Release Artifacts
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [validate-release, run-ci]
    if: needs.run-ci.result == 'success'
    
    strategy:
      matrix:
        platform: [android, ios, web]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo and EAS CLI
        run: |
          npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}
          npm install -g eas-cli

      - name: 🔑 Expo authentication
        run: npx expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 📝 Update version in app.json
        run: |
          VERSION=${{ needs.validate-release.outputs.version }}
          # Remove 'v' prefix for app.json
          APP_VERSION=${VERSION#v}
          
          # Update version in app.json
          npx json -I -f app.json -e "this.expo.version='$APP_VERSION'"
          
          echo "📝 Updated app version to: $APP_VERSION"

      - name: 🏗️ Build for ${{ matrix.platform }}
        run: |
          if [ "${{ matrix.platform }}" = "web" ]; then
            npx expo export --platform web
            npx expo build:web
          elif [ "${{ matrix.platform }}" = "android" ]; then
            npx eas build --platform android --profile production --non-interactive
          elif [ "${{ matrix.platform }}" = "ios" ]; then
            npx eas build --platform ios --profile production --non-interactive
          fi

      - name: 📦 Package release artifacts
        run: |
          mkdir -p release-artifacts/${{ matrix.platform }}
          
          if [ "${{ matrix.platform }}" = "web" ]; then
            cp -r dist/* release-artifacts/${{ matrix.platform }}/
          else
            echo "Build completed for ${{ matrix.platform }}" > release-artifacts/${{ matrix.platform }}/build-info.txt
            echo "Version: ${{ needs.validate-release.outputs.version }}" >> release-artifacts/${{ matrix.platform }}/build-info.txt
            echo "Platform: ${{ matrix.platform }}" >> release-artifacts/${{ matrix.platform }}/build-info.txt
            echo "Build Date: $(date)" >> release-artifacts/${{ matrix.platform }}/build-info.txt
          fi

      - name: 📤 Upload release artifacts
        uses: actions/upload-artifact@v4
        with:
          name: release-${{ matrix.platform }}-${{ needs.validate-release.outputs.version }}
          path: release-artifacts/${{ matrix.platform }}/
          retention-days: 90

  # Job 4: Create GitHub release
  create-github-release:
    name: 🎉 Create GitHub Release
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [validate-release, run-ci, build-release]
    if: |
      needs.run-ci.result == 'success' && 
      needs.build-release.result == 'success' &&
      (github.event.inputs.create_github_release == 'true' || github.event_name == 'push')
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download release artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: release-*-${{ needs.validate-release.outputs.version }}
          path: release-artifacts/

      - name: 📦 Create release archive
        run: |
          cd release-artifacts
          tar -czf ../logaco-${{ needs.validate-release.outputs.version }}.tar.gz *
          cd ..
          
          echo "📦 Created release archive: logaco-${{ needs.validate-release.outputs.version }}.tar.gz"

      - name: 📋 Prepare release notes
        run: |
          cat > release-notes.md << EOF
          # LoGaCo ${{ needs.validate-release.outputs.version }}
          
          ## 🎮 What's New
          
          ${{ needs.validate-release.outputs.changelog }}
          
          ## 📱 Installation
          
          ### Mobile Apps
          - **iOS**: Available on the App Store
          - **Android**: Available on Google Play Store
          
          ### Web App
          - **URL**: https://logaco.app
          
          ## 🔧 Technical Details
          
          - **Version**: ${{ needs.validate-release.outputs.version }}
          - **Build Date**: $(date)
          - **Platforms**: iOS, Android, Web
          - **Expo SDK**: $(npx expo --version)
          
          ## 📊 Checksums
          
          \`\`\`
          $(sha256sum logaco-${{ needs.validate-release.outputs.version }}.tar.gz)
          \`\`\`
          EOF

      - name: 🎉 Create GitHub release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.validate-release.outputs.version }}
          release_name: LoGaCo ${{ needs.validate-release.outputs.version }}
          body_path: release-notes.md
          draft: false
          prerelease: ${{ contains(needs.validate-release.outputs.version, '-') }}

      - name: 📤 Upload release assets
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: logaco-${{ needs.validate-release.outputs.version }}.tar.gz
          asset_name: logaco-${{ needs.validate-release.outputs.version }}.tar.gz
          asset_content_type: application/gzip

  # Job 5: Deploy release to production
  deploy-production:
    name: 🚀 Deploy to Production
    uses: ./.github/workflows/cd.yml
    needs: [validate-release, run-ci, build-release]
    if: needs.run-ci.result == 'success' && needs.build-release.result == 'success'
    with:
      environment: production
    secrets: inherit

  # Job 6: Post-release tasks
  post-release:
    name: 📊 Post-release Tasks
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [validate-release, create-github-release, deploy-production]
    if: always() && needs.create-github-release.result == 'success'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔔 Send release notifications
        run: |
          echo "🔔 Sending release notifications..."
          echo "📦 LoGaCo ${{ needs.validate-release.outputs.version }} has been released!"
          echo "🔗 GitHub Release: https://github.com/${{ github.repository }}/releases/tag/${{ needs.validate-release.outputs.version }}"
          echo "📱 App Store: https://apps.apple.com/app/logaco"
          echo "🤖 Google Play: https://play.google.com/store/apps/details?id=com.logaco.app"
          echo "🌐 Web App: https://logaco.app"

      - name: 📈 Update release metrics
        run: |
          echo "📈 Updating release metrics..."
          # Add metrics collection here

      - name: 🎯 Create next milestone
        run: |
          echo "🎯 Creating next development milestone..."
          # Add milestone creation logic here

      - name: ✅ Release process completed
        run: |
          echo "✅ Release process completed successfully!"
          echo "🎉 LoGaCo ${{ needs.validate-release.outputs.version }} is now live!"
