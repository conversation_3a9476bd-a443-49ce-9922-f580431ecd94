import { useState, useEffect, useCallback } from 'react';
import GameCompatibility, { 
  CompatibilityCheck, 
  SessionRequirements 
} from '../services/game/GameCompatibility';
import { GameInfo } from '../services/game/GameDetector';

interface UseGameCompatibilityReturn {
  checkCompatibility: (game: GameInfo, requirements: SessionRequirements) => Promise<CompatibilityCheck>;
  getOptimalNetworkType: (game: GameInfo, availableNetworks: string[]) => Promise<string | null>;
  getCompatibilityScore: (game: GameInfo, requirements: SessionRequirements) => Promise<number>;
  isGameSupported: (packageName: string) => Promise<boolean>;
  getSupportedGames: () => Promise<string[]>;
  loading: boolean;
  error: string | null;
}

export default function useGameCompatibility(): UseGameCompatibilityReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkCompatibility = useCallback(async (
    game: GameInfo, 
    requirements: SessionRequirements
  ): Promise<CompatibilityCheck> => {
    try {
      setLoading(true);
      setError(null);
      
      const compatibility = await GameCompatibility.checkGameCompatibility(game, requirements);
      return compatibility;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check compatibility';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getOptimalNetworkType = useCallback(async (
    game: GameInfo, 
    availableNetworks: string[]
  ): Promise<string | null> => {
    try {
      setError(null);
      
      const networkType = await GameCompatibility.getOptimalNetworkType(game, availableNetworks);
      return networkType;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get optimal network type';
      setError(errorMessage);
      return null;
    }
  }, []);

  const getCompatibilityScore = useCallback(async (
    game: GameInfo, 
    requirements: SessionRequirements
  ): Promise<number> => {
    try {
      setError(null);
      
      const score = await GameCompatibility.getCompatibilityScore(game, requirements);
      return score;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get compatibility score';
      setError(errorMessage);
      return 0;
    }
  }, []);

  const isGameSupported = useCallback(async (packageName: string): Promise<boolean> => {
    try {
      setError(null);
      
      const supported = await GameCompatibility.isGameSupported(packageName);
      return supported;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check game support';
      setError(errorMessage);
      return false;
    }
  }, []);

  const getSupportedGames = useCallback(async (): Promise<string[]> => {
    try {
      setError(null);
      
      const games = await GameCompatibility.getSupportedGames();
      return games;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get supported games';
      setError(errorMessage);
      return [];
    }
  }, []);

  return {
    checkCompatibility,
    getOptimalNetworkType,
    getCompatibilityScore,
    isGameSupported,
    getSupportedGames,
    loading,
    error,
  };
}
