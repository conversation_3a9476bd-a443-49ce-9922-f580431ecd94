import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Alert,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import { Modal } from '../../components/ui';
import SocialManager, { Friend, FriendRequest } from '../../services/social/SocialManager';

const { width } = Dimensions.get('window');

export default function FriendsScreen() {
  const userProfile = useSelector((state: RootState) => state.user.profile);
  
  const [friends, setFriends] = useState<Friend[]>([]);
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([]);
  const [selectedTab, setSelectedTab] = useState<'friends' | 'requests' | 'add'>('friends');
  const [loading, setLoading] = useState(true);
  const [showAddFriend, setShowAddFriend] = useState(false);
  const [newFriendId, setNewFriendId] = useState('');
  const [newFriendMessage, setNewFriendMessage] = useState('');

  useEffect(() => {
    loadFriendsData();
  }, []);

  const loadFriendsData = async () => {
    try {
      setLoading(true);

      if (userProfile?.id) {
        await SocialManager.initialize(userProfile.id);
        
        const allFriends = SocialManager.getFriends();
        setFriends(allFriends);
        
        const pendingRequests = SocialManager.getPendingFriendRequests();
        setFriendRequests(pendingRequests);
      }
    } catch (error) {
      console.error('Failed to load friends data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendFriendRequest = async () => {
    if (!newFriendId.trim()) {
      Alert.alert('Error', 'Please enter a friend ID');
      return;
    }

    try {
      await SocialManager.sendFriendRequest(
        newFriendId.trim(),
        'Unknown User', // In real app, would lookup user name
        newFriendMessage.trim() || undefined
      );
      
      setNewFriendId('');
      setNewFriendMessage('');
      setShowAddFriend(false);
      
      Alert.alert('Success', 'Friend request sent!');
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to send friend request');
    }
  };

  const handleAcceptRequest = async (requestId: string) => {
    try {
      await SocialManager.acceptFriendRequest(requestId);
      await loadFriendsData();
      Alert.alert('Success', 'Friend request accepted!');
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to accept request');
    }
  };

  const handleDeclineRequest = async (requestId: string) => {
    try {
      await SocialManager.declineFriendRequest(requestId);
      await loadFriendsData();
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to decline request');
    }
  };

  const handleRemoveFriend = (friend: Friend) => {
    Alert.alert(
      'Remove Friend',
      `Are you sure you want to remove ${friend.name} from your friends?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await SocialManager.removeFriend(friend.id);
              await loadFriendsData();
            } catch (error) {
              Alert.alert('Error', 'Failed to remove friend');
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return '#00FF88';
      case 'away': return '#FFB800';
      case 'busy': return '#FF4444';
      default: return '#888888';
    }
  };

  const formatLastSeen = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const TabButton = ({ tab, title, count }: { tab: string; title: string; count?: number }) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        selectedTab === tab && styles.tabButtonActive
      ]}
      onPress={() => setSelectedTab(tab as any)}
    >
      <BlurView intensity={15} style={styles.tabBlur}>
        <Text style={[
          styles.tabText,
          selectedTab === tab && styles.tabTextActive
        ]}>
          {title}
        </Text>
        {count !== undefined && count > 0 && (
          <View style={styles.tabBadge}>
            <Text style={styles.tabBadgeText}>{count}</Text>
          </View>
        )}
      </BlurView>
    </TouchableOpacity>
  );

  const FriendCard = ({ friend }: { friend: Friend }) => (
    <View style={styles.friendCard}>
      <BlurView intensity={15} style={styles.friendBlur}>
        <View style={styles.friendContent}>
          <View style={styles.friendAvatar}>
            <Ionicons name="person" size={24} color="#00D4FF" />
            <View style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor(friend.status) }
            ]} />
          </View>

          <View style={styles.friendInfo}>
            <Text style={styles.friendName}>{friend.name}</Text>
            <Text style={styles.friendDevice}>{friend.deviceType}</Text>
            <Text style={styles.friendStatus}>
              {friend.status === 'online' ? 'Online' : formatLastSeen(friend.lastSeen)}
            </Text>
          </View>

          <View style={styles.friendStats}>
            <Text style={styles.friendStatValue}>{friend.stats.gamesPlayed}</Text>
            <Text style={styles.friendStatLabel}>Games</Text>
          </View>

          <TouchableOpacity
            style={styles.friendAction}
            onPress={() => handleRemoveFriend(friend)}
          >
            <Ionicons name="ellipsis-vertical" size={20} color="#CCCCCC" />
          </TouchableOpacity>
        </View>
      </BlurView>
    </View>
  );

  const RequestCard = ({ request }: { request: FriendRequest }) => (
    <View style={styles.requestCard}>
      <BlurView intensity={15} style={styles.requestBlur}>
        <View style={styles.requestContent}>
          <View style={styles.requestInfo}>
            <Text style={styles.requestName}>{request.fromUserName}</Text>
            {request.message && (
              <Text style={styles.requestMessage}>"{request.message}"</Text>
            )}
            <Text style={styles.requestTime}>
              {formatLastSeen(request.timestamp)}
            </Text>
          </View>

          <View style={styles.requestActions}>
            <TouchableOpacity
              style={[styles.requestButton, styles.acceptButton]}
              onPress={() => handleAcceptRequest(request.id)}
            >
              <Ionicons name="checkmark" size={20} color="#FFF" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.requestButton, styles.declineButton]}
              onPress={() => handleDeclineRequest(request.id)}
            >
              <Ionicons name="close" size={20} color="#FFF" />
            </TouchableOpacity>
          </View>
        </View>
      </BlurView>
    </View>
  );

  const renderFriend = ({ item }: { item: Friend }) => <FriendCard friend={item} />;
  const renderRequest = ({ item }: { item: FriendRequest }) => <RequestCard request={item} />;

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Friends</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddFriend(true)}
          >
            <Ionicons name="person-add" size={24} color="#00D4FF" />
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.tabsScroll}
          contentContainerStyle={styles.tabsContainer}
        >
          <TabButton tab="friends" title="Friends" count={friends.length} />
          <TabButton tab="requests" title="Requests" count={friendRequests.length} />
        </ScrollView>

        {/* Content */}
        <View style={styles.content}>
          {selectedTab === 'friends' && (
            <FlatList
              data={friends}
              renderItem={renderFriend}
              keyExtractor={(item) => item.id}
              style={styles.list}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <BlurView intensity={15} style={styles.emptyCard}>
                    <Ionicons name="people-outline" size={48} color="#666" />
                    <Text style={styles.emptyText}>No friends yet</Text>
                    <Text style={styles.emptySubtext}>
                      Add friends to connect and play together!
                    </Text>
                  </BlurView>
                </View>
              }
            />
          )}

          {selectedTab === 'requests' && (
            <FlatList
              data={friendRequests}
              renderItem={renderRequest}
              keyExtractor={(item) => item.id}
              style={styles.list}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <BlurView intensity={15} style={styles.emptyCard}>
                    <Ionicons name="mail-outline" size={48} color="#666" />
                    <Text style={styles.emptyText}>No friend requests</Text>
                    <Text style={styles.emptySubtext}>
                      Friend requests will appear here
                    </Text>
                  </BlurView>
                </View>
              }
            />
          )}
        </View>

        {/* Add Friend Modal */}
        <Modal
          visible={showAddFriend}
          onClose={() => setShowAddFriend(false)}
          variant="centered"
        >
          <BlurView intensity={20} style={styles.modalContent}>
            <Text style={styles.modalTitle}>Add Friend</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Friend ID</Text>
              <TextInput
                style={styles.textInput}
                value={newFriendId}
                onChangeText={setNewFriendId}
                placeholder="Enter friend's ID"
                placeholderTextColor="#666"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Message (Optional)</Text>
              <TextInput
                style={[styles.textInput, styles.messageInput]}
                value={newFriendMessage}
                onChangeText={setNewFriendMessage}
                placeholder="Say hello..."
                placeholderTextColor="#666"
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowAddFriend(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.sendButton]}
                onPress={handleSendFriendRequest}
              >
                <Text style={styles.sendButtonText}>Send Request</Text>
              </TouchableOpacity>
            </View>
          </BlurView>
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 100,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#00D4FF',
  },
  tabsScroll: {
    maxHeight: 60,
  },
  tabsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  tabButton: {
    marginRight: 16,
  },
  tabButtonActive: {
    transform: [{ scale: 1.05 }],
  },
  tabBlur: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  tabText: {
    fontSize: 16,
    color: '#CCCCCC',
    fontWeight: '600',
  },
  tabTextActive: {
    color: '#00D4FF',
  },
  tabBadge: {
    backgroundColor: '#FF4444',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: 12,
    color: '#FFF',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  friendCard: {
    marginBottom: 12,
  },
  friendBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  friendContent: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  friendAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#000',
  },
  friendInfo: {
    flex: 1,
    marginLeft: 12,
  },
  friendName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  friendDevice: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 2,
  },
  friendStatus: {
    fontSize: 12,
    color: '#00D4FF',
    marginTop: 2,
  },
  friendStats: {
    alignItems: 'center',
    marginRight: 12,
  },
  friendStatValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  friendStatLabel: {
    fontSize: 12,
    color: '#CCCCCC',
  },
  friendAction: {
    padding: 8,
  },
  requestCard: {
    marginBottom: 12,
  },
  requestBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  requestContent: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  requestInfo: {
    flex: 1,
  },
  requestName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  requestMessage: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
    fontStyle: 'italic',
  },
  requestTime: {
    fontSize: 12,
    color: '#888888',
    marginTop: 4,
  },
  requestActions: {
    flexDirection: 'row',
    gap: 8,
  },
  requestButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#00FF88',
  },
  declineButton: {
    backgroundColor: '#FF4444',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyCard: {
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    overflow: 'hidden',
  },
  emptyText: {
    fontSize: 18,
    color: '#FFFFFF',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 8,
    textAlign: 'center',
  },
  modalContent: {
    borderRadius: 20,
    padding: 24,
    margin: 20,
    overflow: 'hidden',
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  messageInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  modalButton: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  sendButton: {
    backgroundColor: '#00D4FF',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  sendButtonText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '600',
  },
});
