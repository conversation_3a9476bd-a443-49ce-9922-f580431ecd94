import { Platform, AppState, AppStateStatus } from 'react-native';
import { EventEmitter } from 'events';
import TrafficRouter from '../networking/TrafficRouter';
import PacketModifier from '../networking/PacketModifier';
import DNSInterceptor from '../networking/DNSInterceptor';

export interface PacketQueueItem {
  id: string;
  packet: any;
  priority: 'high' | 'medium' | 'low';
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  gameId?: string;
  sessionId?: string;
}

export interface TrafficStats {
  packetsProcessed: number;
  packetsQueued: number;
  packetsDropped: number;
  averageProcessingTime: number;
  memoryUsage: number;
  queueSize: number;
  lastUpdated: number;
}

export interface TrafficServiceConfig {
  maxQueueSize: number;
  maxMemoryUsage: number; // in MB
  processingInterval: number;
  batchSize: number;
  priorityWeights: {
    high: number;
    medium: number;
    low: number;
  };
  backgroundProcessing: boolean;
  memoryCleanupInterval: number;
}

class TrafficService extends EventEmitter {
  private static instance: TrafficService;
  private isActive: boolean = false;
  private appState: AppStateStatus = 'active';
  private packetQueue: Map<string, PacketQueueItem> = new Map();
  private priorityQueues: {
    high: PacketQueueItem[];
    medium: PacketQueueItem[];
    low: PacketQueueItem[];
  };
  private trafficRouter: TrafficRouter;
  private packetModifier: PacketModifier;
  private dnsInterceptor: DNSInterceptor;
  private config: TrafficServiceConfig;
  private processingInterval: NodeJS.Timeout | null = null;
  private memoryCleanupInterval: NodeJS.Timeout | null = null;
  private stats: TrafficStats;
  private processingTimes: number[] = [];

  private constructor() {
    super();
    this.trafficRouter = TrafficRouter.getInstance();
    this.packetModifier = PacketModifier.getInstance();
    this.dnsInterceptor = DNSInterceptor.getInstance();
    
    this.priorityQueues = {
      high: [],
      medium: [],
      low: []
    };

    this.config = {
      maxQueueSize: 10000,
      maxMemoryUsage: 50, // 50MB
      processingInterval: 1, // 1ms for low latency
      batchSize: 100,
      priorityWeights: {
        high: 3,
        medium: 2,
        low: 1
      },
      backgroundProcessing: true,
      memoryCleanupInterval: 30000 // 30 seconds
    };

    this.stats = {
      packetsProcessed: 0,
      packetsQueued: 0,
      packetsDropped: 0,
      averageProcessingTime: 0,
      memoryUsage: 0,
      queueSize: 0,
      lastUpdated: Date.now()
    };

    this.setupAppStateHandling();
  }

  static getInstance(): TrafficService {
    if (!TrafficService.instance) {
      TrafficService.instance = new TrafficService();
    }
    return TrafficService.instance;
  }

  // Initialization
  async initialize(config?: Partial<TrafficServiceConfig>): Promise<void> {
    try {
      console.log('Initializing Traffic Service...');

      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Start packet processing
      this.startPacketProcessing();

      // Start memory management
      this.startMemoryManagement();

      this.isActive = true;
      this.emit('initialized');
      console.log('Traffic Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Traffic Service:', error);
      throw error;
    }
  }

  private setupAppStateHandling(): void {
    AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  private handleAppStateChange(nextAppState: AppStateStatus): void {
    if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
      this.onAppBackground();
    } else if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
      this.onAppForeground();
    }
    
    this.appState = nextAppState;
  }

  private onAppBackground(): void {
    console.log('Traffic Service: App entering background');
    if (this.config.backgroundProcessing) {
      // Continue processing in background with reduced frequency
      this.adjustProcessingForBackground();
    } else {
      this.pauseProcessing();
    }
  }

  private onAppForeground(): void {
    console.log('Traffic Service: App entering foreground');
    this.resumeNormalProcessing();
  }

  // Packet Queue Management
  async queuePacket(
    packet: any,
    priority: 'high' | 'medium' | 'low' = 'medium',
    gameId?: string,
    sessionId?: string
  ): Promise<boolean> {
    try {
      // Check memory and queue limits
      if (!this.canAcceptPacket()) {
        this.stats.packetsDropped++;
        console.warn('Packet dropped due to queue/memory limits');
        return false;
      }

      const queueItem: PacketQueueItem = {
        id: `packet_${Date.now()}_${Math.random()}`,
        packet,
        priority,
        timestamp: Date.now(),
        retryCount: 0,
        maxRetries: 3,
        gameId,
        sessionId
      };

      // Add to appropriate priority queue
      this.priorityQueues[priority].push(queueItem);
      this.packetQueue.set(queueItem.id, queueItem);

      this.stats.packetsQueued++;
      this.updateQueueStats();

      console.log(`Packet queued with ${priority} priority: ${queueItem.id}`);
      this.emit('packetQueued', queueItem);

      return true;
    } catch (error) {
      console.error('Failed to queue packet:', error);
      this.stats.packetsDropped++;
      return false;
    }
  }

  private canAcceptPacket(): boolean {
    // Check queue size limit
    if (this.packetQueue.size >= this.config.maxQueueSize) {
      return false;
    }

    // Check memory usage limit
    const memoryUsage = this.estimateMemoryUsage();
    if (memoryUsage > this.config.maxMemoryUsage) {
      return false;
    }

    return true;
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in MB
    const queueSize = this.packetQueue.size;
    const averagePacketSize = 1024; // 1KB average
    return (queueSize * averagePacketSize) / (1024 * 1024);
  }

  // Packet Processing
  private startPacketProcessing(): void {
    this.processingInterval = setInterval(() => {
      this.processPacketBatch();
    }, this.config.processingInterval);
  }

  private async processPacketBatch(): Promise<void> {
    if (!this.isActive || this.packetQueue.size === 0) {
      return;
    }

    const startTime = Date.now();
    const batch = this.getNextBatch();

    if (batch.length === 0) {
      return;
    }

    const processingPromises = batch.map(item => this.processPacketItem(item));
    await Promise.allSettled(processingPromises);

    const processingTime = Date.now() - startTime;
    this.updateProcessingStats(processingTime);
  }

  private getNextBatch(): PacketQueueItem[] {
    const batch: PacketQueueItem[] = [];
    let batchSize = 0;

    // Process high priority first, then medium, then low
    const priorities: ('high' | 'medium' | 'low')[] = ['high', 'medium', 'low'];
    
    for (const priority of priorities) {
      const queue = this.priorityQueues[priority];
      const weight = this.config.priorityWeights[priority];
      const maxFromThisPriority = Math.ceil((this.config.batchSize * weight) / 6); // Total weight is 6

      while (queue.length > 0 && batchSize < this.config.batchSize && batch.length < maxFromThisPriority) {
        const item = queue.shift();
        if (item) {
          batch.push(item);
          batchSize++;
        }
      }
    }

    return batch;
  }

  private async processPacketItem(item: PacketQueueItem): Promise<void> {
    try {
      const startTime = Date.now();

      // Route the packet through the traffic router
      const success = await this.trafficRouter.routePacket(item.packet);

      if (success) {
        // Remove from queue
        this.packetQueue.delete(item.id);
        this.stats.packetsProcessed++;
        
        console.log(`Packet processed successfully: ${item.id}`);
        this.emit('packetProcessed', item);
      } else {
        // Retry if possible
        await this.handlePacketFailure(item);
      }

      const processingTime = Date.now() - startTime;
      this.processingTimes.push(processingTime);
    } catch (error) {
      console.error(`Failed to process packet ${item.id}:`, error);
      await this.handlePacketFailure(item);
    }
  }

  private async handlePacketFailure(item: PacketQueueItem): Promise<void> {
    item.retryCount++;

    if (item.retryCount <= item.maxRetries) {
      // Re-queue for retry
      this.priorityQueues[item.priority].push(item);
      console.log(`Packet ${item.id} queued for retry (attempt ${item.retryCount})`);
    } else {
      // Drop packet after max retries
      this.packetQueue.delete(item.id);
      this.stats.packetsDropped++;
      console.warn(`Packet ${item.id} dropped after ${item.retryCount} retries`);
      this.emit('packetDropped', item);
    }
  }

  // Memory Management
  private startMemoryManagement(): void {
    this.memoryCleanupInterval = setInterval(() => {
      this.performMemoryCleanup();
    }, this.config.memoryCleanupInterval);
  }

  private performMemoryCleanup(): void {
    const now = Date.now();
    const maxAge = 60000; // 1 minute
    let cleanedCount = 0;

    // Remove old packets from queue
    for (const [id, item] of this.packetQueue) {
      if (now - item.timestamp > maxAge) {
        this.packetQueue.delete(id);
        this.removeFromPriorityQueue(item);
        cleanedCount++;
      }
    }

    // Trim processing times array
    if (this.processingTimes.length > 1000) {
      this.processingTimes = this.processingTimes.slice(-1000);
    }

    if (cleanedCount > 0) {
      console.log(`Memory cleanup: removed ${cleanedCount} old packets`);
    }

    this.updateMemoryStats();
  }

  private removeFromPriorityQueue(item: PacketQueueItem): void {
    const queue = this.priorityQueues[item.priority];
    const index = queue.findIndex(qItem => qItem.id === item.id);
    if (index !== -1) {
      queue.splice(index, 1);
    }
  }

  // Background Processing Adjustments
  private adjustProcessingForBackground(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    // Reduce processing frequency in background
    this.processingInterval = setInterval(() => {
      this.processPacketBatch();
    }, this.config.processingInterval * 10); // 10x slower
    
    console.log('Adjusted packet processing for background mode');
  }

  private pauseProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('Packet processing paused');
  }

  private resumeNormalProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    this.startPacketProcessing();
    console.log('Resumed normal packet processing');
  }

  // Statistics
  private updateQueueStats(): void {
    this.stats.queueSize = this.packetQueue.size;
    this.stats.lastUpdated = Date.now();
  }

  private updateMemoryStats(): void {
    this.stats.memoryUsage = this.estimateMemoryUsage();
    this.stats.lastUpdated = Date.now();
  }

  private updateProcessingStats(processingTime: number): void {
    if (this.processingTimes.length > 0) {
      this.stats.averageProcessingTime = 
        this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length;
    }
    this.stats.lastUpdated = Date.now();
  }

  // Public API
  getStats(): TrafficStats {
    return { ...this.stats };
  }

  getQueueInfo(): {
    total: number;
    high: number;
    medium: number;
    low: number;
  } {
    return {
      total: this.packetQueue.size,
      high: this.priorityQueues.high.length,
      medium: this.priorityQueues.medium.length,
      low: this.priorityQueues.low.length
    };
  }

  clearQueue(): void {
    this.packetQueue.clear();
    this.priorityQueues.high = [];
    this.priorityQueues.medium = [];
    this.priorityQueues.low = [];
    this.updateQueueStats();
    console.log('Packet queue cleared');
  }

  updateConfig(config: Partial<TrafficServiceConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('Traffic Service config updated');
  }

  // Cleanup
  destroy(): void {
    this.isActive = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    if (this.memoryCleanupInterval) {
      clearInterval(this.memoryCleanupInterval);
    }
    
    this.clearQueue();
    AppState.removeEventListener('change', this.handleAppStateChange.bind(this));
    this.removeAllListeners();
  }
}

export default TrafficService;
