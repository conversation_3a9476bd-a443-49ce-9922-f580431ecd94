import { Platform } from 'react-native';
import ConnectionManager from '../../../services/networking/ConnectionManager';
import NativeBridgeManager from '../../../services/networking/native/NativeBridgeManager';

// Mock the native modules
jest.mock('../../../services/networking/native/NativeBridgeManager', () => ({
  initialize: jest.fn(),
  on: jest.fn(),
  startAllServices: jest.fn(),
  stopAllServices: jest.fn(),
  sendDataToPeer: jest.fn(),
  broadcastGameData: jest.fn(),
  getUnifiedNetworkStats: jest.fn(() => Promise.resolve({
    platform: Platform.OS,
    bytesReceived: 0,
    bytesSent: 0,
    packetsProcessed: 0,
    connectedPeers: 0,
    isTrafficInterceptionActive: false,
    isPeerToPeerActive: false,
    lastUpdated: Date.now()
  })),
  cleanup: jest.fn()
}));

jest.mock('../../../services/networking/BluetoothService', () => ({
  requestPermissions: jest.fn(() => Promise.resolve(true)),
  isEnabled: jest.fn(() => Promise.resolve(true)),
  startDiscovery: jest.fn(() => Promise.resolve()),
  stopDiscovery: jest.fn(() => Promise.resolve()),
  getDiscoveredDevices: jest.fn(() => Promise.resolve([])),
  connectToDevice: jest.fn(() => Promise.resolve(true)),
  isPaired: jest.fn(() => Promise.resolve(false)),
  pairDevice: jest.fn(() => Promise.resolve(true)),
  sendData: jest.fn(() => Promise.resolve(true))
}));

jest.mock('../../../services/networking/WiFiDirectService', () => ({
  requestPermissions: jest.fn(() => Promise.resolve(true)),
  initialize: jest.fn(() => Promise.resolve(true)),
  isAvailable: jest.fn(() => Platform.OS === 'android'),
  startPeerDiscovery: jest.fn(() => Promise.resolve()),
  stopPeerDiscovery: jest.fn(() => Promise.resolve()),
  getDiscoveredPeers: jest.fn(() => Promise.resolve([])),
  connectToPeer: jest.fn(() => Promise.resolve(true)),
  getConnectionInfo: jest.fn(() => Promise.resolve({ isConnected: true })),
  sendData: jest.fn(() => Promise.resolve(true))
}));

describe('ConnectionManager Integration Tests', () => {
  let connectionManager: typeof ConnectionManager;

  beforeEach(async () => {
    jest.clearAllMocks();
    connectionManager = ConnectionManager;
  });

  afterEach(async () => {
    await connectionManager.cleanup();
  });

  describe('Initialization with Native Bridge', () => {
    it('should initialize with native bridge support', async () => {
      const result = await connectionManager.initialize();
      
      expect(result).toBe(true);
      expect(connectionManager.isInitialized()).toBe(true);
      expect(NativeBridgeManager.initialize).toHaveBeenCalledWith({
        enableTrafficInterception: true,
        enablePeerToPeer: true,
        enableNetworkMonitoring: true,
        autoStartServices: false
      });
    });

    it('should setup native bridge event listeners', async () => {
      await connectionManager.initialize();
      
      expect(NativeBridgeManager.on).toHaveBeenCalledWith('peerConnected', expect.any(Function));
      expect(NativeBridgeManager.on).toHaveBeenCalledWith('peerDisconnected', expect.any(Function));
      expect(NativeBridgeManager.on).toHaveBeenCalledWith('dataReceived', expect.any(Function));
      expect(NativeBridgeManager.on).toHaveBeenCalledWith('gamingPacketDetected', expect.any(Function));
    });

    it('should handle native bridge initialization failure gracefully', async () => {
      (NativeBridgeManager.initialize as jest.Mock).mockRejectedValue(new Error('Native bridge error'));
      
      const result = await connectionManager.initialize();
      
      expect(result).toBe(false);
      expect(connectionManager.isInitialized()).toBe(false);
    });
  });

  describe('Enhanced Device Discovery', () => {
    beforeEach(async () => {
      await connectionManager.initialize();
    });

    it('should start enhanced discovery with native bridge', async () => {
      await connectionManager.startEnhancedDiscovery();
      
      expect(connectionManager.isDiscovering()).toBe(true);
      // Native bridge services should be started
      expect(NativeBridgeManager.startAllServices).toHaveBeenCalled();
    });

    it('should stop enhanced discovery and cleanup native bridge', async () => {
      await connectionManager.startEnhancedDiscovery();
      await connectionManager.stopDiscovery();
      
      expect(connectionManager.isDiscovering()).toBe(false);
      expect(NativeBridgeManager.stopAllServices).toHaveBeenCalled();
    });

    it('should discover devices through multiple protocols', async () => {
      await connectionManager.startEnhancedDiscovery();
      
      // Wait for discovery to process
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const devices = connectionManager.getAvailableDevices();
      expect(Array.isArray(devices)).toBe(true);
    });
  });

  describe('Native Bridge Event Handling', () => {
    beforeEach(async () => {
      await connectionManager.initialize();
    });

    it('should handle peer connected events', async () => {
      const mockCallback = jest.fn();
      connectionManager.onDeviceConnected(mockCallback);

      // Simulate native bridge peer connected event
      const mockEvent = {
        peerID: 'test-peer-123',
        state: 'connected',
        timestamp: Date.now()
      };

      // Get the callback that was registered with NativeBridgeManager
      const onPeerConnectedCall = (NativeBridgeManager.on as jest.Mock).mock.calls
        .find(call => call[0] === 'peerConnected');
      
      if (onPeerConnectedCall) {
        const callback = onPeerConnectedCall[1];
        callback(mockEvent);

        expect(mockCallback).toHaveBeenCalled();
        
        const connectedDevices = connectionManager.getConnectedDevices();
        expect(connectedDevices.some(device => device.id === mockEvent.peerID)).toBe(true);
      }
    });

    it('should handle peer disconnected events', async () => {
      const mockCallback = jest.fn();
      connectionManager.onDeviceDisconnected(mockCallback);

      // First connect a peer
      const connectEvent = {
        peerID: 'test-peer-456',
        state: 'connected',
        timestamp: Date.now()
      };

      const onPeerConnectedCall = (NativeBridgeManager.on as jest.Mock).mock.calls
        .find(call => call[0] === 'peerConnected');
      
      if (onPeerConnectedCall) {
        onPeerConnectedCall[1](connectEvent);
      }

      // Then disconnect
      const disconnectEvent = {
        peerID: 'test-peer-456',
        state: 'disconnected',
        timestamp: Date.now()
      };

      const onPeerDisconnectedCall = (NativeBridgeManager.on as jest.Mock).mock.calls
        .find(call => call[0] === 'peerDisconnected');
      
      if (onPeerDisconnectedCall) {
        onPeerDisconnectedCall[1](disconnectEvent);

        expect(mockCallback).toHaveBeenCalled();
        
        const connectedDevices = connectionManager.getConnectedDevices();
        expect(connectedDevices.some(device => device.id === disconnectEvent.peerID)).toBe(false);
      }
    });

    it('should handle data received events', async () => {
      const mockCallback = jest.fn();
      connectionManager.onDataReceived(mockCallback);

      const dataEvent = {
        data: JSON.stringify({ message: 'test data' }),
        fromPeer: 'test-peer-789',
        bytes: 25,
        timestamp: Date.now()
      };

      const onDataReceivedCall = (NativeBridgeManager.on as jest.Mock).mock.calls
        .find(call => call[0] === 'dataReceived');
      
      if (onDataReceivedCall) {
        onDataReceivedCall[1](dataEvent);

        expect(mockCallback).toHaveBeenCalledWith(
          dataEvent.fromPeer,
          { message: 'test data' }
        );
      }
    });

    it('should handle gaming packet detection events', async () => {
      const packetEvent = {
        sourceIP: '*************',
        destIP: '*************',
        protocol: 6,
        size: 1024,
        timestamp: Date.now()
      };

      const onGamingPacketCall = (NativeBridgeManager.on as jest.Mock).mock.calls
        .find(call => call[0] === 'gamingPacketDetected');
      
      if (onGamingPacketCall) {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
        
        onGamingPacketCall[1](packetEvent);

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Gaming packet detected')
        );

        consoleSpy.mockRestore();
      }
    });
  });

  describe('Enhanced Data Transmission', () => {
    beforeEach(async () => {
      await connectionManager.initialize();
    });

    it('should send data through native bridge when available', async () => {
      const testData = { message: 'test', timestamp: Date.now() };
      const deviceId = 'test-device-123';

      await connectionManager.sendData(deviceId, testData);

      expect(NativeBridgeManager.sendDataToPeer).toHaveBeenCalledWith(
        testData,
        deviceId
      );
    });

    it('should broadcast data through native bridge', async () => {
      const testData = { broadcast: 'message', timestamp: Date.now() };

      await connectionManager.broadcastData(testData);

      expect(NativeBridgeManager.broadcastGameData).toHaveBeenCalledWith(testData);
    });

    it('should fallback to traditional methods when native bridge fails', async () => {
      (NativeBridgeManager.sendDataToPeer as jest.Mock).mockRejectedValue(new Error('Native bridge error'));

      const testData = { message: 'fallback test' };
      const deviceId = 'test-device-456';

      // Should not throw error, should fallback gracefully
      await expect(connectionManager.sendData(deviceId, testData)).resolves.not.toThrow();
    });
  });

  describe('Network Statistics Integration', () => {
    beforeEach(async () => {
      await connectionManager.initialize();
    });

    it('should get enhanced network statistics', async () => {
      const stats = await connectionManager.getNetworkStats();

      expect(stats).toHaveProperty('platform');
      expect(stats).toHaveProperty('bytesReceived');
      expect(stats).toHaveProperty('bytesSent');
      expect(stats).toHaveProperty('connectedPeers');
      expect(stats).toHaveProperty('isTrafficInterceptionActive');
      expect(stats).toHaveProperty('isPeerToPeerActive');

      expect(NativeBridgeManager.getUnifiedNetworkStats).toHaveBeenCalled();
    });

    it('should handle network stats errors gracefully', async () => {
      (NativeBridgeManager.getUnifiedNetworkStats as jest.Mock).mockRejectedValue(new Error('Stats error'));

      const stats = await connectionManager.getNetworkStats();

      // Should return default stats structure
      expect(stats).toHaveProperty('platform');
      expect(stats.platform).toBe(Platform.OS);
    });
  });

  describe('Cleanup and Resource Management', () => {
    it('should cleanup native bridge resources', async () => {
      await connectionManager.initialize();
      await connectionManager.startEnhancedDiscovery();
      
      await connectionManager.cleanup();

      expect(NativeBridgeManager.cleanup).toHaveBeenCalled();
    });

    it('should handle cleanup errors gracefully', async () => {
      await connectionManager.initialize();
      
      (NativeBridgeManager.cleanup as jest.Mock).mockRejectedValue(new Error('Cleanup error'));

      await expect(connectionManager.cleanup()).resolves.not.toThrow();
    });
  });

  describe('Platform-Specific Behavior', () => {
    it('should use iOS Multipeer Connectivity on iOS', async () => {
      if (Platform.OS === 'ios') {
        await connectionManager.initialize();
        await connectionManager.startEnhancedDiscovery();

        // Should have called native bridge initialization
        expect(NativeBridgeManager.initialize).toHaveBeenCalled();
        expect(NativeBridgeManager.startAllServices).toHaveBeenCalled();
      }
    });

    it('should use Android VPN Service on Android', async () => {
      if (Platform.OS === 'android') {
        await connectionManager.initialize();
        await connectionManager.startEnhancedDiscovery();

        // Should have called native bridge initialization
        expect(NativeBridgeManager.initialize).toHaveBeenCalled();
        expect(NativeBridgeManager.startAllServices).toHaveBeenCalled();
      }
    });

    it('should handle web platform gracefully', async () => {
      if (Platform.OS === 'web') {
        await connectionManager.initialize();
        
        // Should initialize without native bridge features
        expect(connectionManager.isInitialized()).toBe(true);
      }
    });
  });
});
