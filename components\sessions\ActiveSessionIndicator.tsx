import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { GameSession, SessionStatus } from '../../services/sessions/SessionManager';

interface ActiveSessionIndicatorProps {
  onPress?: () => void;
  position?: 'top' | 'bottom';
  style?: any;
}

export default function ActiveSessionIndicator({
  onPress,
  position = 'top',
  style,
}: ActiveSessionIndicatorProps) {
  const currentSession = useSelector((state: RootState) => state.session.currentSession);
  const [pulseAnim] = useState(new Animated.Value(1));
  const [slideAnim] = useState(new Animated.Value(-100));

  useEffect(() => {
    if (currentSession) {
      // Slide in animation
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Pulse animation for active sessions
      if (currentSession.status === SessionStatus.ACTIVE) {
        const pulseAnimation = Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
          ])
        );
        pulseAnimation.start();
        return () => pulseAnimation.stop();
      }
    } else {
      // Slide out animation
      Animated.timing(slideAnim, {
        toValue: position === 'top' ? -100 : 100,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [currentSession, position, pulseAnim, slideAnim]);

  if (!currentSession) {
    return null;
  }

  const getStatusColor = (status: SessionStatus): string => {
    switch (status) {
      case SessionStatus.WAITING:
        return '#FFB800';
      case SessionStatus.STARTING:
        return '#00D4FF';
      case SessionStatus.ACTIVE:
        return '#00FF88';
      case SessionStatus.PAUSED:
        return '#FF8C00';
      default:
        return '#CCCCCC';
    }
  };

  const getStatusIcon = (status: SessionStatus): keyof typeof Ionicons.glyphMap => {
    switch (status) {
      case SessionStatus.WAITING:
        return 'time';
      case SessionStatus.STARTING:
        return 'play-circle';
      case SessionStatus.ACTIVE:
        return 'radio-button-on';
      case SessionStatus.PAUSED:
        return 'pause-circle';
      default:
        return 'help-circle';
    }
  };

  const getStatusText = (status: SessionStatus): string => {
    switch (status) {
      case SessionStatus.WAITING:
        return 'Waiting for players';
      case SessionStatus.STARTING:
        return 'Starting game...';
      case SessionStatus.ACTIVE:
        return 'Game in progress';
      case SessionStatus.PAUSED:
        return 'Game paused';
      default:
        return 'Unknown status';
    }
  };

  const formatDuration = (session: GameSession): string => {
    if (!session.timestamps.started) return '';
    
    const now = Date.now();
    const elapsed = now - session.timestamps.started;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        position === 'top' ? styles.topPosition : styles.bottomPosition,
        {
          transform: [
            { translateY: slideAnim },
            { scale: pulseAnim },
          ],
        },
        style,
      ]}
    >
      <TouchableOpacity onPress={onPress} style={styles.touchable}>
        <BlurView intensity={30} style={styles.blurContainer}>
          <View style={styles.content}>
            <View style={styles.leftSection}>
              <View style={[
                styles.statusIndicator,
                { backgroundColor: getStatusColor(currentSession.status) }
              ]}>
                <Ionicons
                  name={getStatusIcon(currentSession.status)}
                  size={16}
                  color="#FFFFFF"
                />
              </View>
              
              <View style={styles.sessionInfo}>
                <Text style={styles.gameName} numberOfLines={1}>
                  {currentSession.gameName}
                </Text>
                <Text style={styles.statusText} numberOfLines={1}>
                  {getStatusText(currentSession.status)}
                </Text>
              </View>
            </View>

            <View style={styles.rightSection}>
              {currentSession.status === SessionStatus.ACTIVE && (
                <View style={styles.durationContainer}>
                  <Ionicons name="timer" size={14} color="rgba(255, 255, 255, 0.7)" />
                  <Text style={styles.durationText}>
                    {formatDuration(currentSession)}
                  </Text>
                </View>
              )}
              
              <View style={styles.participantCount}>
                <Ionicons name="people" size={14} color="rgba(255, 255, 255, 0.7)" />
                <Text style={styles.countText}>
                  {currentSession.participants.size}
                </Text>
              </View>
              
              <Ionicons
                name="chevron-forward"
                size={16}
                color="rgba(255, 255, 255, 0.5)"
              />
            </View>
          </View>
        </BlurView>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 16,
    right: 16,
    zIndex: 1000,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  topPosition: {
    top: 60, // Below status bar and header
  },
  bottomPosition: {
    bottom: 100, // Above tab bar
  },
  touchable: {
    flex: 1,
  },
  blurContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 12,
  },
  statusIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sessionInfo: {
    flex: 1,
  },
  gameName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  statusText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(0, 255, 136, 0.2)',
    borderRadius: 12,
  },
  durationText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#00FF88',
  },
  participantCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
  },
});
