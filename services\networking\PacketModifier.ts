import { EventEmitter } from 'events';

export interface PacketHeader {
  version: number;
  protocol: 'tcp' | 'udp' | 'http' | 'https';
  sourceIP: string;
  sourcePort: number;
  destinationIP: string;
  destinationPort: number;
  length: number;
  checksum: number;
  flags?: number;
  sequenceNumber?: number;
  acknowledgmentNumber?: number;
}

export interface PacketPayload {
  data: Buffer;
  encoding: 'binary' | 'utf8' | 'base64';
  contentType?: string;
  gameProtocol?: string;
}

export interface NetworkPacket {
  id: string;
  timestamp: number;
  direction: 'inbound' | 'outbound';
  header: PacketHeader;
  payload: PacketPayload;
  originalSize: number;
  gameId?: string;
  sessionId?: string;
}

export interface ModificationRule {
  id: string;
  name: string;
  gameId?: string;
  protocol: string;
  conditions: {
    sourceIP?: string;
    destinationIP?: string;
    port?: number;
    contentPattern?: RegExp;
    headerField?: string;
  };
  modifications: {
    replaceIP?: { from: string; to: string };
    replacePort?: { from: number; to: number };
    replaceContent?: { pattern: RegExp; replacement: string };
    addHeaders?: { [key: string]: string };
    removeHeaders?: string[];
    modifyChecksum?: boolean;
  };
  enabled: boolean;
  priority: number;
}

export interface ModificationResult {
  success: boolean;
  originalPacket: NetworkPacket;
  modifiedPacket?: NetworkPacket;
  appliedRules: string[];
  errors: string[];
  processingTime: number;
}

class PacketModifier extends EventEmitter {
  private static instance: PacketModifier;
  private rules: Map<string, ModificationRule> = new Map();
  private isActive: boolean = false;
  private stats = {
    totalPackets: 0,
    modifiedPackets: 0,
    failedModifications: 0,
    averageProcessingTime: 0,
    rulesApplied: 0
  };
  private processingTimes: number[] = [];

  private constructor() {
    super();
    this.initializeDefaultRules();
  }

  static getInstance(): PacketModifier {
    if (!PacketModifier.instance) {
      PacketModifier.instance = new PacketModifier();
    }
    return PacketModifier.instance;
  }

  // Core Packet Modification
  async modifyPacket(packet: NetworkPacket): Promise<ModificationResult> {
    const startTime = Date.now();
    this.stats.totalPackets++;

    const result: ModificationResult = {
      success: false,
      originalPacket: packet,
      appliedRules: [],
      errors: [],
      processingTime: 0
    };

    try {
      if (!this.isActive) {
        result.success = true;
        result.modifiedPacket = packet;
        return result;
      }

      // Find applicable rules
      const applicableRules = this.findApplicableRules(packet);
      if (applicableRules.length === 0) {
        result.success = true;
        result.modifiedPacket = packet;
        return result;
      }

      // Apply modifications
      let modifiedPacket = this.clonePacket(packet);
      
      for (const rule of applicableRules) {
        try {
          modifiedPacket = await this.applyRule(modifiedPacket, rule);
          result.appliedRules.push(rule.id);
          this.stats.rulesApplied++;
        } catch (error) {
          const errorMsg = `Failed to apply rule ${rule.id}: ${error}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      // Recalculate checksum if packet was modified
      if (result.appliedRules.length > 0) {
        modifiedPacket = await this.recalculateChecksum(modifiedPacket);
        this.stats.modifiedPackets++;
      }

      result.success = true;
      result.modifiedPacket = modifiedPacket;
      
      console.log(`Modified packet ${packet.id} with ${result.appliedRules.length} rules`);
      this.emit('packetModified', result);

    } catch (error) {
      result.errors.push(`Packet modification failed: ${error}`);
      this.stats.failedModifications++;
      console.error(`Failed to modify packet ${packet.id}:`, error);
    } finally {
      result.processingTime = Date.now() - startTime;
      this.updateStats(result.processingTime);
    }

    return result;
  }

  // Rule Management
  addRule(rule: ModificationRule): void {
    this.rules.set(rule.id, rule);
    console.log(`Added packet modification rule: ${rule.name}`);
    this.emit('ruleAdded', rule);
  }

  removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      console.log(`Removed packet modification rule: ${ruleId}`);
      this.emit('ruleRemoved', ruleId);
    }
    return removed;
  }

  updateRule(ruleId: string, updates: Partial<ModificationRule>): boolean {
    const rule = this.rules.get(ruleId);
    if (rule) {
      Object.assign(rule, updates);
      console.log(`Updated packet modification rule: ${ruleId}`);
      this.emit('ruleUpdated', rule);
      return true;
    }
    return false;
  }

  enableRule(ruleId: string): boolean {
    return this.updateRule(ruleId, { enabled: true });
  }

  disableRule(ruleId: string): boolean {
    return this.updateRule(ruleId, { enabled: false });
  }

  // Game-Specific Rule Management
  addGameRules(gameId: string, rules: Omit<ModificationRule, 'id'>[]): void {
    rules.forEach((rule, index) => {
      const fullRule: ModificationRule = {
        ...rule,
        id: `${gameId}_rule_${index}`,
        gameId
      };
      this.addRule(fullRule);
    });
    console.log(`Added ${rules.length} rules for game ${gameId}`);
  }

  removeGameRules(gameId: string): number {
    let removedCount = 0;
    for (const [ruleId, rule] of this.rules) {
      if (rule.gameId === gameId) {
        this.rules.delete(ruleId);
        removedCount++;
      }
    }
    console.log(`Removed ${removedCount} rules for game ${gameId}`);
    return removedCount;
  }

  // Private Helper Methods
  private findApplicableRules(packet: NetworkPacket): ModificationRule[] {
    const applicableRules: ModificationRule[] = [];

    for (const rule of this.rules.values()) {
      if (!rule.enabled) continue;

      if (this.ruleMatches(packet, rule)) {
        applicableRules.push(rule);
      }
    }

    // Sort by priority (higher priority first)
    return applicableRules.sort((a, b) => b.priority - a.priority);
  }

  private ruleMatches(packet: NetworkPacket, rule: ModificationRule): boolean {
    const { conditions } = rule;

    // Check protocol
    if (rule.protocol !== '*' && packet.header.protocol !== rule.protocol) {
      return false;
    }

    // Check game ID
    if (rule.gameId && packet.gameId !== rule.gameId) {
      return false;
    }

    // Check source IP
    if (conditions.sourceIP && packet.header.sourceIP !== conditions.sourceIP) {
      return false;
    }

    // Check destination IP
    if (conditions.destinationIP && packet.header.destinationIP !== conditions.destinationIP) {
      return false;
    }

    // Check port
    if (conditions.port && 
        packet.header.sourcePort !== conditions.port && 
        packet.header.destinationPort !== conditions.port) {
      return false;
    }

    // Check content pattern
    if (conditions.contentPattern) {
      const content = packet.payload.data.toString(packet.payload.encoding);
      if (!conditions.contentPattern.test(content)) {
        return false;
      }
    }

    return true;
  }

  private async applyRule(packet: NetworkPacket, rule: ModificationRule): Promise<NetworkPacket> {
    const modifiedPacket = this.clonePacket(packet);
    const { modifications } = rule;

    // Replace IP addresses
    if (modifications.replaceIP) {
      if (modifiedPacket.header.sourceIP === modifications.replaceIP.from) {
        modifiedPacket.header.sourceIP = modifications.replaceIP.to;
      }
      if (modifiedPacket.header.destinationIP === modifications.replaceIP.from) {
        modifiedPacket.header.destinationIP = modifications.replaceIP.to;
      }
    }

    // Replace ports
    if (modifications.replacePort) {
      if (modifiedPacket.header.sourcePort === modifications.replacePort.from) {
        modifiedPacket.header.sourcePort = modifications.replacePort.to;
      }
      if (modifiedPacket.header.destinationPort === modifications.replacePort.from) {
        modifiedPacket.header.destinationPort = modifications.replacePort.to;
      }
    }

    // Replace content
    if (modifications.replaceContent) {
      const content = modifiedPacket.payload.data.toString(modifiedPacket.payload.encoding);
      const newContent = content.replace(
        modifications.replaceContent.pattern,
        modifications.replaceContent.replacement
      );
      modifiedPacket.payload.data = Buffer.from(newContent, modifiedPacket.payload.encoding);
      modifiedPacket.header.length = modifiedPacket.payload.data.length;
    }

    return modifiedPacket;
  }

  private async recalculateChecksum(packet: NetworkPacket): Promise<NetworkPacket> {
    // Calculate new checksum based on modified packet
    const modifiedPacket = this.clonePacket(packet);
    
    // Simple checksum calculation (in real implementation, this would be protocol-specific)
    let checksum = 0;
    const data = modifiedPacket.payload.data;
    
    for (let i = 0; i < data.length; i += 2) {
      const word = (data[i] << 8) + (data[i + 1] || 0);
      checksum += word;
    }
    
    // Add header fields to checksum
    checksum += modifiedPacket.header.sourcePort;
    checksum += modifiedPacket.header.destinationPort;
    checksum += modifiedPacket.header.length;
    
    // Fold 32-bit checksum to 16 bits
    while (checksum >> 16) {
      checksum = (checksum & 0xFFFF) + (checksum >> 16);
    }
    
    modifiedPacket.header.checksum = ~checksum & 0xFFFF;
    
    return modifiedPacket;
  }

  private clonePacket(packet: NetworkPacket): NetworkPacket {
    return {
      ...packet,
      header: { ...packet.header },
      payload: {
        ...packet.payload,
        data: Buffer.from(packet.payload.data)
      }
    };
  }

  private initializeDefaultRules(): void {
    // Minecraft server redirection
    this.addRule({
      id: 'minecraft_server_redirect',
      name: 'Minecraft Server Redirection',
      gameId: 'minecraft',
      protocol: 'tcp',
      conditions: {
        destinationIP: '.*\\.mojang\\.com',
        port: 25565
      },
      modifications: {
        replaceIP: { from: '.*\\.mojang\\.com', to: '127.0.0.1' }
      },
      enabled: true,
      priority: 10
    });

    // Among Us matchmaker redirection
    this.addRule({
      id: 'among_us_matchmaker_redirect',
      name: 'Among Us Matchmaker Redirection',
      gameId: 'among_us',
      protocol: 'udp',
      conditions: {
        destinationIP: 'matchmaker.innersloth.com',
        port: 22023
      },
      modifications: {
        replaceIP: { from: 'matchmaker.innersloth.com', to: '127.0.0.1' }
      },
      enabled: true,
      priority: 10
    });

    console.log('Initialized default packet modification rules');
  }

  private updateStats(processingTime: number): void {
    this.processingTimes.push(processingTime);
    
    // Keep only last 1000 processing times
    if (this.processingTimes.length > 1000) {
      this.processingTimes = this.processingTimes.slice(-1000);
    }

    this.stats.averageProcessingTime = 
      this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length;
  }

  // Public API
  start(): void {
    this.isActive = true;
    console.log('Packet Modifier started');
    this.emit('started');
  }

  stop(): void {
    this.isActive = false;
    console.log('Packet Modifier stopped');
    this.emit('stopped');
  }

  getStats() {
    return { ...this.stats };
  }

  getRules(): ModificationRule[] {
    return Array.from(this.rules.values());
  }

  isRunning(): boolean {
    return this.isActive;
  }

  // Cleanup
  destroy(): void {
    this.stop();
    this.rules.clear();
    this.removeAllListeners();
  }
}

export default PacketModifier;
