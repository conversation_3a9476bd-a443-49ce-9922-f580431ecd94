import { Platform } from 'react-native';
import * as Device from 'expo-device';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface DeviceInfo {
  deviceName: string;
  deviceType: string;
  osName: string;
  osVersion: string;
  modelName: string;
  brand: string;
  manufacturer: string;
  totalMemory: number;
  usedMemory: number;
  availableMemory: number;
  totalStorage: number;
  usedStorage: number;
  availableStorage: number;
  batteryLevel: number;
  isCharging: boolean;
  networkType: string;
  screenResolution: string;
  deviceId: string;
}

export interface StorageInfo {
  totalSpace: number;
  usedSpace: number;
  availableSpace: number;
  appSize: number;
  cacheSize: number;
  documentsSize: number;
  breakdown: {
    app: number;
    cache: number;
    documents: number;
    other: number;
  };
}

export interface PerformanceMetrics {
  cpuUsage: number;
  memoryUsage: number;
  batteryUsage: number;
  networkUsage: {
    sent: number;
    received: number;
  };
  appStartupTime: number;
  frameRate: number;
}

class DeviceInfoService {
  private static instance: DeviceInfoService;
  private deviceInfo: DeviceInfo | null = null;
  private performanceMetrics: PerformanceMetrics | null = null;

  static getInstance(): DeviceInfoService {
    if (!DeviceInfoService.instance) {
      DeviceInfoService.instance = new DeviceInfoService();
    }
    return DeviceInfoService.instance;
  }

  async getDeviceInfo(): Promise<DeviceInfo> {
    if (this.deviceInfo) {
      return this.deviceInfo;
    }

    try {
      // Get basic device information
      const deviceName = Device.deviceName || 'Unknown Device';
      const deviceType = this.getDeviceType();
      const osName = Platform.OS === 'ios' ? 'iOS' : 'Android';
      const osVersion = Device.osVersion || 'Unknown';
      const modelName = Device.modelName || 'Unknown Model';
      const brand = Device.brand || 'Unknown Brand';
      const manufacturer = Device.manufacturer || 'Unknown Manufacturer';

      // Get memory information (mock data for now)
      const memoryInfo = await this.getMemoryInfo();
      const storageInfo = await this.getStorageInfo();
      const batteryInfo = await this.getBatteryInfo();
      const networkInfo = await this.getNetworkInfo();
      const screenInfo = await this.getScreenInfo();

      this.deviceInfo = {
        deviceName,
        deviceType,
        osName,
        osVersion,
        modelName,
        brand,
        manufacturer,
        totalMemory: memoryInfo.total,
        usedMemory: memoryInfo.used,
        availableMemory: memoryInfo.available,
        totalStorage: storageInfo.totalSpace,
        usedStorage: storageInfo.usedSpace,
        availableStorage: storageInfo.availableSpace,
        batteryLevel: batteryInfo.level,
        isCharging: batteryInfo.isCharging,
        networkType: networkInfo.type,
        screenResolution: screenInfo.resolution,
        deviceId: await this.getDeviceId(),
      };

      return this.deviceInfo;
    } catch (error) {
      console.error('Failed to get device info:', error);
      return this.getMockDeviceInfo();
    }
  }

  private getDeviceType(): string {
    if (Platform.OS === 'ios') {
      return Device.deviceType === Device.DeviceType.TABLET ? 'iPad' : 'iPhone';
    } else {
      return Device.deviceType === Device.DeviceType.TABLET ? 'Android Tablet' : 'Android Phone';
    }
  }

  private async getMemoryInfo(): Promise<{ total: number; used: number; available: number }> {
    // In a real implementation, you would use native modules to get actual memory info
    // For now, return mock data
    const total = 8 * 1024 * 1024 * 1024; // 8GB
    const used = Math.floor(total * 0.6); // 60% used
    const available = total - used;

    return { total, used, available };
  }

  async getStorageInfo(): Promise<StorageInfo> {
    try {
      // In a real implementation, you would use native modules to get actual storage info
      // For now, return mock data based on typical device storage
      const totalSpace = 128 * 1024 * 1024 * 1024; // 128GB
      const usedSpace = Math.floor(totalSpace * 0.4); // 40% used
      const availableSpace = totalSpace - usedSpace;

      // Estimate app-specific storage
      const appSize = 150 * 1024 * 1024; // 150MB
      const cacheSize = await this.getCacheSize();
      const documentsSize = await this.getDocumentsSize();

      return {
        totalSpace,
        usedSpace,
        availableSpace,
        appSize,
        cacheSize,
        documentsSize,
        breakdown: {
          app: appSize,
          cache: cacheSize,
          documents: documentsSize,
          other: usedSpace - appSize - cacheSize - documentsSize,
        },
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return this.getMockStorageInfo();
    }
  }

  private async getCacheSize(): Promise<number> {
    // Estimate cache size based on AsyncStorage usage
    try {
      const keys = await AsyncStorage.getAllKeys();
      let totalSize = 0;
      
      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += new Blob([value]).size;
        }
      }
      
      return totalSize;
    } catch (error) {
      return 10 * 1024 * 1024; // 10MB default
    }
  }

  private async getDocumentsSize(): Promise<number> {
    // In a real implementation, you would calculate actual documents size
    return 5 * 1024 * 1024; // 5MB default
  }

  private async getBatteryInfo(): Promise<{ level: number; isCharging: boolean }> {
    // In a real implementation, you would use expo-battery or native modules
    return {
      level: Math.floor(Math.random() * 100),
      isCharging: Math.random() > 0.5,
    };
  }

  private async getNetworkInfo(): Promise<{ type: string }> {
    // In a real implementation, you would use @react-native-community/netinfo
    const types = ['WiFi', '4G', '5G', 'Ethernet'];
    return {
      type: types[Math.floor(Math.random() * types.length)],
    };
  }

  private async getScreenInfo(): Promise<{ resolution: string }> {
    const { Dimensions } = require('react-native');
    const { width, height } = Dimensions.get('screen');
    return {
      resolution: `${width}x${height}`,
    };
  }

  private async getDeviceId(): Promise<string> {
    try {
      let deviceId = await AsyncStorage.getItem('@device_id');
      if (!deviceId) {
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        await AsyncStorage.setItem('@device_id', deviceId);
      }
      return deviceId;
    } catch (error) {
      return `device_${Date.now()}`;
    }
  }

  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    if (this.performanceMetrics) {
      return this.performanceMetrics;
    }

    // In a real implementation, you would collect actual performance metrics
    this.performanceMetrics = {
      cpuUsage: Math.floor(Math.random() * 100),
      memoryUsage: Math.floor(Math.random() * 100),
      batteryUsage: Math.floor(Math.random() * 100),
      networkUsage: {
        sent: Math.floor(Math.random() * 1000000),
        received: Math.floor(Math.random() * 1000000),
      },
      appStartupTime: 1200 + Math.floor(Math.random() * 800), // 1.2-2.0 seconds
      frameRate: 58 + Math.floor(Math.random() * 4), // 58-62 FPS
    };

    return this.performanceMetrics;
  }

  async clearCache(): Promise<boolean> {
    try {
      // Clear app cache (in a real implementation, you would clear actual cache)
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('@cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
      
      // Reset cached data
      this.deviceInfo = null;
      this.performanceMetrics = null;
      
      return true;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return false;
    }
  }

  async optimizeStorage(): Promise<boolean> {
    try {
      // Perform storage optimization
      await this.clearCache();
      
      // In a real implementation, you would:
      // - Clear temporary files
      // - Compress old data
      // - Remove unused assets
      
      return true;
    } catch (error) {
      console.error('Failed to optimize storage:', error);
      return false;
    }
  }

  private getMockDeviceInfo(): DeviceInfo {
    return {
      deviceName: 'Mock Device',
      deviceType: Platform.OS === 'ios' ? 'iPhone' : 'Android Phone',
      osName: Platform.OS === 'ios' ? 'iOS' : 'Android',
      osVersion: '17.0',
      modelName: 'Mock Model',
      brand: 'Mock Brand',
      manufacturer: 'Mock Manufacturer',
      totalMemory: 8 * 1024 * 1024 * 1024,
      usedMemory: 4 * 1024 * 1024 * 1024,
      availableMemory: 4 * 1024 * 1024 * 1024,
      totalStorage: 128 * 1024 * 1024 * 1024,
      usedStorage: 50 * 1024 * 1024 * 1024,
      availableStorage: 78 * 1024 * 1024 * 1024,
      batteryLevel: 75,
      isCharging: false,
      networkType: 'WiFi',
      screenResolution: '390x844',
      deviceId: 'mock_device_id',
    };
  }

  private getMockStorageInfo(): StorageInfo {
    const totalSpace = 128 * 1024 * 1024 * 1024;
    const usedSpace = 50 * 1024 * 1024 * 1024;
    const availableSpace = totalSpace - usedSpace;
    const appSize = 150 * 1024 * 1024;
    const cacheSize = 10 * 1024 * 1024;
    const documentsSize = 5 * 1024 * 1024;

    return {
      totalSpace,
      usedSpace,
      availableSpace,
      appSize,
      cacheSize,
      documentsSize,
      breakdown: {
        app: appSize,
        cache: cacheSize,
        documents: documentsSize,
        other: usedSpace - appSize - cacheSize - documentsSize,
      },
    };
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default DeviceInfoService.getInstance();
