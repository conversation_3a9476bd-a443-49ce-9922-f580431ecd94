import { Platform } from 'react-native';
import { EventEmitter } from 'events';

export interface PerformanceConfig {
  enableMemoryOptimization: boolean;
  enableBatteryOptimization: boolean;
  enableNetworkOptimization: boolean;
  enableRenderingOptimization: boolean;
  maxMemoryUsage: number; // in MB
  maxCacheSize: number; // in MB
  backgroundTaskTimeout: number; // in ms
  networkRequestTimeout: number; // in ms
}

export interface MemoryMetrics {
  used: number;
  total: number;
  available: number;
  percentage: number;
  timestamp: number;
}

export interface BatteryMetrics {
  level: number;
  isCharging: boolean;
  chargingTime?: number;
  dischargingTime?: number;
  timestamp: number;
}

export interface NetworkMetrics {
  type: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
  timestamp: number;
}

export interface PerformanceOptimization {
  id: string;
  type: 'memory' | 'battery' | 'network' | 'rendering' | 'storage';
  action: string;
  impact: 'low' | 'medium' | 'high';
  appliedAt: number;
  result: {
    success: boolean;
    improvement?: number;
    error?: string;
  };
}

class PerformanceOptimizer extends EventEmitter {
  private static instance: PerformanceOptimizer;
  private config: PerformanceConfig;
  private isInitialized: boolean = false;
  private isOptimizing: boolean = false;
  private memoryCache: Map<string, { data: any; timestamp: number; size: number }> = new Map();
  private backgroundTasks: Map<string, NodeJS.Timeout> = new Map();
  private optimizationHistory: PerformanceOptimization[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private currentMetrics: {
    memory?: MemoryMetrics;
    battery?: BatteryMetrics;
    network?: NetworkMetrics;
  } = {};

  private constructor() {
    super();
    this.config = {
      enableMemoryOptimization: true,
      enableBatteryOptimization: true,
      enableNetworkOptimization: true,
      enableRenderingOptimization: true,
      maxMemoryUsage: 512, // 512 MB
      maxCacheSize: 100, // 100 MB
      backgroundTaskTimeout: 30000, // 30 seconds
      networkRequestTimeout: 10000 // 10 seconds
    };
  }

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  // Initialization
  async initialize(config?: Partial<PerformanceConfig>): Promise<void> {
    try {
      console.log('Initializing Performance Optimizer...');

      // Update configuration
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Start performance monitoring
      await this.startPerformanceMonitoring();

      // Setup optimization triggers
      this.setupOptimizationTriggers();

      // Initial optimization pass
      await this.performInitialOptimization();

      this.isInitialized = true;
      this.emit('initialized');
      console.log('Performance Optimizer initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Performance Optimizer:', error);
      throw error;
    }
  }

  // Memory Optimization
  async optimizeMemory(): Promise<PerformanceOptimization> {
    const optimization: PerformanceOptimization = {
      id: this.generateOptimizationId(),
      type: 'memory',
      action: 'memory_cleanup',
      impact: 'medium',
      appliedAt: Date.now(),
      result: { success: false }
    };

    try {
      console.log('Starting memory optimization...');

      const beforeMetrics = await this.getMemoryMetrics();
      
      // Clear expired cache entries
      await this.clearExpiredCache();
      
      // Force garbage collection if available
      await this.forceGarbageCollection();
      
      // Clean up unused resources
      await this.cleanupUnusedResources();
      
      const afterMetrics = await this.getMemoryMetrics();
      const improvement = beforeMetrics.used - afterMetrics.used;
      
      optimization.result = {
        success: true,
        improvement: improvement
      };

      this.optimizationHistory.push(optimization);
      this.emit('memoryOptimized', optimization);
      console.log(`Memory optimization completed. Freed ${improvement} MB`);
      
      return optimization;
    } catch (error) {
      optimization.result = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      console.error('Memory optimization failed:', error);
      return optimization;
    }
  }

  private async clearExpiredCache(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.memoryCache) {
      // Cache entries expire after 1 hour
      if (now - entry.timestamp > 3600000) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.memoryCache.delete(key));
    console.log(`Cleared ${expiredKeys.length} expired cache entries`);
  }

  private async forceGarbageCollection(): Promise<void> {
    try {
      // Force garbage collection if available (Node.js/V8)
      if (global.gc) {
        global.gc();
        console.log('Forced garbage collection');
      } else if (Platform.OS === 'web' && (window as any).gc) {
        (window as any).gc();
        console.log('Forced garbage collection (web)');
      }
    } catch (error) {
      console.warn('Garbage collection not available:', error);
    }
  }

  private async cleanupUnusedResources(): Promise<void> {
    // Clean up background tasks that have been running too long
    const now = Date.now();
    const tasksToCleanup: string[] = [];
    
    for (const [taskId, timeout] of this.backgroundTasks) {
      // Clean up tasks older than the configured timeout
      if (now - parseInt(taskId.split('_')[1]) > this.config.backgroundTaskTimeout) {
        clearTimeout(timeout);
        tasksToCleanup.push(taskId);
      }
    }
    
    tasksToCleanup.forEach(taskId => this.backgroundTasks.delete(taskId));
    console.log(`Cleaned up ${tasksToCleanup.length} background tasks`);
  }

  // Battery Optimization
  async optimizeBattery(): Promise<PerformanceOptimization> {
    const optimization: PerformanceOptimization = {
      id: this.generateOptimizationId(),
      type: 'battery',
      action: 'battery_optimization',
      impact: 'high',
      appliedAt: Date.now(),
      result: { success: false }
    };

    try {
      console.log('Starting battery optimization...');

      const batteryMetrics = await this.getBatteryMetrics();
      
      // Reduce background activity if battery is low
      if (batteryMetrics.level < 20) {
        await this.reduceBatteryUsage();
        optimization.impact = 'high';
      } else if (batteryMetrics.level < 50) {
        await this.moderateBatteryUsage();
        optimization.impact = 'medium';
      } else {
        await this.normalBatteryUsage();
        optimization.impact = 'low';
      }

      optimization.result = { success: true };
      this.optimizationHistory.push(optimization);
      this.emit('batteryOptimized', optimization);
      console.log('Battery optimization completed');
      
      return optimization;
    } catch (error) {
      optimization.result = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      console.error('Battery optimization failed:', error);
      return optimization;
    }
  }

  private async reduceBatteryUsage(): Promise<void> {
    // Reduce update frequencies
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = setInterval(() => this.updateMetrics(), 60000); // 1 minute
    }
    
    // Pause non-essential background tasks
    console.log('Reduced battery usage - low battery mode activated');
  }

  private async moderateBatteryUsage(): Promise<void> {
    // Moderate update frequencies
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = setInterval(() => this.updateMetrics(), 30000); // 30 seconds
    }
    
    console.log('Moderate battery usage mode activated');
  }

  private async normalBatteryUsage(): Promise<void> {
    // Normal update frequencies
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = setInterval(() => this.updateMetrics(), 15000); // 15 seconds
    }
    
    console.log('Normal battery usage mode activated');
  }

  // Network Optimization
  async optimizeNetwork(): Promise<PerformanceOptimization> {
    const optimization: PerformanceOptimization = {
      id: this.generateOptimizationId(),
      type: 'network',
      action: 'network_optimization',
      impact: 'medium',
      appliedAt: Date.now(),
      result: { success: false }
    };

    try {
      console.log('Starting network optimization...');

      const networkMetrics = await this.getNetworkMetrics();
      
      // Optimize based on connection type
      if (networkMetrics.effectiveType === 'slow-2g' || networkMetrics.effectiveType === '2g') {
        await this.optimizeForSlowConnection();
        optimization.impact = 'high';
      } else if (networkMetrics.effectiveType === '3g') {
        await this.optimizeForMediumConnection();
        optimization.impact = 'medium';
      } else {
        await this.optimizeForFastConnection();
        optimization.impact = 'low';
      }

      optimization.result = { success: true };
      this.optimizationHistory.push(optimization);
      this.emit('networkOptimized', optimization);
      console.log('Network optimization completed');
      
      return optimization;
    } catch (error) {
      optimization.result = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      console.error('Network optimization failed:', error);
      return optimization;
    }
  }

  private async optimizeForSlowConnection(): Promise<void> {
    // Reduce image quality, disable auto-updates, etc.
    console.log('Optimized for slow connection');
  }

  private async optimizeForMediumConnection(): Promise<void> {
    // Moderate optimizations
    console.log('Optimized for medium connection');
  }

  private async optimizeForFastConnection(): Promise<void> {
    // Enable all features
    console.log('Optimized for fast connection');
  }

  // Cache Management
  setCacheItem(key: string, data: any, ttl: number = 3600000): void {
    try {
      const size = this.estimateDataSize(data);
      const currentCacheSize = this.getCurrentCacheSize();
      
      // Check if adding this item would exceed cache limit
      if (currentCacheSize + size > this.config.maxCacheSize * 1024 * 1024) {
        this.evictLeastRecentlyUsed();
      }
      
      this.memoryCache.set(key, {
        data,
        timestamp: Date.now(),
        size
      });
      
      console.log(`Cache item set: ${key} (${size} bytes)`);
    } catch (error) {
      console.error('Failed to set cache item:', error);
    }
  }

  getCacheItem(key: string): any | null {
    try {
      const entry = this.memoryCache.get(key);
      if (!entry) return null;
      
      // Check if expired
      if (Date.now() - entry.timestamp > 3600000) {
        this.memoryCache.delete(key);
        return null;
      }
      
      return entry.data;
    } catch (error) {
      console.error('Failed to get cache item:', error);
      return null;
    }
  }

  private getCurrentCacheSize(): number {
    let totalSize = 0;
    for (const entry of this.memoryCache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  private estimateDataSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch (error) {
      return 1024; // Default 1KB
    }
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();
    
    for (const [key, entry] of this.memoryCache) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
      console.log(`Evicted LRU cache item: ${oldestKey}`);
    }
  }

  // Metrics Collection
  private async getMemoryMetrics(): Promise<MemoryMetrics> {
    try {
      if (Platform.OS === 'web' && (performance as any).memory) {
        const memory = (performance as any).memory;
        return {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
          available: Math.round((memory.jsHeapSizeLimit - memory.usedJSHeapSize) / 1024 / 1024),
          percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100),
          timestamp: Date.now()
        };
      } else {
        // Fallback for other platforms
        return {
          used: 128,
          total: 512,
          available: 384,
          percentage: 25,
          timestamp: Date.now()
        };
      }
    } catch (error) {
      console.error('Failed to get memory metrics:', error);
      return {
        used: 0,
        total: 0,
        available: 0,
        percentage: 0,
        timestamp: Date.now()
      };
    }
  }

  private async getBatteryMetrics(): Promise<BatteryMetrics> {
    try {
      if (Platform.OS === 'web' && 'getBattery' in navigator) {
        const battery = await (navigator as any).getBattery();
        return {
          level: Math.round(battery.level * 100),
          isCharging: battery.charging,
          chargingTime: battery.chargingTime,
          dischargingTime: battery.dischargingTime,
          timestamp: Date.now()
        };
      } else {
        // Fallback for other platforms
        return {
          level: 75,
          isCharging: false,
          timestamp: Date.now()
        };
      }
    } catch (error) {
      console.error('Failed to get battery metrics:', error);
      return {
        level: 100,
        isCharging: false,
        timestamp: Date.now()
      };
    }
  }

  private async getNetworkMetrics(): Promise<NetworkMetrics> {
    try {
      if (Platform.OS === 'web' && 'connection' in navigator) {
        const connection = (navigator as any).connection;
        return {
          type: connection.type || 'unknown',
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
          timestamp: Date.now()
        };
      } else {
        // Fallback for other platforms
        return {
          type: 'wifi',
          effectiveType: '4g',
          timestamp: Date.now()
        };
      }
    } catch (error) {
      console.error('Failed to get network metrics:', error);
      return {
        type: 'unknown',
        timestamp: Date.now()
      };
    }
  }

  // Monitoring and Triggers
  private async startPerformanceMonitoring(): Promise<void> {
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
    }, 15000); // Update every 15 seconds
    
    console.log('Performance monitoring started');
  }

  private async updateMetrics(): Promise<void> {
    try {
      this.currentMetrics.memory = await this.getMemoryMetrics();
      this.currentMetrics.battery = await this.getBatteryMetrics();
      this.currentMetrics.network = await this.getNetworkMetrics();
      
      this.emit('metricsUpdated', this.currentMetrics);
    } catch (error) {
      console.error('Failed to update metrics:', error);
    }
  }

  private setupOptimizationTriggers(): void {
    this.on('metricsUpdated', (metrics) => {
      // Trigger optimizations based on metrics
      if (metrics.memory && metrics.memory.percentage > 80) {
        this.optimizeMemory();
      }
      
      if (metrics.battery && metrics.battery.level < 20) {
        this.optimizeBattery();
      }
    });
  }

  private async performInitialOptimization(): Promise<void> {
    console.log('Performing initial optimization...');
    
    await Promise.all([
      this.optimizeMemory(),
      this.optimizeBattery(),
      this.optimizeNetwork()
    ]);
    
    console.log('Initial optimization completed');
  }

  // Utility Methods
  private generateOptimizationId(): string {
    return `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Getters
  getCurrentMetrics(): typeof this.currentMetrics {
    return { ...this.currentMetrics };
  }

  getOptimizationHistory(): PerformanceOptimization[] {
    return [...this.optimizationHistory];
  }

  getCacheStats(): { size: number; items: number; hitRate: number } {
    return {
      size: this.getCurrentCacheSize(),
      items: this.memoryCache.size,
      hitRate: 0.85 // Would be calculated from actual cache hits/misses
    };
  }

  isInitializedState(): boolean {
    return this.isInitialized;
  }

  isOptimizingState(): boolean {
    return this.isOptimizing;
  }
}

export default PerformanceOptimizer.getInstance();
