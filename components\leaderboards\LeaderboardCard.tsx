import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

import { Leaderboard, LeaderboardEntry } from '../../services/leaderboards/LeaderboardManager';

interface LeaderboardCardProps {
  leaderboard: Leaderboard;
  onPress?: () => void;
  showTopPlayers?: number;
  currentPlayerId?: string;
}

export default function LeaderboardCard({
  leaderboard,
  onPress,
  showTopPlayers = 3,
  currentPlayerId
}: LeaderboardCardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return 'trophy';
      case 2: return 'medal';
      case 3: return 'ribbon';
      default: return 'person';
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return '#FFD700';
      case 2: return '#C0C0C0';
      case 3: return '#CD7F32';
      default: return '#CCCCCC';
    }
  };

  const formatScore = (score: number, category: string) => {
    switch (category) {
      case 'playtime':
        const hours = Math.floor(score / 60);
        const minutes = score % 60;
        return `${hours}h ${minutes}m`;
      case 'wins':
        return `${score} wins`;
      case 'achievements':
        return `${score} achievements`;
      default:
        return score.toLocaleString();
    }
  };

  const topPlayers = leaderboard.entries.slice(0, showTopPlayers);
  const currentPlayerEntry = currentPlayerId 
    ? leaderboard.entries.find(entry => entry.playerId === currentPlayerId)
    : null;

  const PlayerRow = ({ entry, index }: { entry: LeaderboardEntry; index: number }) => (
    <View style={[
      styles.playerRow,
      entry.playerId === currentPlayerId && styles.currentPlayerRow
    ]}>
      <View style={styles.rankContainer}>
        <View style={[
          styles.rankBadge,
          { backgroundColor: getRankColor(entry.rank) }
        ]}>
          <Ionicons 
            name={getRankIcon(entry.rank) as any} 
            size={entry.rank <= 3 ? 16 : 12} 
            color={entry.rank <= 3 ? '#000' : '#FFF'} 
          />
        </View>
        <Text style={styles.rankText}>#{entry.rank}</Text>
      </View>

      <View style={styles.playerInfo}>
        <Text style={[
          styles.playerName,
          entry.playerId === currentPlayerId && styles.currentPlayerName
        ]}>
          {entry.playerName}
        </Text>
      </View>

      <Text style={styles.playerScore}>
        {formatScore(entry.score, leaderboard.category)}
      </Text>
    </View>
  );

  const CardContent = () => (
    <BlurView intensity={15} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerInfo}>
          <Text style={styles.title}>{leaderboard.name}</Text>
          <Text style={styles.description}>{leaderboard.description}</Text>
        </View>
        
        <View style={styles.headerStats}>
          <Text style={styles.playerCount}>{leaderboard.totalPlayers}</Text>
          <Text style={styles.playerCountLabel}>Players</Text>
        </View>
      </View>

      {/* Top Players */}
      <View style={styles.playersContainer}>
        {topPlayers.map((entry, index) => (
          <PlayerRow key={entry.playerId} entry={entry} index={index} />
        ))}
      </View>

      {/* Current Player Position (if not in top players) */}
      {currentPlayerEntry && !topPlayers.some(p => p.playerId === currentPlayerId) && (
        <View style={styles.currentPlayerContainer}>
          <View style={styles.separator} />
          <PlayerRow entry={currentPlayerEntry} index={-1} />
        </View>
      )}

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.timeframe}>
          {leaderboard.timeframe.replace('_', ' ').toUpperCase()}
        </Text>
        <Text style={styles.lastUpdated}>
          Updated {new Date(leaderboard.lastUpdated).toLocaleDateString()}
        </Text>
        {onPress && (
          <Ionicons name="chevron-forward" size={16} color="#00D4FF" />
        )}
      </View>
    </BlurView>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8} style={styles.touchable}>
        <CardContent />
      </TouchableOpacity>
    );
  }

  return <CardContent />;
}

const styles = StyleSheet.create({
  touchable: {
    marginBottom: 16,
  },
  container: {
    borderRadius: 16,
    padding: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#CCCCCC',
    lineHeight: 20,
  },
  headerStats: {
    alignItems: 'center',
    marginLeft: 16,
  },
  playerCount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#00D4FF',
  },
  playerCountLabel: {
    fontSize: 12,
    color: '#CCCCCC',
    marginTop: 2,
  },
  playersContainer: {
    marginBottom: 12,
  },
  playerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  currentPlayerRow: {
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  rankContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 60,
  },
  rankBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  rankText: {
    fontSize: 12,
    color: '#CCCCCC',
    minWidth: 20,
  },
  playerInfo: {
    flex: 1,
    marginLeft: 8,
  },
  playerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  currentPlayerName: {
    color: '#00D4FF',
  },
  playerScore: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'right',
    minWidth: 60,
  },
  currentPlayerContainer: {
    marginTop: 8,
  },
  separator: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  timeframe: {
    fontSize: 12,
    color: '#00D4FF',
    fontWeight: '600',
  },
  lastUpdated: {
    fontSize: 12,
    color: '#888888',
    flex: 1,
    textAlign: 'center',
  },
});
