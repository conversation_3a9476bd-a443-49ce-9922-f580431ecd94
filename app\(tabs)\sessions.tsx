import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { SessionCard, ActiveSessionIndicator } from '../../components/sessions';
import { Modal } from '../../components/ui';
import useRealTimeSession from '../../hooks/useRealTimeSession';
import { GameSession, SessionStatus } from '../../services/sessions/SessionManager';

export default function SessionsScreen() {
  const userProfile = useSelector((state: RootState) => (state as any).user?.profile);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'waiting' | 'completed'>('all');

  const {
    currentSession,
    activeSessions,
    userSessions,
    sessionStats,
    loading,
    error,
    isInitialized,
    initialize,
    createNewSession,
    joinExistingSession,
    leaveCurrentSession,
    startCurrentSession,
    endCurrentSession,
    refreshSessions,
    clearSessionError,
    isHost,
    canJoinSession,
    canStartSession,
  } = useRealTimeSession(userProfile?.id);

  useEffect(() => {
    if (userProfile?.id && !isInitialized) {
      initialize(userProfile.id);
    }
  }, [userProfile?.id, isInitialized, initialize]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshSessions();
    } catch (error) {
      console.error('Failed to refresh sessions:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreateSession = () => {
    setShowCreateModal(true);
  };

  const handleJoinSession = async (session: GameSession) => {
    try {
      await joinExistingSession(session.id);
      Alert.alert('Success', `Joined ${session.gameName} session!`);
    } catch (error) {
      Alert.alert('Error', 'Failed to join session');
    }
  };

  const handleLeaveSession = async (session: GameSession) => {
    Alert.alert(
      'Leave Session',
      `Are you sure you want to leave ${session.gameName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              await leaveCurrentSession(session.id);
              Alert.alert('Left Session', `You left ${session.gameName}`);
            } catch (error) {
              Alert.alert('Error', 'Failed to leave session');
            }
          },
        },
      ]
    );
  };

  const handleStartSession = async (session: GameSession) => {
    try {
      await startCurrentSession(session.id);
      Alert.alert('Session Started', `${session.gameName} session has started!`);
    } catch (error) {
      Alert.alert('Error', 'Failed to start session');
    }
  };

  const handleEndSession = async (session: GameSession) => {
    Alert.alert(
      'End Session',
      `Are you sure you want to end ${session.gameName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'End Session',
          style: 'destructive',
          onPress: async () => {
            try {
              await endCurrentSession(session.id, 'completed');
              Alert.alert('Session Ended', `${session.gameName} session has ended`);
            } catch (error) {
              Alert.alert('Error', 'Failed to end session');
            }
          },
        },
      ]
    );
  };

  const getFilteredSessions = () => {
    switch (selectedFilter) {
      case 'active':
        return activeSessions.filter(s => s.status === SessionStatus.ACTIVE);
      case 'waiting':
        return activeSessions.filter(s => s.status === SessionStatus.WAITING);
      case 'completed':
        return userSessions.filter(s => s.status === SessionStatus.COMPLETED);
      default:
        return activeSessions;
    }
  };

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      {[
        { key: 'all', label: 'All', count: activeSessions.length },
        { key: 'active', label: 'Active', count: activeSessions.filter(s => s.status === SessionStatus.ACTIVE).length },
        { key: 'waiting', label: 'Waiting', count: activeSessions.filter(s => s.status === SessionStatus.WAITING).length },
        { key: 'completed', label: 'History', count: sessionStats?.completedSessions || 0 },
      ].map((filter) => (
        <TouchableOpacity
          key={filter.key}
          style={[
            styles.filterTab,
            selectedFilter === filter.key && styles.filterTabActive,
          ]}
          onPress={() => setSelectedFilter(filter.key as any)}
        >
          <Text
            style={[
              styles.filterTabText,
              selectedFilter === filter.key && styles.filterTabTextActive,
            ]}
          >
            {filter.label}
          </Text>
          {filter.count > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>{filter.count}</Text>
            </View>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSessionStats = () => {
    if (!sessionStats) return null;

    return (
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{sessionStats.totalSessions}</Text>
          <Text style={styles.statLabel}>Total Sessions</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{sessionStats.activeSessions}</Text>
          <Text style={styles.statLabel}>Active Now</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {Math.round(sessionStats.averageSessionDuration / 60000)}m
          </Text>
          <Text style={styles.statLabel}>Avg Duration</Text>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people-outline" size={64} color="rgba(255, 255, 255, 0.3)" />
      <Text style={styles.emptyTitle}>No Sessions Found</Text>
      <Text style={styles.emptySubtitle}>
        {selectedFilter === 'all'
          ? 'Create a session or join one from the Games tab'
          : `No ${selectedFilter} sessions available`}
      </Text>
      {selectedFilter === 'all' && (
        <TouchableOpacity style={styles.createButton} onPress={handleCreateSession}>
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={styles.createButtonText}>Create Session</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const filteredSessions = getFilteredSessions();

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>Sessions</Text>
            <Text style={styles.subtitle}>
              Manage your gaming sessions
            </Text>
          </View>
          <TouchableOpacity
            style={styles.createSessionButton}
            onPress={handleCreateSession}
          >
            <Ionicons name="add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Active Session Indicator */}
        {currentSession && (
          <ActiveSessionIndicator
            onPress={() => {
              // Navigate to session details
              console.log('Navigate to session details');
            }}
            position="top"
          />
        )}

        {/* Session Stats */}
        {renderSessionStats()}

        {/* Filter Tabs */}
        {renderFilterTabs()}

        {/* Error Display */}
        {error && (
          <View style={styles.errorContainer}>
            <Ionicons name="warning" size={20} color="#FF4757" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={clearSessionError}>
              <Ionicons name="close" size={20} color="#FF4757" />
            </TouchableOpacity>
          </View>
        )}

        {/* Sessions List */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor="#00D4FF"
              colors={["#00D4FF"]}
            />
          }
        >
          {filteredSessions.length === 0 ? (
            renderEmptyState()
          ) : (
            filteredSessions.map((session) => (
              <SessionCard
                key={session.id}
                session={session}
                currentUserId={userProfile?.id}
                variant="detailed"
                onPress={() => {
                  // Navigate to session details
                  console.log('Session pressed:', session.id);
                }}
                onJoin={() => handleJoinSession(session)}
                onLeave={() => handleLeaveSession(session)}
                onStart={() => handleStartSession(session)}
                onEnd={() => handleEndSession(session)}
              />
            ))
          )}
        </ScrollView>
      </SafeAreaView>

      {/* Create Session Modal */}
      <Modal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        variant="fullscreen"
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Create Session</Text>
          <Text style={styles.modalSubtitle}>
            Go to the Games tab to create a session for a specific game
          </Text>
          <TouchableOpacity
            style={styles.modalButton}
            onPress={() => {
              setShowCreateModal(false);
              // Navigate to games tab
            }}
          >
            <Text style={styles.modalButtonText}>Go to Games</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  createSessionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#00D4FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#00D4FF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 8,
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    gap: 6,
  },
  filterTabActive: {
    backgroundColor: '#00D4FF',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  filterTabTextActive: {
    color: '#FFFFFF',
  },
  filterBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 71, 87, 0.1)',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 71, 87, 0.3)',
    gap: 8,
  },
  errorText: {
    flex: 1,
    fontSize: 14,
    color: '#FF4757',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#00D4FF',
    borderRadius: 24,
    gap: 8,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  modalContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  modalSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  modalButton: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    backgroundColor: '#00D4FF',
    borderRadius: 24,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
