import React, { createContext, useContext, useState, ReactNode } from 'react';
import ConfirmationModal from './ConfirmationModal';
import InfoModal from './InfoModal';
import ActionModal, { ActionModalAction } from './ActionModal';
import InputModal from './InputModal';
import { Ionicons } from '@expo/vector-icons';

interface ConfirmationOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: string;
  cancelColor?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  destructive?: boolean;
}

interface InfoOptions {
  title: string;
  message?: string;
  content?: ReactNode;
  buttonText?: string;
  buttonColor?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  scrollable?: boolean;
}

interface ActionOptions {
  title: string;
  message?: string;
  actions: ActionModalAction[];
  cancelText?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
}

interface InputOptions {
  title: string;
  message?: string;
  placeholder?: string;
  defaultValue?: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  validation?: (value: string) => string | null;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  maxLength?: number;
  multiline?: boolean;
  autoFocus?: boolean;
}

interface ModalContextType {
  showConfirmation: (options: ConfirmationOptions) => Promise<boolean>;
  showInfo: (options: InfoOptions) => Promise<void>;
  showActions: (options: ActionOptions) => Promise<void>;
  showInput: (options: InputOptions) => Promise<string | null>;
  hideAll: () => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

interface ModalState {
  type: 'confirmation' | 'info' | 'action' | 'input' | null;
  options: any;
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
}

interface ModalProviderProps {
  children: ReactNode;
}

export function ModalProvider({ children }: ModalProviderProps) {
  const [modalState, setModalState] = useState<ModalState | null>(null);

  const showModal = <T,>(
    type: ModalState['type'],
    options: any
  ): Promise<T> => {
    return new Promise((resolve, reject) => {
      setModalState({
        type,
        options,
        resolve,
        reject,
      });
    });
  };

  const hideModal = () => {
    setModalState(null);
  };

  const showConfirmation = (options: ConfirmationOptions): Promise<boolean> => {
    return showModal<boolean>('confirmation', options);
  };

  const showInfo = (options: InfoOptions): Promise<void> => {
    return showModal<void>('info', options);
  };

  const showActions = (options: ActionOptions): Promise<void> => {
    return showModal<void>('action', options);
  };

  const showInput = (options: InputOptions): Promise<string | null> => {
    return showModal<string | null>('input', options);
  };

  const hideAll = () => {
    if (modalState) {
      modalState.reject(new Error('Modal cancelled'));
      hideModal();
    }
  };

  const handleConfirmationResult = (confirmed: boolean) => {
    if (modalState) {
      modalState.resolve(confirmed);
      hideModal();
    }
  };

  const handleInfoClose = () => {
    if (modalState) {
      modalState.resolve(undefined);
      hideModal();
    }
  };

  const handleActionClose = () => {
    if (modalState) {
      modalState.resolve(undefined);
      hideModal();
    }
  };

  const handleInputResult = (value: string | null) => {
    if (modalState) {
      modalState.resolve(value);
      hideModal();
    }
  };

  const contextValue: ModalContextType = {
    showConfirmation,
    showInfo,
    showActions,
    showInput,
    hideAll,
  };

  return (
    <ModalContext.Provider value={contextValue}>
      {children}
      
      {/* Confirmation Modal */}
      {modalState?.type === 'confirmation' && (
        <ConfirmationModal
          visible={true}
          {...modalState.options}
          onConfirm={() => handleConfirmationResult(true)}
          onCancel={() => handleConfirmationResult(false)}
        />
      )}

      {/* Info Modal */}
      {modalState?.type === 'info' && (
        <InfoModal
          visible={true}
          {...modalState.options}
          onClose={handleInfoClose}
        />
      )}

      {/* Action Modal */}
      {modalState?.type === 'action' && (
        <ActionModal
          visible={true}
          {...modalState.options}
          onCancel={handleActionClose}
        />
      )}

      {/* Input Modal */}
      {modalState?.type === 'input' && (
        <InputModal
          visible={true}
          {...modalState.options}
          onConfirm={(value: string) => handleInputResult(value)}
          onCancel={() => handleInputResult(null)}
        />
      )}
    </ModalContext.Provider>
  );
}

export function useModal(): ModalContextType {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
}

// Convenience hooks for specific modal types
export function useConfirmation() {
  const { showConfirmation } = useModal();
  return showConfirmation;
}

export function useInfo() {
  const { showInfo } = useModal();
  return showInfo;
}

export function useActions() {
  const { showActions } = useModal();
  return showActions;
}

export function useInput() {
  const { showInput } = useModal();
  return showInput;
}
