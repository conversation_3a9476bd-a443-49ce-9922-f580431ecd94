package com.logaco.networking;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.net.VpnService;
import android.os.Build;
import android.os.ParcelFileDescriptor;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.DatagramChannel;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

public class LoGaCoVPNService extends VpnService implements Runnable {
    private static final String TAG = "LoGaCoVPNService";
    private static final String CHANNEL_ID = "VPN_SERVICE_CHANNEL";
    private static final int NOTIFICATION_ID = 1001;
    
    private Thread vpnThread;
    private ParcelFileDescriptor vpnInterface;
    private AtomicBoolean isRunning = new AtomicBoolean(false);
    private AtomicReference<ReactApplicationContext> reactContext = new AtomicReference<>();
    
    // Packet processing
    private FileInputStream inputStream;
    private FileOutputStream outputStream;
    private DatagramChannel tunnelChannel;
    
    // Traffic statistics
    private long bytesReceived = 0;
    private long bytesSent = 0;
    private long packetsProcessed = 0;
    
    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        Log.d(TAG, "VPN Service created");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            if ("START_VPN".equals(action)) {
                startVPN();
            } else if ("STOP_VPN".equals(action)) {
                stopVPN();
            }
        }
        return START_STICKY;
    }
    
    private void startVPN() {
        if (isRunning.get()) {
            Log.w(TAG, "VPN already running");
            return;
        }
        
        try {
            // Create VPN interface
            VpnService.Builder builder = new VpnService.Builder()
                .setMtu(1500)
                .addAddress("********", 24)
                .addRoute("0.0.0.0", 0)
                .addDnsServer("*******")
                .addDnsServer("*******")
                .setSession("LoGaCo Gaming VPN")
                .setConfigureIntent(createConfigIntent());
            
            // Allow specific apps to bypass VPN if needed
            try {
                builder.addDisallowedApplication(getPackageName());
            } catch (Exception e) {
                Log.w(TAG, "Could not disallow own package", e);
            }
            
            vpnInterface = builder.establish();
            
            if (vpnInterface == null) {
                Log.e(TAG, "Failed to establish VPN interface");
                sendEvent("VPNError", "Failed to establish VPN interface");
                return;
            }
            
            // Setup I/O streams
            inputStream = new FileInputStream(vpnInterface.getFileDescriptor());
            outputStream = new FileOutputStream(vpnInterface.getFileDescriptor());
            
            // Setup tunnel channel
            tunnelChannel = DatagramChannel.open();
            tunnelChannel.connect(new InetSocketAddress("127.0.0.1", 8080));
            tunnelChannel.configureBlocking(false);
            
            // Start VPN thread
            isRunning.set(true);
            vpnThread = new Thread(this, "VPNThread");
            vpnThread.start();
            
            // Start foreground service
            startForeground(NOTIFICATION_ID, createNotification());
            
            Log.i(TAG, "VPN started successfully");
            sendEvent("VPNStarted", "VPN service started");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to start VPN", e);
            sendEvent("VPNError", "Failed to start VPN: " + e.getMessage());
            stopVPN();
        }
    }
    
    private void stopVPN() {
        isRunning.set(false);
        
        try {
            if (vpnThread != null) {
                vpnThread.interrupt();
                vpnThread.join(1000);
                vpnThread = null;
            }
            
            if (tunnelChannel != null) {
                tunnelChannel.close();
                tunnelChannel = null;
            }
            
            if (inputStream != null) {
                inputStream.close();
                inputStream = null;
            }
            
            if (outputStream != null) {
                outputStream.close();
                outputStream = null;
            }
            
            if (vpnInterface != null) {
                vpnInterface.close();
                vpnInterface = null;
            }
            
            stopForeground(true);
            stopSelf();
            
            Log.i(TAG, "VPN stopped");
            sendEvent("VPNStopped", "VPN service stopped");
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping VPN", e);
        }
    }
    
    @Override
    public void run() {
        Log.d(TAG, "VPN thread started");
        
        ByteBuffer packet = ByteBuffer.allocate(32767);
        
        while (isRunning.get() && !Thread.currentThread().isInterrupted()) {
            try {
                // Read packet from VPN interface
                packet.clear();
                int length = inputStream.read(packet.array());
                
                if (length > 0) {
                    packet.limit(length);
                    processPacket(packet);
                    packetsProcessed++;
                    bytesReceived += length;
                }
                
                // Small delay to prevent busy waiting
                Thread.sleep(1);
                
            } catch (InterruptedException e) {
                Log.d(TAG, "VPN thread interrupted");
                break;
            } catch (IOException e) {
                Log.e(TAG, "Error reading from VPN interface", e);
                if (isRunning.get()) {
                    sendEvent("VPNError", "I/O error: " + e.getMessage());
                }
                break;
            } catch (Exception e) {
                Log.e(TAG, "Unexpected error in VPN thread", e);
                if (isRunning.get()) {
                    sendEvent("VPNError", "Unexpected error: " + e.getMessage());
                }
                break;
            }
        }
        
        Log.d(TAG, "VPN thread ended");
    }
    
    private void processPacket(ByteBuffer packet) {
        try {
            // Parse IP header
            packet.rewind();
            byte versionAndIHL = packet.get();
            int version = (versionAndIHL >> 4) & 0x0F;
            int headerLength = (versionAndIHL & 0x0F) * 4;
            
            if (version != 4) {
                // Only handle IPv4 for now
                forwardPacket(packet);
                return;
            }
            
            // Skip to protocol field
            packet.position(9);
            byte protocol = packet.get();
            
            // Skip to source and destination IP
            packet.position(12);
            byte[] sourceIP = new byte[4];
            byte[] destIP = new byte[4];
            packet.get(sourceIP);
            packet.get(destIP);
            
            // Check if this is gaming traffic
            if (isGamingTraffic(sourceIP, destIP, protocol)) {
                handleGamingPacket(packet, sourceIP, destIP, protocol);
            } else {
                forwardPacket(packet);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing packet", e);
            forwardPacket(packet);
        }
    }
    
    private boolean isGamingTraffic(byte[] sourceIP, byte[] destIP, byte protocol) {
        // Check for known gaming servers and ports
        String destAddress = String.format("%d.%d.%d.%d", 
            destIP[0] & 0xFF, destIP[1] & 0xFF, destIP[2] & 0xFF, destIP[3] & 0xFF);
        
        // Check for Minecraft servers (port 25565)
        if (protocol == 6) { // TCP
            // Would need to parse TCP header to get port
            return destAddress.contains("mojang") || destAddress.contains("minecraft");
        }
        
        // Check for Among Us servers
        if (protocol == 17) { // UDP
            return destAddress.contains("innersloth");
        }
        
        return false;
    }
    
    private void handleGamingPacket(ByteBuffer packet, byte[] sourceIP, byte[] destIP, byte protocol) {
        try {
            // Send packet info to React Native
            WritableMap packetInfo = Arguments.createMap();
            packetInfo.putString("sourceIP", ipToString(sourceIP));
            packetInfo.putString("destIP", ipToString(destIP));
            packetInfo.putInt("protocol", protocol);
            packetInfo.putInt("size", packet.remaining());
            packetInfo.putDouble("timestamp", System.currentTimeMillis());
            
            sendEvent("GamingPacketDetected", packetInfo);
            
            // For now, forward the packet normally
            // In a full implementation, this would redirect to local peers
            forwardPacket(packet);
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling gaming packet", e);
            forwardPacket(packet);
        }
    }
    
    private void forwardPacket(ByteBuffer packet) {
        try {
            packet.rewind();
            outputStream.write(packet.array(), 0, packet.remaining());
            bytesSent += packet.remaining();
        } catch (IOException e) {
            Log.e(TAG, "Error forwarding packet", e);
        }
    }
    
    private String ipToString(byte[] ip) {
        return String.format("%d.%d.%d.%d", 
            ip[0] & 0xFF, ip[1] & 0xFF, ip[2] & 0xFF, ip[3] & 0xFF);
    }
    
    private PendingIntent createConfigIntent() {
        Intent intent = new Intent(this, getClass());
        return PendingIntent.getActivity(this, 0, intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("LoGaCo Gaming VPN Service");
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    private Notification createNotification() {
        Intent stopIntent = new Intent(this, LoGaCoVPNService.class);
        stopIntent.setAction("STOP_VPN");
        PendingIntent stopPendingIntent = PendingIntent.getService(this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("LoGaCo Gaming VPN")
            .setContentText("Routing gaming traffic through local network")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .addAction(android.R.drawable.ic_media_pause, "Stop", stopPendingIntent)
            .setOngoing(true)
            .build();
    }
    
    private void sendEvent(String eventName, Object data) {
        ReactApplicationContext context = reactContext.get();
        if (context != null && context.hasActiveCatalystInstance()) {
            try {
                context
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit(eventName, data);
            } catch (Exception e) {
                Log.e(TAG, "Error sending event to React Native", e);
            }
        }
    }
    
    public void setReactContext(ReactApplicationContext context) {
        reactContext.set(context);
    }
    
    public boolean isVPNRunning() {
        return isRunning.get();
    }
    
    public long getBytesReceived() {
        return bytesReceived;
    }
    
    public long getBytesSent() {
        return bytesSent;
    }
    
    public long getPacketsProcessed() {
        return packetsProcessed;
    }
    
    @Override
    public void onDestroy() {
        stopVPN();
        super.onDestroy();
        Log.d(TAG, "VPN Service destroyed");
    }
}
